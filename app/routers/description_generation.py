"""
工具描述生成API路由

提供REST API接口来执行工具描述的自动生成，包括：
- 批量生成描述接口
- 生成状态查询接口
- 可用模型列表接口
- 服务健康检查接口
"""

import logging
from typing import Dict, Any, Optional, List
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field

from app.core.database import get_db
from app.services.description_generation_service import DescriptionGenerationService

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/description-generation", tags=["Description Generation"])


# Pydantic模型定义
class GenerationRequest(BaseModel):
    """生成请求模型"""
    locale: str = Field(default="en", description="语言代码")
    model_name: str = Field(default="gemma3:latest", description="使用的Ollama模型名称")
    fallback_model: str = Field(default="deepseek-r1:latest", description="备用模型名称")
    fields: str = Field(
        default="long_description,usage_instructions,pricing_details,integration_info",
        description="要生成的字段，用逗号分隔"
    )
    limit: int = Field(default=10, ge=1, le=100, description="处理的工具数量上限")
    max_chunks: int = Field(default=5, ge=1, le=10, description="最大文本块数量")
    chunk_size: int = Field(default=2000, ge=500, le=8000, description="文本块大小")
    max_tokens: int = Field(default=2048, ge=256, le=4096, description="最大令牌数")
    temperature: float = Field(default=0.7, ge=0.0, le=1.0, description="生成温度")
    dry_run: bool = Field(default=False, description="是否为测试模式")
    ip_address: Optional[str] = Field(default=None, description="Ollama服务器IP地址")
    port: int = Field(default=11434, description="Ollama服务器端口")


class GenerationResponse(BaseModel):
    """生成响应模型"""
    success: bool
    message: str
    processed: int = 0
    succeeded: int = 0
    failed: int = 0
    errors: List[str] = []
    tools: Optional[List[Dict[str, str]]] = None


class StatusResponse(BaseModel):
    """状态响应模型"""
    success: bool
    total_tools: int = 0
    locale: str = ""
    field_statistics: Dict[str, Dict[str, Any]] = {}
    ollama_service_available: bool = False
    error: Optional[str] = None


class ModelInfo(BaseModel):
    """模型信息模型"""
    name: str
    size: Optional[str] = None
    modified_at: Optional[str] = None


class ModelsResponse(BaseModel):
    """模型列表响应模型"""
    success: bool
    models: List[ModelInfo] = []
    error: Optional[str] = None


# 依赖函数
def get_description_service(db: Session = Depends(get_db)) -> DescriptionGenerationService:
    """获取描述生成服务实例"""
    return DescriptionGenerationService(db)


# API端点
@router.post("/generate", response_model=GenerationResponse)
async def generate_descriptions(
    request: GenerationRequest,
    background_tasks: BackgroundTasks,
    service: DescriptionGenerationService = Depends(get_description_service)
):
    """
    批量生成工具描述
    
    支持同步和异步两种模式：
    - 同步模式：直接返回结果
    - 异步模式：后台执行，立即返回任务ID（未实现）
    """
    try:
        logger.info(f"收到描述生成请求: locale={request.locale}, limit={request.limit}")
        
        # 初始化服务
        success = service.initialize_services(request.ip_address, request.port)
        if not success:
            raise HTTPException(
                status_code=503,
                detail="无法连接到Ollama服务，请检查服务是否启动"
            )
        
        # 执行生成
        result = service.generate_descriptions(
            locale=request.locale,
            model_name=request.model_name,
            fallback_model=request.fallback_model,
            fields=request.fields,
            limit=request.limit,
            max_chunks=request.max_chunks,
            chunk_size=request.chunk_size,
            max_tokens=request.max_tokens,
            temperature=request.temperature,
            dry_run=request.dry_run
        )
        
        if not result["success"]:
            raise HTTPException(
                status_code=500,
                detail=result.get("error", "生成过程中发生未知错误")
            )
        
        return GenerationResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"生成描述时发生错误: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"服务器内部错误: {str(e)}"
        )


@router.get("/status", response_model=StatusResponse)
async def get_generation_status(
    locale: str = "en",
    service: DescriptionGenerationService = Depends(get_description_service)
):
    """
    获取生成状态统计
    
    返回指定语言的工具描述生成完成情况统计
    """
    try:
        logger.info(f"获取生成状态: locale={locale}")
        
        # 初始化服务（仅用于检查Ollama服务状态）
        service.initialize_services()
        
        # 获取状态
        status = service.get_generation_status(locale)
        
        if not status["success"]:
            return StatusResponse(
                success=False,
                error=status.get("error", "获取状态失败")
            )
        
        return StatusResponse(**status)
        
    except Exception as e:
        logger.error(f"获取生成状态时发生错误: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"服务器内部错误: {str(e)}"
        )


@router.get("/models", response_model=ModelsResponse)
async def list_available_models(
    ip_address: Optional[str] = None,
    port: int = 11434,
    service: DescriptionGenerationService = Depends(get_description_service)
):
    """
    获取可用的Ollama模型列表
    
    返回当前Ollama服务器上可用的所有模型
    """
    try:
        logger.info("获取可用模型列表")
        
        # 初始化服务
        success = service.initialize_services(ip_address, port)
        if not success:
            return ModelsResponse(
                success=False,
                error="无法连接到Ollama服务"
            )
        
        # 获取模型列表
        models_data = service.get_available_models()
        
        # 转换为响应格式
        models = []
        for model_data in models_data:
            models.append(ModelInfo(
                name=model_data.get("name", "Unknown"),
                size=model_data.get("size"),
                modified_at=model_data.get("modified_at")
            ))
        
        return ModelsResponse(
            success=True,
            models=models
        )
        
    except Exception as e:
        logger.error(f"获取模型列表时发生错误: {str(e)}")
        return ModelsResponse(
            success=False,
            error=str(e)
        )


@router.get("/health")
async def health_check(
    ip_address: Optional[str] = None,
    port: int = 11434,
    service: DescriptionGenerationService = Depends(get_description_service)
):
    """
    健康检查接口
    
    检查描述生成服务和Ollama服务的可用性
    """
    try:
        # 检查数据库连接
        db_status = "connected"
        try:
            # 简单查询测试数据库连接
            service.db.execute("SELECT 1")
        except Exception as e:
            db_status = f"error: {str(e)}"
        
        # 检查Ollama服务
        ollama_status = "disconnected"
        try:
            success = service.initialize_services(ip_address, port)
            if success:
                ollama_status = f"connected: {service.ollama_service.ip_address}:{service.ollama_service.port}"
            else:
                ollama_status = "connection_failed"
        except Exception as e:
            ollama_status = f"error: {str(e)}"
        
        return {
            "status": "healthy" if db_status == "connected" and "connected" in ollama_status else "unhealthy",
            "database": db_status,
            "ollama_service": ollama_status,
            "timestamp": "2024-01-01T00:00:00Z"  # 可以使用实际时间戳
        }
        
    except Exception as e:
        logger.error(f"健康检查时发生错误: {str(e)}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": "2024-01-01T00:00:00Z"
        }


@router.post("/generate-single")
async def generate_single_tool_description(
    tool_id: str,
    field_type: str,
    model_name: str = "llama3:latest",
    ip_address: Optional[str] = None,
    port: int = 11434,
    service: DescriptionGenerationService = Depends(get_description_service)
):
    """
    为单个工具生成指定字段的描述
    
    用于测试或单独处理特定工具
    """
    try:
        logger.info(f"为工具 {tool_id} 生成 {field_type} 字段")
        
        # 初始化服务
        success = service.initialize_services(ip_address, port)
        if not success:
            raise HTTPException(
                status_code=503,
                detail="无法连接到Ollama服务"
            )
        
        # 查找工具
        from app.models.tool import Tool, ToolTranslation
        tool = service.db.query(Tool).filter(Tool.tool_id == tool_id).first()
        if not tool:
            raise HTTPException(
                status_code=404,
                detail=f"未找到工具: {tool_id}"
            )
        
        # 生成内容
        content = service.content_generation_service.generate_tool_content(
            tool=tool,
            field_type=field_type,
            model_name=model_name
        )
        
        if content:
            return {
                "success": True,
                "tool_id": tool_id,
                "field_type": field_type,
                "content": content,
                "message": "生成成功"
            }
        else:
            return {
                "success": False,
                "tool_id": tool_id,
                "field_type": field_type,
                "message": "生成失败，请检查日志"
            }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"为工具 {tool_id} 生成 {field_type} 时发生错误: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"生成失败: {str(e)}"
        )
