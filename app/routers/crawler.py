from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from sqlalchemy.orm import Session
from typing import Dict, Any, List
import logging
import traceback

from app.core.database import get_db
from app.schemas.tool import CrawlerRequest, BatchCrawlerRequest, CrawlerResponse, ToolifyDataResponse
from app.services.toolify_service import ToolifyAPIService
from app.services.database_service import DatabaseService
from app.utils.data_analyzer import ToolifyDataAnaly<PERSON>, analyze_toolify_data

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/crawler", tags=["crawler"])

@router.get("/toolify")
async def crawl_toolify_single_page(
    page: int = Query(1, ge=1, description="页码，从1开始"),
    per_page: int = Query(28, ge=1, le=100, description="每页记录数"),
    tool_type: int = Query(1, description="工具类型"),
    db: Session = Depends(get_db)
):
    """
    单页爬取Toolify.ai的数据
    """
    try:
        logger.info(f"开始单页爬取: page={page}, per_page={per_page}, tool_type={tool_type}")
        
        # 创建API服务实例
        service = ToolifyAPIService()
        
        # 获取数据
        result = service.fetch_tools(page=page, per_page=per_page, tool_type=tool_type)
        
        if not result['success']:
            raise HTTPException(status_code=400, detail=result['message'])
        
        # 返回原始形式的数据
        return result['data']
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"单页爬取数据出错: {str(e)}")
        logger.error(f"异常详情: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"服务器错误: {str(e)}")

@router.post("/toolify/batch", response_model=CrawlerResponse)
async def batch_crawl_tools(
    request: BatchCrawlerRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    批量爬取多页数据并可选择存储到数据库
    """
    try:
        logger.info("=" * 80)
        logger.info("🚀 [批量爬取] 开始批量爬取工具数据")
        logger.info("=" * 80)
        logger.info(f"📋 [请求参数] 完整请求: {request.model_dump()}")
        logger.info(f"📋 [请求参数] 起始页码: {request.start_page}")
        logger.info(f"📋 [请求参数] 最大页数: {request.max_pages}")
        logger.info(f"📋 [请求参数] 每页记录数: {request.per_page}")
        logger.info(f"📋 [请求参数] 工具类型: {request.tool_type}")
        logger.info(f"📋 [请求参数] 是否保存到数据库: {request.save_to_db}")

        # 创建API服务实例
        service = ToolifyAPIService()

        # 获取多页数据
        logger.info("🌐 [数据采集] 开始获取工具数据...")
        tools = service.fetch_all_pages(
            start_page=request.start_page,
            max_pages=request.max_pages,
            per_page=request.per_page,
            tool_type=request.tool_type
        )
        logger.info(f"✅ [数据采集] 成功获取{len(tools)}条工具数据")
        
        result = CrawlerResponse(
            success=True,
            message=f"成功爬取{len(tools)}条工具数据",
            total=len(tools),
            tools=tools if not request.save_to_db else None
        )
        
        # 如果需要存入数据库
        if request.save_to_db and tools:
            logger.info("💾 [数据库保存] 开始将数据保存到数据库...")

            # 记录数据库表的当前状态
            db_service = DatabaseService(db)
            initial_count = db_service.get_tools_count()
            initial_categories_count = db_service.get_categories_count()
            logger.info(f"📊 [数据库状态] 保存前工具数量: {initial_count}")
            logger.info(f"📊 [数据库状态] 保存前分类数量: {initial_categories_count}")

            # 转换数据格式
            logger.info(f"🔄 [数据转换] 开始转换{len(tools)}条工具数据...")
            transformed_tools = []
            for i, tool in enumerate(tools, 1):
                logger.info(f"🔄 [数据转换] 转换第{i}/{len(tools)}条工具")
                transformed_tool = service.transform_tool_data(tool)
                transformed_tools.append(transformed_tool)
            logger.info(f"✅ [数据转换] 完成{len(transformed_tools)}条工具数据转换")

            # 保存到数据库
            logger.info(f"💾 [数据库保存] 开始保存{len(transformed_tools)}条转换后的数据...")
            saved_count, errors = db_service.save_tools_to_database(transformed_tools)

            result.saved_count = saved_count
            if errors:
                logger.warning(f"⚠️ [保存警告] 保存过程中出现 {len(errors)} 个错误")
                for i, error in enumerate(errors[:5], 1):  # 只记录前5个错误
                    logger.warning(f"❌ [保存错误{i}] {error}")
                if len(errors) > 5:
                    logger.warning(f"⚠️ [保存警告] 还有 {len(errors) - 5} 个错误未显示")

            final_count = db_service.get_tools_count()
            final_categories_count = db_service.get_categories_count()
            logger.info(f"📊 [数据库状态] 保存后工具数量: {final_count} (增加: {final_count - initial_count})")
            logger.info(f"📊 [数据库状态] 保存后分类数量: {final_categories_count} (增加: {final_categories_count - initial_categories_count})")
            logger.info(f"✅ [数据库保存] 完成数据保存: 成功保存{saved_count}条记录")
        
        logger.info("=" * 80)
        logger.info(f"🎉 [批量爬取完成] 总结:")
        logger.info(f"📊 [最终结果] 爬取工具数: {len(tools)}")
        logger.info(f"📊 [最终结果] 保存成功数: {result.saved_count if result.saved_count else 0}")
        logger.info(f"📊 [最终结果] 是否保存到数据库: {request.save_to_db}")
        logger.info(f"📤 [响应数据] {result.model_dump()}")
        logger.info("=" * 80)

        return result
        
    except Exception as e:
        logger.error(f"批量爬取数据出错: {str(e)}")
        logger.error(f"异常详情: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"服务器错误: {str(e)}")

@router.post("/toolify/analyze")
async def analyze_toolify_data_endpoint(
    data: Dict[str, Any]
):
    """
    分析Toolify数据结构和内容
    """
    try:
        logger.info("开始分析Toolify数据...")
        
        # 使用数据分析器
        analysis_result = analyze_toolify_data(data)
        
        if 'error' in analysis_result:
            raise HTTPException(status_code=400, detail=analysis_result['error'])
        
        logger.info("数据分析完成")
        return {
            "success": True,
            "message": "数据分析完成",
            "analysis": analysis_result
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"分析数据出错: {str(e)}")
        logger.error(f"异常详情: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"服务器错误: {str(e)}")

@router.get("/status")
async def get_crawler_status(db: Session = Depends(get_db)):
    """
    获取爬虫状态和数据库统计信息
    """
    try:
        db_service = DatabaseService(db)
        
        tools_count = db_service.get_tools_count()
        categories_count = db_service.get_categories_count()
        
        return {
            "success": True,
            "message": "状态获取成功",
            "status": {
                "database": {
                    "tools_count": tools_count,
                    "categories_count": categories_count
                },
                "crawler": {
                    "service": "ToolifyAPIService",
                    "base_url": ToolifyAPIService.BASE_URL,
                    "status": "ready"
                }
            }
        }
        
    except Exception as e:
        logger.error(f"获取状态出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器错误: {str(e)}")

@router.post("/test")
async def test_crawler():
    """
    测试爬虫服务连接
    """
    try:
        service = ToolifyAPIService()
        
        # 测试获取第一页数据
        result = service.fetch_tools(page=1, per_page=5, tool_type=1)
        
        if result['success']:
            data = result['data']
            tools_count = len(data.get('data', []))
            
            return {
                "success": True,
                "message": "爬虫服务测试成功",
                "test_result": {
                    "tools_fetched": tools_count,
                    "total_categories": data.get('category_count', 0),
                    "api_response_keys": list(data.keys()) if data else []
                }
            }
        else:
            return {
                "success": False,
                "message": f"爬虫服务测试失败: {result['message']}"
            }
            
    except Exception as e:
        logger.error(f"测试爬虫服务出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器错误: {str(e)}")

def background_crawl_task(
    start_page: int,
    max_pages: int,
    per_page: int,
    tool_type: int,
    save_to_db: bool,
    db: Session
):
    """
    后台爬取任务
    """
    try:
        logger.info(f"开始后台爬取任务: start_page={start_page}, max_pages={max_pages}")
        
        service = ToolifyAPIService()
        tools = service.fetch_all_pages(
            start_page=start_page,
            max_pages=max_pages,
            per_page=per_page,
            tool_type=tool_type
        )
        
        if save_to_db and tools:
            db_service = DatabaseService(db)
            
            # 转换数据格式
            transformed_tools = []
            for tool in tools:
                transformed_tool = service.transform_tool_data(tool)
                transformed_tools.append(transformed_tool)
            
            # 保存到数据库
            saved_count, errors = db_service.save_tools_to_database(transformed_tools)
            logger.info(f"后台任务完成: 爬取{len(tools)}条，保存{saved_count}条")
        
    except Exception as e:
        logger.error(f"后台爬取任务出错: {str(e)}")

@router.post("/background-crawl")
async def start_background_crawl(
    request: BatchCrawlerRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    启动后台爬取任务
    """
    try:
        # 添加后台任务
        background_tasks.add_task(
            background_crawl_task,
            request.start_page,
            request.max_pages,
            request.per_page,
            request.tool_type,
            request.save_to_db,
            db
        )
        
        return {
            "success": True,
            "message": "后台爬取任务已启动",
            "task_params": request.model_dump()
        }
        
    except Exception as e:
        logger.error(f"启动后台任务出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器错误: {str(e)}")
