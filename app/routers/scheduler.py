from fastapi import APIRouter, HTTPException
from typing import Dict, Any, List
import logging

from app.services.scheduler_service import get_scheduler

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/scheduler", tags=["scheduler"])

@router.get("/status")
async def get_scheduler_status():
    """
    获取定时任务调度器状态
    """
    try:
        scheduler = get_scheduler()
        status = scheduler.get_status()
        
        return {
            "success": True,
            "message": "调度器状态获取成功",
            "status": status
        }
        
    except Exception as e:
        logger.error(f"获取调度器状态出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器错误: {str(e)}")

@router.get("/jobs")
async def get_scheduled_jobs():
    """
    获取所有定时任务信息
    """
    try:
        scheduler = get_scheduler()
        jobs = scheduler.get_jobs()
        
        return {
            "success": True,
            "message": "任务列表获取成功",
            "jobs": jobs,
            "total": len(jobs)
        }
        
    except Exception as e:
        logger.error(f"获取任务列表出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器错误: {str(e)}")

@router.post("/start")
async def start_scheduler():
    """
    启动定时任务调度器
    """
    try:
        scheduler = get_scheduler()
        
        if scheduler.is_running:
            return {
                "success": True,
                "message": "调度器已经在运行中"
            }
        
        scheduler.start()
        
        return {
            "success": True,
            "message": "调度器启动成功"
        }
        
    except Exception as e:
        logger.error(f"启动调度器出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器错误: {str(e)}")

@router.post("/stop")
async def stop_scheduler():
    """
    停止定时任务调度器
    """
    try:
        scheduler = get_scheduler()
        
        if not scheduler.is_running:
            return {
                "success": True,
                "message": "调度器已经停止"
            }
        
        scheduler.stop()
        
        return {
            "success": True,
            "message": "调度器停止成功"
        }
        
    except Exception as e:
        logger.error(f"停止调度器出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器错误: {str(e)}")

@router.post("/restart")
async def restart_scheduler():
    """
    重启定时任务调度器
    """
    try:
        scheduler = get_scheduler()
        
        # 先停止
        if scheduler.is_running:
            scheduler.stop()
            logger.info("调度器已停止")
        
        # 再启动
        scheduler.start()
        logger.info("调度器已重新启动")
        
        return {
            "success": True,
            "message": "调度器重启成功"
        }
        
    except Exception as e:
        logger.error(f"重启调度器出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器错误: {str(e)}")
