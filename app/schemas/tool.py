from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
from decimal import Decimal

class CategoryTranslationBase(BaseModel):
    locale: str
    name: str
    description: Optional[str] = None

class CategoryTranslationCreate(CategoryTranslationBase):
    pass

class CategoryTranslation(CategoryTranslationBase):
    id: int
    category_id: int
    created_at: datetime
    updated_at: datetime
    
    model_config = {"from_attributes": True}

class CategoryBase(BaseModel):
    slug: str
    icon_url: Optional[str] = None

class CategoryCreate(CategoryBase):
    translations: List[CategoryTranslationCreate] = []

class Category(CategoryBase):
    id: int
    created_at: datetime
    updated_at: datetime
    translations: List[CategoryTranslation] = []
    
    model_config = {"from_attributes": True}

class TagTranslationBase(BaseModel):
    locale: str
    name: str

class TagTranslation(TagTranslationBase):
    id: int
    tag_id: int
    created_at: datetime
    updated_at: datetime
    
    model_config = {"from_attributes": True}

class TagBase(BaseModel):
    slug: str

class Tag(TagBase):
    id: int
    created_at: datetime
    updated_at: datetime
    translations: List[TagTranslation] = []
    
    model_config = {"from_attributes": True}

class ToolTranslationBase(BaseModel):
    locale: str
    name: str
    description: str
    long_description: Optional[str] = None
    usage_instructions: Optional[str] = None
    pricing_details: Optional[str] = None
    integration_info: Optional[str] = None

class ToolTranslationCreate(ToolTranslationBase):
    pass

class ToolTranslation(ToolTranslationBase):
    id: int
    tool_id: str
    created_at: datetime
    updated_at: datetime
    
    model_config = {"from_attributes": True}

class ToolFeatureBase(BaseModel):
    locale: str
    feature: str

class ToolFeature(ToolFeatureBase):
    id: int
    tool_id: str
    created_at: datetime
    updated_at: datetime
    
    model_config = {"from_attributes": True}

class ToolBase(BaseModel):
    tool_id: str
    icon_url: str
    url: str
    pricing_type: str
    is_premium: bool = False
    is_new: bool = False
    is_featured: bool = False
    rating: Optional[Decimal] = None
    api_available: bool = False
    publisher: Optional[str] = None
    publisher_url: Optional[str] = None
    terms_url: Optional[str] = None
    privacy_url: Optional[str] = None

class ToolCreate(ToolBase):
    translations: List[ToolTranslationCreate] = []
    features: List[ToolFeatureBase] = []
    category_slugs: List[str] = []
    tag_slugs: List[str] = []

class Tool(ToolBase):
    id: int
    created_at: datetime
    updated_at: datetime
    translations: List[ToolTranslation] = []
    features: List[ToolFeature] = []
    categories: List[Category] = []
    tags: List[Tag] = []
    
    model_config = {"from_attributes": True}

# 爬虫相关的Schema
class CrawlerRequest(BaseModel):
    page: int = Field(default=1, ge=1, description="页码，从1开始")
    per_page: int = Field(default=28, ge=1, le=100, description="每页记录数")
    tool_type: int = Field(default=1, description="工具类型")

class BatchCrawlerRequest(BaseModel):
    start_page: int = Field(default=1, ge=1, description="起始页码")
    max_pages: int = Field(default=10, ge=1, le=50, description="最大页数")
    per_page: int = Field(default=28, ge=1, le=100, description="每页记录数")
    tool_type: int = Field(default=1, description="工具类型")
    save_to_db: bool = Field(default=False, description="是否保存到数据库")

class CrawlerResponse(BaseModel):
    success: bool
    message: str
    total: Optional[int] = None
    saved_count: Optional[int] = None
    tools: Optional[List[Dict[str, Any]]] = None

class ToolifyDataResponse(BaseModel):
    """Toolify API返回的原始数据结构"""
    data: List[Dict[str, Any]]
    category_count: Optional[int] = None
    total: Optional[int] = None
    is_logged_in: Optional[bool] = None

    # 允许额外字段
    model_config = {"extra": "allow"}
