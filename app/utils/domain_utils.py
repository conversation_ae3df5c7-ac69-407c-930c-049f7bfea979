import whois
import logging
import requests
import json
import time
import socket
import httpx
import asyncio
from datetime import datetime
from typing import Union, Dict, Any, List, Optional, Tuple
import subprocess
import re
from tenacity import retry, stop_after_attempt, wait_random_exponential, retry_if_exception

logger = logging.getLogger(__name__)

def _is_http_429_error(exception):
    """检查是否为HTTP 429错误（请求过于频繁）"""
    return (isinstance(exception, httpx.HTTPStatusError) and
            exception.response.status_code == 429)

def _should_retry_whosb_error(exception):
    """检查Who.sb API错误是否应该重试"""
    if isinstance(exception, httpx.HTTPStatusError):
        # 只对429（请求过于频繁）和502/503（临时服务器问题）重试
        # 不对500（内部服务器错误）重试，因为通常是域名不存在或不支持
        return exception.response.status_code in [429, 502, 503]
    return False

def _log_retry(retry_state):
    """记录重试信息"""
    logger.warning(f"🔄 [重试] 第 {retry_state.attempt_number} 次重试，等待 {retry_state.next_action.sleep} 秒")

def _format_datetime_str(datetime_str: Optional[str]) -> Optional[str]:
    """
    格式化日期时间字符串

    Args:
        datetime_str: 原始日期时间字符串

    Returns:
        Optional[str]: 格式化后的日期时间字符串
    """
    if not datetime_str:
        return None

    try:
        # 尝试解析并重新格式化日期
        parsed_date = DomainInfoUtils._parse_date_string(datetime_str)
        if parsed_date:
            return parsed_date.strftime('%Y-%m-%d %H:%M:%S')
        return datetime_str
    except Exception:
        return datetime_str

class DomainInfoUtils:
    """
    域名信息工具类，提供域名WHOIS信息查询功能
    """
    
    @staticmethod
    def get_domain_registration_date(domain: str) -> Union[datetime, str, None]:
        """
        获取域名的注册时间 - 使用多种方法提高成功率

        Args:
            domain (str): 要查询的域名，如 'example.com'

        Returns:
            datetime或str或None: 如果查询成功返回注册时间(datetime对象)，失败则返回错误信息或None
        """
        # 清理域名
        original_domain = domain
        domain = DomainInfoUtils._clean_domain(domain)

        if not domain:
            return "无效的域名格式"

        logger.info(f"🌐 [域名查询] 查询域名: {domain} (原始: {original_domain})")

        # 定义查询方法列表，按成功率优先级排序
        query_methods = [
            ("Who.sb API", DomainInfoUtils._query_with_whosb_api),  # 快速且准确，优先尝试
            ("系统whois命令", DomainInfoUtils._query_with_system_whois),  # 成功率较高，提升优先级
            ("备用WHOIS服务", DomainInfoUtils._query_with_backup_whois),  # 直接连接，较可靠
            ("Python-whois库", DomainInfoUtils._query_with_python_whois),  # 经常连接问题，降低优先级
            ("WHOIS API", DomainInfoUtils._query_with_whois_api),  # SSL问题较多，最后尝试
        ]

        last_error = None

        for method_name, method_func in query_methods:
            try:
                logger.info(f"🔍 [查询方法] 尝试: {method_name}")
                result = method_func(domain)

                if isinstance(result, datetime):
                    logger.info(f"✅ [查询成功] {method_name}: {result}")
                    return result
                elif result is None:
                    logger.debug(f"⚠️ [查询方法] {method_name} 未找到注册日期")
                else:
                    logger.debug(f"⚠️ [查询方法] {method_name} 失败: {str(result)[:100]}...")
                    last_error = result

            except Exception as e:
                error_msg = f"{method_name} 查询失败: {str(e)}"
                logger.warning(f"⚠️ [查询方法] {error_msg}")
                last_error = error_msg
                continue

        # 所有方法都失败
        final_error = last_error or f"所有查询方法都无法获取域名 {domain} 的注册信息"
        logger.error(f"❌ [域名查询] {final_error}")
        return final_error
    
    @staticmethod
    def get_domain_full_info(domain: str) -> Dict[str, Any]:
        """
        获取域名的完整WHOIS信息
        
        Args:
            domain (str): 要查询的域名，如 'example.com'
            
        Returns:
            dict: 包含域名信息的字典
        """
        try:
            # 清理域名，移除协议和路径
            if domain.startswith(('http://', 'https://')):
                domain = domain.split('://')[1]
            if '/' in domain:
                domain = domain.split('/')[0]
                
            logger.info(f"正在查询域名完整信息: {domain}")
            w = whois.whois(domain)
            
            # 处理注册时间，可能是列表或单个日期
            creation_date = w.creation_date
            if isinstance(creation_date, list):
                creation_date = creation_date[0]
            
            # 处理过期时间，可能是列表或单个日期
            expiration_date = w.expiration_date
            if isinstance(expiration_date, list):
                expiration_date = expiration_date[0]
                
            # 构建结果
            result = {
                "域名": domain,
                "注册时间": creation_date,
                "过期时间": expiration_date,
                "注册商": w.registrar if hasattr(w, 'registrar') else None,
                "名称服务器": w.name_servers if hasattr(w, 'name_servers') else [],
                "注册人": w.name if hasattr(w, 'name') else None,
                "注册人组织": w.org if hasattr(w, 'org') else None,
                "状态": "success"
            }
            
            logger.info(f"成功获取域名 {domain} 的完整信息")
            return result
            
        except Exception as e:
            error_msg = f"无法获取域名 {domain} 的信息: {str(e)}"
            logger.error(error_msg)
            return {
                "域名": domain,
                "状态": "error",
                "错误": error_msg
            }
    
    @staticmethod
    def extract_domain_from_url(url: str) -> str:
        """
        从URL中提取域名
        
        Args:
            url (str): 完整的URL
            
        Returns:
            str: 提取的域名
        """
        try:
            from urllib.parse import urlparse
            
            if not url:
                return ""
            
            # 如果URL没有协议，添加http://
            if not url.startswith(('http://', 'https://')):
                url = 'http://' + url
            
            parsed = urlparse(url)
            return parsed.netloc
            
        except Exception as e:
            logger.error(f"从URL提取域名失败: {url}, 错误: {str(e)}")
            return ""
    
    @staticmethod
    def is_valid_domain(domain: str) -> bool:
        """
        检查域名是否有效
        
        Args:
            domain (str): 要检查的域名
            
        Returns:
            bool: 域名是否有效
        """
        import re
        
        if not domain:
            return False
        
        # 基本的域名格式检查
        domain_pattern = r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$'
        
        return bool(re.match(domain_pattern, domain)) and len(domain) <= 253

    @staticmethod
    def _clean_domain(domain: str) -> str:
        """
        清理域名，移除协议和路径

        Args:
            domain: 原始域名或URL

        Returns:
            str: 清理后的域名
        """
        if not domain:
            return ""

        # 移除协议
        if domain.startswith(('http://', 'https://')):
            domain = domain.split('://')[1]

        # 移除路径
        if '/' in domain:
            domain = domain.split('/')[0]

        # 移除端口号
        if ':' in domain:
            domain = domain.split(':')[0]

        # 移除www前缀（可选）
        if domain.startswith('www.'):
            domain = domain[4:]

        return domain.lower().strip()

    @staticmethod
    def _query_with_python_whois(domain: str) -> Union[datetime, str, None]:
        """
        使用python-whois库查询域名注册日期

        Args:
            domain: 域名

        Returns:
            datetime或str或None: 注册日期或错误信息
        """
        try:
            logger.info(f"📡 [Python-whois] 查询域名: {domain}")
            w = whois.whois(domain)

            # 尝试多个可能的字段名
            date_fields = ['creation_date', 'created', 'registered', 'registration_date']

            for field in date_fields:
                if hasattr(w, field):
                    creation_date = getattr(w, field)
                    logger.info(f"📊 [Python-whois] 找到字段 {field}: {creation_date} (类型: {type(creation_date)})")

                    if creation_date:
                        # 处理列表格式
                        if isinstance(creation_date, list):
                            if len(creation_date) > 0:
                                creation_date = creation_date[0]
                            else:
                                continue

                        # 确保是datetime对象
                        if isinstance(creation_date, datetime):
                            return creation_date
                        elif isinstance(creation_date, str):
                            # 尝试解析字符串日期
                            parsed_date = DomainInfoUtils._parse_date_string(creation_date)
                            if parsed_date:
                                return parsed_date

            logger.warning(f"⚠️ [Python-whois] 未找到有效的注册日期字段")
            return None

        except Exception as e:
            error_msg = f"Python-whois查询失败: {str(e)}"
            logger.warning(f"⚠️ [Python-whois] {error_msg}")
            return error_msg

    @staticmethod
    def _query_with_whosb_api(domain: str) -> Union[datetime, str, None]:
        """
        使用Who.sb API查询域名注册日期（支持重试机制）

        Args:
            domain: 域名

        Returns:
            datetime或str或None: 注册日期或错误信息
        """
        try:
            logger.info(f"🌐 [Who.sb API] 查询域名: {domain}")

            # 使用asyncio运行异步函数
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(DomainInfoUtils._get_domain_info_async(domain))
                if result and result[0]:  # 检查注册时间
                    # 解析返回的日期字符串
                    registration_date = DomainInfoUtils._parse_date_string(result[0])
                    if registration_date:
                        logger.info(f"✅ [Who.sb API] 成功获取注册日期: {registration_date}")
                        return registration_date
                    else:
                        logger.warning(f"⚠️ [Who.sb API] 无法解析注册日期: {result[0]}")
                        return None
                else:
                    logger.warning(f"⚠️ [Who.sb API] 未获取到注册日期")
                    return None
            finally:
                loop.close()

        except Exception as e:
            error_msg = f"Who.sb API查询失败: {str(e)}"
            logger.warning(f"⚠️ [Who.sb API] {error_msg}")
            return error_msg

    @staticmethod
    @retry(
        stop=stop_after_attempt(3),  # 减少重试次数到3次
        wait=wait_random_exponential(multiplier=1, max=30),  # 减少最大等待时间到30s
        retry=retry_if_exception(_should_retry_whosb_error),  # 使用更智能的重试策略
        before_sleep=_log_retry  # 重试前记录日志
    )
    async def _get_domain_info_async(domain_name: str) -> Optional[Tuple[Optional[str], Optional[str], Optional[str]]]:
        """
        获取指定域名的注册信息（异步版本，支持重试）

        Args:
            domain_name: 需要查询的域名

        Returns:
            一个元组，包含注册时间、更新时间和过期时间。如果查询失败或数据不存在，则返回 None。
            (registered_time, updated_time, expires_time)
        """
        url = f"https://instant.who.sb/api/v1/whois?domain={domain_name}&cache=false&return-prices=false"
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(url, timeout=10.0)
                response.raise_for_status()  # 如果状态码是 4xx 或 5xx，则引发 HTTPStatusError

            data: Dict[str, Any] = response.json()

            if data and isinstance(data, dict) and "parsed" in data:
                parsed_info = data.get("parsed")
                if isinstance(parsed_info, dict):
                    registered_time = parsed_info.get("registered")
                    # API使用 'd_updated' 表示域名数据更新时间
                    updated_time = parsed_info.get("d_updated")
                    expires_time = parsed_info.get("expires")

                    formatted_registered_time = _format_datetime_str(registered_time)
                    formatted_updated_time = _format_datetime_str(updated_time)
                    formatted_expires_time = _format_datetime_str(expires_time)

                    # 记录提取的信息用于调试
                    logger.info(f"📊 [Who.sb API] 域名: {domain_name}, 注册: {formatted_registered_time}, 更新: {formatted_updated_time}, 过期: {formatted_expires_time}")

                    return formatted_registered_time, formatted_updated_time, formatted_expires_time
                else:
                    logger.warning(f"⚠️ [Who.sb API] 解析信息不是字典格式，域名 {domain_name}: {parsed_info}")
            else:
                logger.warning(f"⚠️ [Who.sb API] 意外的数据结构或缺少'parsed'键，域名 {domain_name}: {data}")

            return None, None, None

        except httpx.HTTPStatusError as e:
            logger.error(f"❌ [Who.sb API] HTTP错误，域名 {domain_name}: {e.response.status_code} - {e.response.text}")
            raise  # 重新抛出异常以便重试机制处理
        except httpx.RequestError as e:
            logger.error(f"❌ [Who.sb API] 请求错误，域名 {domain_name}: {e}")
            raise  # 重新抛出异常以便重试机制处理
        except Exception as e:
            logger.error(f"❌ [Who.sb API] 意外错误，域名 {domain_name}: {e}")
            raise  # 重新抛出异常以便重试机制处理

    @staticmethod
    def _query_with_whois_api(domain: str) -> Union[datetime, str, None]:
        """
        使用WHOIS API查询域名注册日期

        Args:
            domain: 域名

        Returns:
            datetime或str或None: 注册日期或错误信息
        """
        try:
            logger.info(f"🌐 [WHOIS API] 查询域名: {domain}")

            # 使用免费的WHOIS API服务
            api_urls = [
                f"https://api.whoisjson.com/v1/{domain}",
                f"https://whois.freeapi.app/api/whois?domainName={domain}",
                f"https://api.whoapi.com/?domain={domain}&r=whois&apikey=free"
            ]

            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }

            for api_url in api_urls:
                try:
                    logger.info(f"🔗 [WHOIS API] 尝试API: {api_url}")
                    response = requests.get(api_url, headers=headers, timeout=10)

                    if response.status_code == 200:
                        data = response.json()

                        # 尝试从不同的API响应格式中提取注册日期
                        date_value = DomainInfoUtils._extract_date_from_api_response(data)
                        if date_value:
                            return date_value

                    time.sleep(1)  # API调用间隔

                except requests.RequestException as e:
                    logger.warning(f"⚠️ [WHOIS API] API请求失败: {api_url}, 错误: {str(e)}")
                    continue

            logger.warning(f"⚠️ [WHOIS API] 所有API都未返回有效数据")
            return None

        except Exception as e:
            error_msg = f"WHOIS API查询失败: {str(e)}"
            logger.warning(f"⚠️ [WHOIS API] {error_msg}")
            return error_msg

    @staticmethod
    def _query_with_system_whois(domain: str) -> Union[datetime, str, None]:
        """
        使用系统whois命令查询域名注册日期

        Args:
            domain: 域名

        Returns:
            datetime或str或None: 注册日期或错误信息
        """
        try:
            logger.info(f"💻 [系统whois] 查询域名: {domain}")

            # 执行系统whois命令，减少超时时间提高效率
            result = subprocess.run(
                ['whois', domain],
                capture_output=True,
                text=True,
                timeout=15  # 减少超时时间从30s到15s
            )

            if result.returncode == 0:
                whois_text = result.stdout
                logger.info(f"📄 [系统whois] 获取到whois文本，长度: {len(whois_text)}")

                # 从whois文本中提取注册日期
                date_value = DomainInfoUtils._extract_date_from_whois_text(whois_text)
                if date_value:
                    return date_value
            else:
                logger.warning(f"⚠️ [系统whois] 命令执行失败，返回码: {result.returncode}")
                logger.warning(f"⚠️ [系统whois] 错误输出: {result.stderr}")

            return None

        except subprocess.TimeoutExpired:
            error_msg = "系统whois命令超时"
            logger.warning(f"⚠️ [系统whois] {error_msg}")
            return error_msg
        except FileNotFoundError:
            error_msg = "系统未安装whois命令"
            logger.warning(f"⚠️ [系统whois] {error_msg}")
            return error_msg
        except Exception as e:
            error_msg = f"系统whois查询失败: {str(e)}"
            logger.warning(f"⚠️ [系统whois] {error_msg}")
            return error_msg

    @staticmethod
    def _query_with_backup_whois(domain: str) -> Union[datetime, str, None]:
        """
        使用备用WHOIS服务查询域名注册日期

        Args:
            domain: 域名

        Returns:
            datetime或str或None: 注册日期或错误信息
        """
        try:
            logger.info(f"🔄 [备用WHOIS] 查询域名: {domain}")

            # 尝试直接连接WHOIS服务器
            tld = domain.split('.')[-1].lower()
            whois_servers = DomainInfoUtils._get_whois_servers(tld)

            for server in whois_servers:
                try:
                    logger.info(f"🌐 [备用WHOIS] 尝试服务器: {server}")
                    whois_text = DomainInfoUtils._query_whois_server(domain, server)

                    if whois_text:
                        date_value = DomainInfoUtils._extract_date_from_whois_text(whois_text)
                        if date_value:
                            return date_value

                    time.sleep(1)  # 服务器查询间隔

                except Exception as e:
                    logger.warning(f"⚠️ [备用WHOIS] 服务器 {server} 查询失败: {str(e)}")
                    continue

            logger.warning(f"⚠️ [备用WHOIS] 所有备用服务器都未返回有效数据")
            return None

        except Exception as e:
            error_msg = f"备用WHOIS查询失败: {str(e)}"
            logger.warning(f"⚠️ [备用WHOIS] {error_msg}")
            return error_msg

    @staticmethod
    def _get_whois_servers(tld: str) -> List[str]:
        """
        获取指定TLD的WHOIS服务器列表

        Args:
            tld: 顶级域名

        Returns:
            List[str]: WHOIS服务器列表
        """
        # 常见TLD的WHOIS服务器（按成功率排序）
        whois_servers = {
            'com': ['whois.verisign-grs.com', 'whois.internic.net'],
            'net': ['whois.verisign-grs.com', 'whois.internic.net'],
            'org': ['whois.pir.org'],
            'info': ['whois.afilias.net'],
            'biz': ['whois.neulevel.biz'],
            'us': ['whois.nic.us'],
            'uk': ['whois.nic.uk'],
            'de': ['whois.denic.de'],
            'fr': ['whois.afnic.fr'],
            'jp': ['whois.jprs.jp'],
            'cn': ['whois.cnnic.cn'],
            'ru': ['whois.tcinet.ru'],
            'io': ['whois.nic.io'],
            'co': ['whois.nic.co'],
            'me': ['whois.nic.me'],
            'tv': ['whois.nic.tv'],
            'cc': ['whois.nic.cc'],
            'app': ['whois.nic.google'],  # 新增.app域名支持
            'es': ['whois.nic.es'],       # 新增.es域名支持
            'vn': ['whois.nic.vn'],       # 新增.vn域名支持
            'ai': ['whois.nic.ai'],       # 新增.ai域名支持
        }

        return whois_servers.get(tld, ['whois.internic.net'])

    @staticmethod
    def _query_whois_server(domain: str, server: str, port: int = 43) -> Optional[str]:
        """
        直接查询WHOIS服务器

        Args:
            domain: 域名
            server: WHOIS服务器
            port: 端口号，默认43

        Returns:
            Optional[str]: WHOIS响应文本
        """
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            sock.connect((server, port))

            query = f"{domain}\r\n"
            sock.send(query.encode('utf-8'))

            response = b""
            while True:
                data = sock.recv(4096)
                if not data:
                    break
                response += data

            sock.close()
            return response.decode('utf-8', errors='ignore')

        except Exception as e:
            logger.warning(f"⚠️ [WHOIS服务器] 连接 {server} 失败: {str(e)}")
            return None

    @staticmethod
    def _extract_date_from_api_response(data: Dict[str, Any]) -> Optional[datetime]:
        """
        从API响应中提取注册日期

        Args:
            data: API响应数据

        Returns:
            Optional[datetime]: 注册日期
        """
        try:
            # 尝试不同的字段名和数据结构
            date_paths = [
                ['creation_date'],
                ['created'],
                ['registered'],
                ['registration_date'],
                ['domain', 'creation_date'],
                ['domain', 'created'],
                ['whois', 'creation_date'],
                ['whois', 'created'],
                ['result', 'creation_date'],
                ['data', 'creation_date'],
            ]

            for path in date_paths:
                try:
                    value = data
                    for key in path:
                        if isinstance(value, dict) and key in value:
                            value = value[key]
                        else:
                            value = None
                            break

                    if value:
                        if isinstance(value, str):
                            parsed_date = DomainInfoUtils._parse_date_string(value)
                            if parsed_date:
                                return parsed_date
                        elif isinstance(value, datetime):
                            return value

                except Exception:
                    continue

            return None

        except Exception as e:
            logger.warning(f"⚠️ [API响应解析] 解析失败: {str(e)}")
            return None

    @staticmethod
    def _extract_date_from_whois_text(whois_text: str) -> Optional[datetime]:
        """
        从WHOIS文本中提取注册日期

        Args:
            whois_text: WHOIS响应文本

        Returns:
            Optional[datetime]: 注册日期
        """
        try:
            # 常见的注册日期字段模式
            patterns = [
                r'Creation Date:\s*(.+)',
                r'Created:\s*(.+)',
                r'Registered:\s*(.+)',
                r'Registration Date:\s*(.+)',
                r'Domain Registration Date:\s*(.+)',
                r'created:\s*(.+)',
                r'registration date:\s*(.+)',
                r'Registration Time:\s*(.+)',
                r'Registered On:\s*(.+)',
                r'Created On:\s*(.+)',
                r'Record created:\s*(.+)',
                r'Domain created:\s*(.+)',
                r'Registered Date:\s*(.+)',
                r'Created Date:\s*(.+)',
                r'Registration:\s*(.+)',
            ]

            for pattern in patterns:
                matches = re.findall(pattern, whois_text, re.IGNORECASE | re.MULTILINE)
                for match in matches:
                    date_str = match.strip()
                    if date_str:
                        parsed_date = DomainInfoUtils._parse_date_string(date_str)
                        if parsed_date:
                            logger.info(f"📅 [WHOIS文本解析] 找到注册日期: {parsed_date}")
                            return parsed_date

            logger.warning(f"⚠️ [WHOIS文本解析] 未找到匹配的日期模式")
            return None

        except Exception as e:
            logger.warning(f"⚠️ [WHOIS文本解析] 解析失败: {str(e)}")
            return None

    @staticmethod
    def _parse_date_string(date_str: str) -> Optional[datetime]:
        """
        解析日期字符串为datetime对象

        Args:
            date_str: 日期字符串

        Returns:
            Optional[datetime]: 解析后的日期对象
        """
        if not date_str or not isinstance(date_str, str):
            return None

        # 清理日期字符串
        date_str = date_str.strip()

        # 移除常见的后缀
        suffixes_to_remove = [' UTC', ' GMT', ' PST', ' EST', ' CST', ' MST', 'T00:00:00Z', 'Z']
        for suffix in suffixes_to_remove:
            if date_str.endswith(suffix):
                date_str = date_str[:-len(suffix)].strip()

        # 常见的日期格式
        date_formats = [
            '%Y-%m-%d',                    # 2020-01-15
            '%Y-%m-%d %H:%M:%S',          # 2020-01-15 10:30:00
            '%Y-%m-%dT%H:%M:%S',          # 2020-01-15T10:30:00
            '%Y-%m-%dT%H:%M:%SZ',         # 2020-01-15T10:30:00Z
            '%Y/%m/%d',                    # 2020/01/15
            '%Y/%m/%d %H:%M:%S',          # 2020/01/15 10:30:00
            '%d-%m-%Y',                    # 15-01-2020
            '%d/%m/%Y',                    # 15/01/2020
            '%d.%m.%Y',                    # 15.01.2020
            '%m/%d/%Y',                    # 01/15/2020
            '%m-%d-%Y',                    # 01-15-2020
            '%d %b %Y',                    # 15 Jan 2020
            '%d %B %Y',                    # 15 January 2020
            '%b %d %Y',                    # Jan 15 2020
            '%B %d %Y',                    # January 15 2020
            '%Y.%m.%d',                    # 2020.01.15
            '%d-%b-%Y',                    # 15-Jan-2020
            '%d-%B-%Y',                    # 15-January-2020
            '%Y%m%d',                      # 20200115
            '%d-%m-%Y %H:%M:%S',          # 15-01-2020 10:30:00
            '%d/%m/%Y %H:%M:%S',          # 15/01/2020 10:30:00
            '%m/%d/%Y %H:%M:%S',          # 01/15/2020 10:30:00
        ]

        for fmt in date_formats:
            try:
                parsed_date = datetime.strptime(date_str, fmt)
                logger.info(f"📅 [日期解析] 成功解析: '{date_str}' -> {parsed_date} (格式: {fmt})")
                return parsed_date
            except ValueError:
                continue

        # 尝试使用更灵活的解析方法
        try:
            # 使用正则表达式提取数字
            import re

            # 匹配 YYYY-MM-DD 或 YYYY/MM/DD 格式
            match = re.search(r'(\d{4})[/-](\d{1,2})[/-](\d{1,2})', date_str)
            if match:
                year, month, day = match.groups()
                parsed_date = datetime(int(year), int(month), int(day))
                logger.info(f"📅 [日期解析] 正则表达式解析成功: '{date_str}' -> {parsed_date}")
                return parsed_date

            # 匹配 DD-MM-YYYY 或 DD/MM/YYYY 格式
            match = re.search(r'(\d{1,2})[/-](\d{1,2})[/-](\d{4})', date_str)
            if match:
                day, month, year = match.groups()
                parsed_date = datetime(int(year), int(month), int(day))
                logger.info(f"📅 [日期解析] 正则表达式解析成功: '{date_str}' -> {parsed_date}")
                return parsed_date

        except Exception as e:
            logger.warning(f"⚠️ [日期解析] 正则表达式解析失败: {str(e)}")

        logger.warning(f"⚠️ [日期解析] 无法解析日期字符串: '{date_str}'")
        return None
