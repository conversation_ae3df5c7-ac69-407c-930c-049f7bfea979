import logging
from typing import Dict, Any, List
import json

logger = logging.getLogger(__name__)

class ToolifyDataAnalyzer:
    """
    解析和分析Toolify API返回的数据，并映射到我们的数据库结构
    """
    
    @staticmethod
    def analyze_mapping() -> Dict[str, Dict[str, str]]:
        """
        返回Toolify API字段与我们数据库字段的映射关系
        """
        mapping = {
            "工具表(tools)": {
                "id": "tool_id (外部工具ID)",
                "website": "url (工具网址)",
                "image": "icon_url (工具图标)",
                "is_recommend_now": "is_featured (是否特色工具)",
                "is_ad": "is_premium (是否付费工具)",
                "is_noticeable": "is_new (是否新工具)",
                "collected_count/month_visited_count": "rating (可用于计算评分)"
            },
            "工具翻译表(tool_translations)": {
                "name": "name (工具名称)",
                "description/what_is_summary": "description (工具描述)",
                "locale": "默认为'en'和'zh'"
            },
            "分类关系(tool_categories)": {
                "categories[].handle": "category_id (通过categories表查找)",
                "categories[].name": "存储在category_translations表"
            }
        }
        
        return mapping
    
    @staticmethod
    def transform_tool_data(tool_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        将Toolify API返回的单个工具数据转换为我们的数据库格式
        
        Args:
            tool_data: Toolify API返回的工具数据
            
        Returns:
            dict: 转换后符合我们数据库结构的数据
        """
        # 基本信息转换
        transformed = {
            "tool_id": str(tool_data.get("id", "")),
            "name": tool_data.get("name", ""),
            "description": tool_data.get("description", "") or tool_data.get("what_is_summary", ""),
            "url": tool_data.get("website", ""),
            "icon_url": tool_data.get("image", ""),
            "is_featured": bool(tool_data.get("is_recommend_now", 0)),
            "is_premium": bool(tool_data.get("is_ad", False)),
            "is_new": bool(tool_data.get("is_noticeable", 0) == 1),
            "pricing_type": "free" if not tool_data.get("is_ad", False) else "premium",
            "rating": float(tool_data.get("collected_count", 0)) / 10.0 if tool_data.get("collected_count", 0) > 0 else 0.0,
            "categories": [
                {"handle": cat.get("handle", ""), "name": cat.get("name", "")}
                for cat in tool_data.get("categories", [])
                if isinstance(cat, dict)
            ]
        }
        
        return transformed
    
    @staticmethod
    def print_sample_data(data: Dict[str, Any]) -> None:
        """
        打印样本数据，分析字段结构
        
        Args:
            data: Toolify API返回的完整数据
        """
        if not data or "data" not in data or not isinstance(data["data"], list) or len(data["data"]) == 0:
            logger.warning("无效的数据格式")
            return
        
        sample = data["data"][0]
        logger.info("===== Toolify API 数据结构分析 =====")
        logger.info(f"总工具数: {len(data['data'])}")
        logger.info(f"总分类数: {data.get('category_count', 0)}")
        
        logger.info("单个工具数据示例:")
        for key, value in sample.items():
            if isinstance(value, dict) or isinstance(value, list):
                logger.info(f"  {key}: {type(value).__name__} ({len(value)} 项)")
            else:
                logger.info(f"  {key}: {value}")
        
        # 打印分类示例
        if "categories" in sample and len(sample["categories"]) > 0:
            logger.info("分类数据示例:")
            cat = sample["categories"][0]
            for key, value in cat.items():
                logger.info(f"  {key}: {value}")
    
    @staticmethod
    def validate_tool_data(tool_data: Dict[str, Any]) -> tuple[bool, List[str]]:
        """
        验证工具数据的完整性
        
        Args:
            tool_data: 工具数据
            
        Returns:
            tuple: (是否有效, 错误信息列表)
        """
        errors = []
        
        # 检查必需字段
        required_fields = ['id', 'name', 'website']
        for field in required_fields:
            if not tool_data.get(field):
                errors.append(f"缺少必需字段: {field}")
        
        # 检查URL格式
        url = tool_data.get('website', '')
        if url and not (url.startswith('http://') or url.startswith('https://')):
            errors.append(f"URL格式无效: {url}")
        
        # 检查分类数据
        categories = tool_data.get('categories', [])
        if not isinstance(categories, list):
            errors.append("分类数据格式错误，应为列表")
        
        return len(errors) == 0, errors
    
    @staticmethod
    def extract_categories(tools_data: List[Dict[str, Any]]) -> Dict[str, Dict[str, str]]:
        """
        从工具数据中提取所有分类信息
        
        Args:
            tools_data: 工具数据列表
            
        Returns:
            dict: 分类信息字典 {handle: {name: str, description: str}}
        """
        categories = {}
        
        for tool in tools_data:
            tool_categories = tool.get('categories', [])
            if isinstance(tool_categories, list):
                for cat in tool_categories:
                    if isinstance(cat, dict):
                        handle = cat.get('handle', '')
                        name = cat.get('name', '')
                        if handle and name:
                            categories[handle] = {
                                'name': name,
                                'description': cat.get('description', '')
                            }
        
        return categories
    
    @staticmethod
    def generate_statistics(tools_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        生成工具数据的统计信息
        
        Args:
            tools_data: 工具数据列表
            
        Returns:
            dict: 统计信息
        """
        if not tools_data:
            return {}
        
        total_tools = len(tools_data)
        premium_tools = sum(1 for tool in tools_data if tool.get('is_ad', False))
        new_tools = sum(1 for tool in tools_data if tool.get('is_noticeable', 0) == 1)
        featured_tools = sum(1 for tool in tools_data if tool.get('is_recommend_now', 0))
        
        # 统计分类
        all_categories = set()
        for tool in tools_data:
            categories = tool.get('categories', [])
            if isinstance(categories, list):
                for cat in categories:
                    if isinstance(cat, dict) and cat.get('handle'):
                        all_categories.add(cat['handle'])
        
        # 统计评分分布
        ratings = []
        for tool in tools_data:
            collected_count = tool.get('collected_count', 0)
            if collected_count > 0:
                rating = min(float(collected_count) / 100.0, 5.0)
                ratings.append(rating)
        
        avg_rating = sum(ratings) / len(ratings) if ratings else 0
        
        return {
            'total_tools': total_tools,
            'premium_tools': premium_tools,
            'new_tools': new_tools,
            'featured_tools': featured_tools,
            'total_categories': len(all_categories),
            'tools_with_rating': len(ratings),
            'average_rating': round(avg_rating, 2),
            'premium_percentage': round((premium_tools / total_tools) * 100, 2) if total_tools > 0 else 0,
            'new_percentage': round((new_tools / total_tools) * 100, 2) if total_tools > 0 else 0,
            'featured_percentage': round((featured_tools / total_tools) * 100, 2) if total_tools > 0 else 0
        }

def analyze_toolify_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    分析Toolify数据的便捷函数
    
    Args:
        data: Toolify API返回的数据
        
    Returns:
        dict: 分析结果
    """
    analyzer = ToolifyDataAnalyzer()
    
    if not data or 'data' not in data:
        return {'error': '无效的数据格式'}
    
    tools_data = data['data']
    
    # 生成统计信息
    statistics = analyzer.generate_statistics(tools_data)
    
    # 提取分类信息
    categories = analyzer.extract_categories(tools_data)
    
    # 获取映射关系
    mapping = analyzer.analyze_mapping()
    
    return {
        'statistics': statistics,
        'categories': categories,
        'mapping': mapping,
        'sample_count': min(len(tools_data), 5)
    }
