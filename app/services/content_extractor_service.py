"""
网站内容提取服务

提供网站内容爬取和智能提取功能，包括：
- HTML内容解析
- 智能文本提取
- 内容分块处理
- 价格信息识别
- 结构化内容提取
"""

import re
import requests
import logging
from typing import Dict, List, Any
from urllib.parse import urlparse
from bs4 import BeautifulSoup

logger = logging.getLogger(__name__)


class ContentExtractorService:
    """网站内容提取服务类"""
    
    def __init__(self):
        """初始化内容提取服务"""
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
    
    def get_domain_from_url(self, url: str) -> str:
        """
        从URL中提取域名信息
        
        Args:
            url: 网站URL
            
        Returns:
            str: 域名
        """
        parsed_url = urlparse(url)
        return parsed_url.netloc.replace('www.', '')
    
    def extract_website_content(self, url: str, max_chunks: int = 5, chunk_size: int = 2000) -> Dict[str, Any]:
        """
        爬取网站内容并提取文本
        
        Args:
            url: 网站URL
            max_chunks: 最大文本块数量
            chunk_size: 每个文本块的最大字符数
            
        Returns:
            Dict: 包含标题、描述和文本块的字典
        """
        try:
            logger.info(f"正在爬取网站: {url}")
            
            # 发送HTTP请求
            response = requests.get(url, headers=self.headers, timeout=15)
            response.raise_for_status()
            
            # 使用BeautifulSoup解析HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 移除脚本、样式和非内容元素
            for tag in soup(["script", "style", "iframe", "svg", "noscript"]):
                tag.extract()
            
            # 获取页面标题
            title = self._extract_title(soup)
            
            # 获取meta描述
            meta_desc = self._extract_meta_description(soup)
            
            # 提取内容
            content_data = self._extract_content(soup)
            
            # 智能分块
            chunks = self._smart_chunk_content(content_data, max_chunks, chunk_size)
            
            logger.info(f"成功从网站提取了 {len(chunks)} 个文本块，总字符数: {sum(len(chunk) for chunk in chunks)}")
            
            return {
                "title": title,
                "meta_description": meta_desc,
                "chunks": chunks
            }
            
        except requests.exceptions.RequestException as e:
            logger.error(f"爬取网站 {url} 时发生错误: {str(e)}")
            return {
                "title": "",
                "meta_description": "",
                "chunks": []
            }
        except Exception as e:
            logger.error(f"解析网站 {url} 内容时发生错误: {str(e)}")
            return {
                "title": "",
                "meta_description": "",
                "chunks": []
            }
    
    def _extract_title(self, soup: BeautifulSoup) -> str:
        """提取页面标题"""
        title_tag = soup.find("title")
        return title_tag.get_text().strip() if title_tag else ""
    
    def _extract_meta_description(self, soup: BeautifulSoup) -> str:
        """提取meta描述"""
        meta_desc = ""
        meta_tags = [
            soup.find("meta", attrs={"name": "description"}),
            soup.find("meta", attrs={"property": "og:description"}),
            soup.find("meta", attrs={"name": "twitter:description"})
        ]
        
        for meta_tag in meta_tags:
            if meta_tag and "content" in meta_tag.attrs:
                content = meta_tag["content"].strip()
                if len(content) > len(meta_desc):
                    meta_desc = content
        
        return meta_desc
    
    def _extract_content(self, soup: BeautifulSoup) -> str:
        """提取网站内容"""
        all_texts = []
        
        # 方法1：获取重要内容区域
        important_sections = self._extract_important_sections(soup)
        if important_sections:
            all_texts.append("## 重要内容区域")
            all_texts.extend(important_sections)
        
        # 方法2：获取结构化元素内容
        structured_content = self._extract_structured_content(soup)
        if structured_content:
            all_texts.append("## 结构化内容")
            all_texts.extend(structured_content)
        
        # 方法3：获取价格信息
        price_info = self._extract_price_info(soup)
        if price_info:
            all_texts.append("## 价格信息")
            all_texts.extend(price_info)
        
        # 如果上述方法都没有获取到足够内容，使用完整文本
        combined_text = "\n\n".join(all_texts)
        if len(combined_text) < 500:
            full_text = soup.get_text(separator="\n", strip=True)
            if full_text:
                all_texts.append("## 网站全文内容")
                paragraphs = re.split(r'\n{2,}', full_text)
                all_texts.extend(paragraphs)
        
        # 整合并清理文本
        combined_text = "\n\n".join(all_texts)
        return self._clean_text(combined_text)
    
    def _extract_important_sections(self, soup: BeautifulSoup) -> List[str]:
        """提取重要内容区域"""
        important_sections_text = []
        
        # 常见内容区域类和ID
        content_selectors = [
            "div.content", "div.main", "article", "main", "div.article", 
            "div.post", "div.entry", "div.prose", "div.faq", "#content", 
            "#main", "#article", "section.content", "div[role='main']",
            ".post-content", ".entry-content", ".article-content", ".page-content",
            ".product-description", ".pricing", ".features", "div.description"
        ]
        
        for selector in content_selectors:
            try:
                section = soup.select_one(selector)
                if section and section.get_text(strip=True):
                    text = section.get_text(separator="\n", strip=True)
                    if len(text) > 100:
                        important_sections_text.append(f"[{selector}区域内容]\n{text}")
            except Exception as e:
                logger.debug(f"使用选择器 {selector} 查找内容时出错: {str(e)}")
        
        return important_sections_text
    
    def _extract_structured_content(self, soup: BeautifulSoup) -> List[str]:
        """提取结构化内容"""
        elements_text = []
        
        # 提取标题
        headings = []
        for i, heading in enumerate(soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])):
            text = heading.get_text(strip=True)
            if text:
                tag_name = heading.name
                level = int(tag_name[1])
                headings.append((level, text, i))
        
        # 按序号排序并格式化
        headings.sort(key=lambda x: x[2])
        for level, text, _ in headings:
            elements_text.append(f"{'#' * level} {text}")
        
        # 提取段落
        for p in soup.find_all('p'):
            text = p.get_text(strip=True)
            if text and len(text) > 15:
                elements_text.append(text)
        
        # 提取列表
        for list_elem in soup.find_all(['ul', 'ol']):
            list_items = []
            for li in list_elem.find_all('li'):
                text = li.get_text(strip=True)
                if text:
                    list_items.append(f"- {text}")
            if list_items:
                elements_text.append("\n".join(list_items))
        
        # 提取表格
        for table in soup.find_all('table'):
            table_content = self._extract_table_content(table)
            if table_content:
                elements_text.append(table_content)
        
        return elements_text
    
    def _extract_table_content(self, table) -> str:
        """提取表格内容"""
        rows = []
        
        # 添加表头
        headers = []
        for th in table.find_all('th'):
            text = th.get_text(strip=True)
            headers.append(text if text else " ")
        
        if headers:
            rows.append("| " + " | ".join(headers) + " |")
            rows.append("| " + " | ".join(["---"] * len(headers)) + " |")
        
        # 添加数据行
        for tr in table.find_all('tr'):
            cells = []
            for td in tr.find_all(['td']):
                text = td.get_text(strip=True)
                cells.append(text if text else " ")
            if cells:
                rows.append("| " + " | ".join(cells) + " |")
        
        return "\n".join(rows) if rows else ""
    
    def _extract_price_info(self, soup: BeautifulSoup) -> List[str]:
        """提取价格信息"""
        price_info = []
        
        # 扩展的价格相关选择器，包含更多可能的定价页面元素
        price_selectors = [
            # 基本价格选择器
            ".price", ".pricing", ".plan", ".subscription", ".cost", ".billing",
            "#pricing", "#price", "#plans", "#subscription",
            
            # 具体的价格容器
            "div.price", "div.pricing", "section.pricing", "div.plans", 
            "div.subscription", "div.billing", "section.plans",
            
            # 价格卡片和表格
            ".price-card", ".pricing-card", ".plan-card", ".subscription-card",
            ".pricing-table", ".price-table", ".plans-table",
            
            # 通用属性选择器
            "[class*='price']", "[class*='pricing']", "[class*='plan']", 
            "[class*='subscription']", "[class*='billing']",
            "[id*='price']", "[id*='pricing']", "[id*='plan']", "[id*='subscription']",
            
            # 特定的定价页面结构
            ".tier", ".package", ".offer", ".deal",
            "[data-price]", "[data-plan]", "[data-tier]"
        ]
        
        # 价格关键词模式，更全面的匹配
        price_patterns = [
            r'(\$|€|£|¥|₹)\s*\d+',  # 货币符号 + 数字
            r'\d+\s*(\$|€|£|¥|₹)',  # 数字 + 货币符号
            r'\b\d+\s*(USD|EUR|GBP|CNY|INR)\b',  # 数字 + 货币代码
            r'\b(free|trial|demo)\b',  # 免费相关
            r'\b(month|monthly|year|yearly|annual)\b',  # 计费周期
            r'\b(plan|tier|package|subscription)\b',  # 方案相关
            r'\b(basic|pro|premium|enterprise|starter)\b',  # 常见方案名称
            r'\b(per\s+(user|month|year))\b',  # 计费单位
            r'\b(starting\s+(at|from))\b'  # 起始价格
        ]
        
        combined_pattern = '|'.join(price_patterns)
        
        for selector in price_selectors:
            try:
                price_elements = soup.select(selector)
                for elem in price_elements:
                    text = elem.get_text(strip=True)
                    if text and len(text) > 3:  # 避免过短的文本
                        # 使用更精确的正则匹配
                        if re.search(combined_pattern, text, re.IGNORECASE):
                            # 清理文本，移除多余空白
                            cleaned_text = re.sub(r'\s+', ' ', text)
                            if len(cleaned_text) <= 500:  # 避免过长的文本
                                price_info.append(f"[{selector}] {cleaned_text}")
                            else:
                                # 截取前500字符
                                price_info.append(f"[{selector}] {cleaned_text[:500]}...")
            except Exception as e:
                logger.debug(f"使用选择器 {selector} 查找价格时出错: {str(e)}")
        
        # 去重，避免重复的价格信息
        unique_price_info = []
        seen_texts = set()
        for info in price_info:
            # 提取实际文本内容（去除选择器标记）
            actual_text = info.split('] ', 1)[-1] if '] ' in info else info
            if actual_text not in seen_texts:
                seen_texts.add(actual_text)
                unique_price_info.append(info)
        
        return unique_price_info[:10]  # 限制返回数量，避免信息过多
    
    def _clean_text(self, text: str) -> str:
        """清理文本"""
        # 清理文本，移除多余空白和特殊字符
        text = re.sub(r'\n{3,}', '\n\n', text)
        text = re.sub(r'\s{2,}', ' ', text)
        text = re.sub(r'[^\w\s\n\.,;:!?#\-\|\[\]\(\)\'\"$€£%&*+=/\\<>{}^~`@]', '', text)
        
        return text.strip() if text.strip() else "无法提取网站内容"
    
    def _smart_chunk_content(self, content: str, max_chunks: int, chunk_size: int) -> List[str]:
        """智能分块内容"""
        if not content:
            return []
        
        chunks = []
        min_chunk_size = min(chunk_size // 4, 500)
        current_chunk = ""
        
        # 按段落分割
        paragraphs = content.split("\n\n")
        
        for para in paragraphs:
            # 如果段落本身超过块大小限制，需要进一步分割
            if len(para) > chunk_size:
                # 先添加现有块
                if current_chunk:
                    chunks.append(current_chunk.strip())
                    current_chunk = ""
                
                # 按句子分割长段落
                sentences = re.split(r'(?<=[.!?])\s+', para)
                para_chunk = ""
                
                for sentence in sentences:
                    if len(para_chunk) + len(sentence) > chunk_size and para_chunk:
                        chunks.append(para_chunk.strip())
                        para_chunk = sentence
                    else:
                        para_chunk += (" " + sentence if para_chunk else sentence)
                
                # 添加最后一部分
                if para_chunk:
                    current_chunk = para_chunk
            else:
                # 检查添加此段落是否会超出块大小
                if len(current_chunk) + len(para) + 2 > chunk_size and current_chunk:
                    chunks.append(current_chunk.strip())
                    current_chunk = para
                else:
                    current_chunk += ("\n\n" + para if current_chunk else para)
        
        # 添加最后一个块
        if current_chunk and len(current_chunk.strip()) >= min_chunk_size:
            chunks.append(current_chunk.strip())
        
        # 如果没有产生足够长的块，则整体作为一个块
        if not chunks or (len(chunks) == 1 and len(chunks[0]) < min_chunk_size):
            chunks = [content.strip()]
        
        # 如果块数量超过最大限制，保留最重要的块
        if len(chunks) > max_chunks:
            chunks = self._select_important_chunks(chunks, max_chunks)
        
        return chunks
    
    def _select_important_chunks(self, chunks: List[str], max_chunks: int) -> List[str]:
        """选择最重要的文本块"""
        chunk_scores = []
        
        for i, chunk in enumerate(chunks):
            # 计算分数因素：长度、标题数量、关键词出现频率
            length_score = len(chunk) / max(len(c) for c in chunks)
            heading_score = len(re.findall(r'^#+\s', chunk, re.MULTILINE)) * 2
            
            # 计算关键词出现频率
            keyword_matches = len(re.findall(
                r'\b(features?|benefits?|pricing|plans?|api|integrate|support|why|how|what|uses?|works?)\b', 
                chunk.lower()
            ))
            keyword_score = keyword_matches * 0.5
            
            # 计算总分
            total_score = length_score + heading_score + keyword_score
            chunk_scores.append((i, total_score, chunk))
        
        # 按总分降序排序
        chunk_scores.sort(key=lambda x: x[1], reverse=True)
        
        # 取前max_chunks个块
        selected_chunks = [cs[2] for cs in chunk_scores[:max_chunks]]
        
        # 按原始顺序排序
        chunk_order = [(selected_chunks.index(cs[2]), cs[0]) for cs in chunk_scores[:max_chunks]]
        chunk_order.sort(key=lambda x: x[1])
        
        return [selected_chunks[i] for i, _ in chunk_order]
