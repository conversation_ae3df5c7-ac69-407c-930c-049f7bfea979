from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from typing import List, Dict, Any, Optional, Tuple
import logging
from datetime import datetime
from app.models.tool import Tool, ToolTranslation, Category, CategoryTranslation, Tag, TagTranslation, ToolFeature
from app.core.database import get_db

logger = logging.getLogger(__name__)

class DatabaseService:
    """
    数据库操作服务类
    """
    
    def __init__(self, db: Session):
        self.db = db
    
    def save_tools_to_database(self, tools_data: List[Dict[str, Any]]) -> Tuple[int, List[str]]:
        """
        批量保存工具数据到数据库
        
        Args:
            tools_data: 工具数据列表
            
        Returns:
            tuple: (成功保存的数量, 错误信息列表)
        """
        saved_count = 0
        errors = []
        
        try:
            for tool_data in tools_data:
                try:
                    success = self.save_single_tool(tool_data)
                    if success:
                        saved_count += 1
                    else:
                        errors.append(f"保存工具失败: {tool_data.get('name', 'Unknown')}")
                except Exception as e:
                    error_msg = f"保存工具 {tool_data.get('name', 'Unknown')} 时出错: {str(e)}"
                    logger.error(error_msg)
                    errors.append(error_msg)
            
            # 提交事务
            self.db.commit()
            logger.info(f"批量保存完成: 成功 {saved_count} 个，失败 {len(errors)} 个")
            
        except Exception as e:
            self.db.rollback()
            error_msg = f"批量保存事务失败: {str(e)}"
            logger.error(error_msg)
            errors.append(error_msg)
        
        return saved_count, errors
    
    def save_single_tool(self, tool_data: Dict[str, Any]) -> bool:
        """
        保存单个工具到数据库

        Args:
            tool_data: 工具数据

        Returns:
            bool: 是否保存成功
        """
        try:
            tool_id = tool_data.get('tool_id')
            if not tool_id:
                logger.error("❌ [数据库] 工具ID为空，跳过保存")
                return False

            logger.info(f"💾 [数据库] 开始保存工具: {tool_id}")
            logger.info(f"📋 [保存数据] 完整数据: {tool_data}")

            # 检查工具是否已存在
            existing_tool = self.db.query(Tool).filter(Tool.tool_id == tool_id).first()

            if existing_tool:
                # 更新现有工具
                logger.info(f"🔄 [数据库] 工具已存在，执行更新: {tool_id}")
                logger.info(f"📊 [更新前] 现有工具信息: ID={existing_tool.id}, URL={existing_tool.url}, 评分={existing_tool.rating}")
                self._update_tool(existing_tool, tool_data)
                logger.info(f"✅ [数据库] 工具更新完成: {tool_id}")
            else:
                # 创建新工具
                logger.info(f"🆕 [数据库] 工具不存在，创建新工具: {tool_id}")
                self._create_tool(tool_data)
                logger.info(f"✅ [数据库] 新工具创建完成: {tool_id}")

            return True
            
        except IntegrityError as e:
            logger.error(f"数据完整性错误: {str(e)}")
            self.db.rollback()
            return False
        except Exception as e:
            logger.error(f"保存工具时出错: {str(e)}")
            self.db.rollback()
            return False
    
    def _create_tool(self, tool_data: Dict[str, Any]) -> Tool:
        """
        创建新工具

        Args:
            tool_data: 工具数据

        Returns:
            Tool: 创建的工具对象
        """
        logger.info(f"🆕 [创建工具] 开始创建工具主记录")

        # 创建工具主记录
        tool = Tool(
            tool_id=tool_data['tool_id'],
            icon_url=tool_data.get('icon_url', ''),
            url=tool_data.get('url', ''),
            pricing_type=tool_data.get('pricing_type', 'free'),
            is_premium=tool_data.get('is_premium', False),
            is_new=tool_data.get('is_new', False),
            is_featured=tool_data.get('is_featured', False),
            rating=tool_data.get('rating'),
            api_available=tool_data.get('api_available', False),
            publisher=tool_data.get('publisher'),
            publisher_url=tool_data.get('publisher_url'),
            terms_url=tool_data.get('terms_url'),
            privacy_url=tool_data.get('privacy_url')
        )

        logger.info(f"📝 [工具主记录] tool_id: {tool_data['tool_id']}")
        logger.info(f"📝 [工具主记录] icon_url: {tool_data.get('icon_url', '')}")
        logger.info(f"📝 [工具主记录] url: {tool_data.get('url', '')}")
        logger.info(f"📝 [工具主记录] pricing_type: {tool_data.get('pricing_type', 'free')}")
        logger.info(f"📝 [工具主记录] is_premium: {tool_data.get('is_premium', False)}")
        logger.info(f"📝 [工具主记录] is_new: {tool_data.get('is_new', False)}")
        logger.info(f"📝 [工具主记录] is_featured: {tool_data.get('is_featured', False)}")
        logger.info(f"📝 [工具主记录] rating: {tool_data.get('rating')}")

        self.db.add(tool)
        self.db.flush()  # 获取ID

        logger.info(f"✅ [工具主记录] 创建成功，数据库ID: {tool.id}")
        
        # 创建工具翻译
        self._create_tool_translation(tool, tool_data)
        
        # 处理分类关联
        self._handle_tool_categories(tool, tool_data.get('categories', []))
        
        return tool
    
    def _update_tool(self, tool: Tool, tool_data: Dict[str, Any]) -> None:
        """
        更新现有工具
        
        Args:
            tool: 现有工具对象
            tool_data: 新的工具数据
        """
        # 更新基本信息
        tool.icon_url = tool_data.get('icon_url', tool.icon_url)
        tool.url = tool_data.get('url', tool.url)
        tool.pricing_type = tool_data.get('pricing_type', tool.pricing_type)
        tool.is_premium = tool_data.get('is_premium', tool.is_premium)
        tool.is_new = tool_data.get('is_new', tool.is_new)
        tool.is_featured = tool_data.get('is_featured', tool.is_featured)
        tool.rating = tool_data.get('rating', tool.rating)
        tool.api_available = tool_data.get('api_available', tool.api_available)
        tool.publisher = tool_data.get('publisher', tool.publisher)
        tool.publisher_url = tool_data.get('publisher_url', tool.publisher_url)
        tool.terms_url = tool_data.get('terms_url', tool.terms_url)
        tool.privacy_url = tool_data.get('privacy_url', tool.privacy_url)
        
        # 更新或创建翻译
        self._update_or_create_tool_translation(tool, tool_data)
        
        # 更新分类关联
        self._handle_tool_categories(tool, tool_data.get('categories', []))
    
    def _create_tool_translation(self, tool: Tool, tool_data: Dict[str, Any]) -> None:
        """
        创建工具翻译

        Args:
            tool: 工具对象
            tool_data: 工具数据
        """
        locale = tool_data.get('locale', 'en')

        logger.info(f"🌐 [创建翻译] 开始创建工具翻译记录")

        translation = ToolTranslation(
            tool_id=tool.tool_id,
            locale=locale,
            name=tool_data.get('name', ''),
            description=tool_data.get('description', ''),
            long_description=tool_data.get('long_description'),
            usage_instructions=tool_data.get('usage_instructions'),
            pricing_details=tool_data.get('pricing_details'),
            integration_info=tool_data.get('integration_info')
        )

        logger.info(f"📝 [翻译记录] tool_id: {tool.tool_id}")
        logger.info(f"📝 [翻译记录] locale: {locale}")
        logger.info(f"📝 [翻译记录] name: {tool_data.get('name', '')}")
        logger.info(f"📝 [翻译记录] description: {tool_data.get('description', '')[:100]}...")

        self.db.add(translation)
        logger.info(f"✅ [翻译记录] 创建成功")
    
    def _update_or_create_tool_translation(self, tool: Tool, tool_data: Dict[str, Any]) -> None:
        """
        更新或创建工具翻译
        
        Args:
            tool: 工具对象
            tool_data: 工具数据
        """
        locale = tool_data.get('locale', 'en')
        
        # 查找现有翻译
        existing_translation = self.db.query(ToolTranslation).filter(
            ToolTranslation.tool_id == tool.tool_id,
            ToolTranslation.locale == locale
        ).first()
        
        if existing_translation:
            # 更新现有翻译
            existing_translation.name = tool_data.get('name', existing_translation.name)
            existing_translation.description = tool_data.get('description', existing_translation.description)
            existing_translation.long_description = tool_data.get('long_description', existing_translation.long_description)
            existing_translation.usage_instructions = tool_data.get('usage_instructions', existing_translation.usage_instructions)
            existing_translation.pricing_details = tool_data.get('pricing_details', existing_translation.pricing_details)
            existing_translation.integration_info = tool_data.get('integration_info', existing_translation.integration_info)
        else:
            # 创建新翻译
            self._create_tool_translation(tool, tool_data)
    
    def _handle_tool_categories(self, tool: Tool, categories_data: List[Dict[str, Any]]) -> None:
        """
        处理工具分类关联
        
        Args:
            tool: 工具对象
            categories_data: 分类数据列表
        """
        if not categories_data:
            return
        
        # 清除现有分类关联
        tool.categories.clear()
        
        for cat_data in categories_data:
            handle = cat_data.get('handle', '').strip()
            name = cat_data.get('name', '').strip()
            
            if not handle or not name:
                continue
            
            # 查找或创建分类
            category = self._get_or_create_category(handle, name)
            if category:
                tool.categories.append(category)
    
    def _get_or_create_category(self, handle: str, name: str) -> Optional[Category]:
        """
        获取或创建分类

        Args:
            handle: 分类标识
            name: 分类名称

        Returns:
            Category: 分类对象
        """
        try:
            logger.info(f"🏷️ [分类处理] 处理分类: {handle} - {name}")

            # 查找现有分类
            category = self.db.query(Category).filter(Category.slug == handle).first()

            if not category:
                logger.info(f"🆕 [新建分类] 分类不存在，创建新分类")

                # 创建新分类
                category = Category(slug=handle)
                self.db.add(category)
                self.db.flush()  # 获取ID

                logger.info(f"📝 [分类主记录] slug: {handle}, 数据库ID: {category.id}")

                # 创建分类翻译
                translation = CategoryTranslation(
                    category_id=category.id,
                    locale='en',
                    name=name,
                    description=f"Category for {name}"
                )
                self.db.add(translation)

                logger.info(f"📝 [分类翻译] category_id: {category.id}, name: {name}")
                logger.info(f"✅ [新建分类] 创建成功: {handle} - {name}")
            else:
                logger.info(f"♻️ [已存在分类] 使用现有分类: {handle} - 数据库ID: {category.id}")

            return category
            
        except Exception as e:
            logger.error(f"创建分类时出错: {handle} - {str(e)}")
            return None
    
    def get_tool_by_id(self, tool_id: str) -> Optional[Tool]:
        """
        根据tool_id获取工具
        
        Args:
            tool_id: 工具ID
            
        Returns:
            Tool: 工具对象或None
        """
        return self.db.query(Tool).filter(Tool.tool_id == tool_id).first()
    
    def get_tools_count(self) -> int:
        """
        获取工具总数
        
        Returns:
            int: 工具总数
        """
        return self.db.query(Tool).count()
    
    def get_categories_count(self) -> int:
        """
        获取分类总数

        Returns:
            int: 分类总数
        """
        return self.db.query(Category).count()

    def get_tools_without_domain_registration_date(self, limit: int = 100) -> List[Tool]:
        """
        获取没有域名注册日期的工具列表

        Args:
            limit: 限制返回的工具数量

        Returns:
            List[Tool]: 工具列表
        """
        try:
            return self.db.query(Tool).filter(
                Tool.domain_registration_date.is_(None),
                Tool.url.isnot(None),
                Tool.url != ''
            ).limit(limit).all()
        except Exception as e:
            logger.error(f"查询没有域名注册日期的工具失败: {str(e)}")
            return []

    def update_tool_domain_registration_date(self, tool_id: str, registration_date) -> bool:
        """
        更新工具的域名注册日期

        Args:
            tool_id: 工具ID
            registration_date: 注册日期

        Returns:
            bool: 是否更新成功
        """
        try:
            tool = self.db.query(Tool).filter(Tool.tool_id == tool_id).first()
            if tool:
                tool.domain_registration_date = registration_date
                self.db.commit()
                logger.info(f"成功更新工具 {tool_id} 的域名注册日期: {registration_date}")
                return True
            else:
                logger.warning(f"未找到工具: {tool_id}")
                return False
        except Exception as e:
            logger.error(f"更新工具 {tool_id} 域名注册日期失败: {str(e)}")
            self.db.rollback()
            return False
