from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
import logging
import asyncio
from typing import Optional
from sqlalchemy.orm import Session

from app.core.database import SessionLocal
from app.services.toolify_service import ToolifyAPIService
from app.services.database_service import DatabaseService
from app.services.domain_registration_service import DomainRegistrationService

logger = logging.getLogger(__name__)

class SchedulerService:
    """
    定时任务调度服务
    """
    
    def __init__(self):
        self.scheduler: Optional[AsyncIOScheduler] = None
        self.is_running = False
    
    def start(self):
        """启动调度器"""
        if self.scheduler is not None:
            logger.warning("调度器已经在运行中")
            return
        
        try:
            self.scheduler = AsyncIOScheduler()
            
            # 添加定时任务
            self._add_jobs()
            
            # 启动调度器
            self.scheduler.start()
            self.is_running = True
            
            logger.info("✅ 定时任务调度器启动成功")
            
        except Exception as e:
            logger.error(f"❌ 启动调度器失败: {str(e)}")
            raise
    
    def stop(self):
        """停止调度器"""
        if self.scheduler is not None:
            try:
                self.scheduler.shutdown()
                self.scheduler = None
                self.is_running = False
                logger.info("✅ 定时任务调度器已停止")
            except Exception as e:
                logger.error(f"❌ 停止调度器失败: {str(e)}")
    
    def _add_jobs(self):
        """添加定时任务"""
        if not self.scheduler:
            return
        
        # 每日12点执行爬取任务
        self.scheduler.add_job(
            func=self._daily_crawl_task,
            trigger=CronTrigger(hour=12, minute=0),
            id='daily_crawl',
            name='每日工具数据爬取',
            replace_existing=True
        )
        
        # 每周日凌晨2点执行深度爬取任务
        self.scheduler.add_job(
            func=self._weekly_deep_crawl_task,
            trigger=CronTrigger(day_of_week=6, hour=2, minute=0),
            id='weekly_deep_crawl',
            name='每周深度工具数据爬取',
            replace_existing=True
        )

        # 每日凌晨1点执行域名注册日期更新任务
        self.scheduler.add_job(
            func=self._daily_domain_registration_update_task,
            trigger=CronTrigger(hour=1, minute=0),
            id='daily_domain_registration_update',
            name='每日域名注册日期更新',
            replace_existing=True
        )

        logger.info("定时任务已添加:")
        logger.info("  - 每日12点: 爬取2页工具数据")
        logger.info("  - 每周日2点: 深度爬取10页工具数据")
        logger.info("  - 每日1点: 更新域名注册日期")
    
    async def _daily_crawl_task(self):
        """每日爬取任务"""
        try:
            logger.info("🕐 开始执行每日工具数据爬取任务...")
            
            # 创建数据库会话
            db = SessionLocal()
            
            try:
                # 执行爬取
                await self._execute_crawl_task(
                    start_page=1,
                    max_pages=2,
                    per_page=20,
                    tool_type=1,
                    db=db,
                    task_name="每日爬取"
                )
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"❌ 每日爬取任务执行失败: {str(e)}")
    
    async def _weekly_deep_crawl_task(self):
        """每周深度爬取任务"""
        try:
            logger.info("🕐 开始执行每周深度工具数据爬取任务...")
            
            # 创建数据库会话
            db = SessionLocal()
            
            try:
                # 执行深度爬取
                await self._execute_crawl_task(
                    start_page=1,
                    max_pages=10,
                    per_page=28,
                    tool_type=1,
                    db=db,
                    task_name="每周深度爬取"
                )
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"❌ 每周深度爬取任务执行失败: {str(e)}")

    async def _daily_domain_registration_update_task(self):
        """每日域名注册日期更新任务"""
        try:
            logger.info("🕐 开始执行每日域名注册日期更新任务...")

            # 创建数据库会话
            db = SessionLocal()

            try:
                # 创建域名注册服务实例
                domain_service = DomainRegistrationService(db)

                # 首先确保数据库表有domain_registration_date字段
                logger.info("🔧 检查数据库表结构...")
                domain_service.add_domain_registration_date_column()

                # 执行批量更新
                logger.info("🚀 开始批量更新域名注册日期...")
                result = domain_service.batch_update_registration_dates(
                    batch_size=20,  # 每批处理20个工具
                    max_tools=50    # 每天最多处理50个工具
                )

                # 记录结果
                logger.info("🎉 每日域名注册日期更新任务完成:")
                logger.info(f"  - 总工具数: {result['total_tools']}")
                logger.info(f"  - 已处理: {result['processed']}")
                logger.info(f"  - 成功: {result['success']}")
                logger.info(f"  - 失败: {result['failed']}")
                logger.info(f"  - 成功率: {result.get('success_rate', 0)}%")

                # 记录错误分类统计
                if result.get('error_categories'):
                    logger.info("📊 错误分类统计:")
                    for category, count in result['error_categories'].items():
                        if count > 0:
                            logger.info(f"  - {category}: {count}")

                if result['errors']:
                    logger.warning("⚠️ 部分工具处理失败:")
                    for error in result['errors'][:3]:  # 只记录前3个错误
                        category = error.get('category', 'unknown')
                        logger.warning(f"  - {error.get('tool_id', 'Unknown')} [{category}]: {error.get('error', 'Unknown error')}")

                # 获取并记录总体统计
                try:
                    stats = domain_service.get_query_statistics()
                    logger.info("📈 总体进度统计:")
                    logger.info(f"  - 完成率: {stats.get('completion_rate', 0)}%")
                    logger.info(f"  - 已完成: {stats.get('tools_with_registration_date', 0)}")
                    logger.info(f"  - 待处理: {stats.get('tools_pending_update', 0)}")
                except Exception as e:
                    logger.warning(f"⚠️ 获取统计信息失败: {str(e)}")

            finally:
                db.close()

        except Exception as e:
            logger.error(f"❌ 每日域名注册日期更新任务执行失败: {str(e)}")
    
    async def _execute_crawl_task(
        self,
        start_page: int,
        max_pages: int,
        per_page: int,
        tool_type: int,
        db: Session,
        task_name: str
    ):
        """
        执行爬取任务
        
        Args:
            start_page: 起始页码
            max_pages: 最大页数
            per_page: 每页记录数
            tool_type: 工具类型
            db: 数据库会话
            task_name: 任务名称
        """
        try:
            # 创建服务实例
            toolify_service = ToolifyAPIService()
            db_service = DatabaseService(db)
            
            # 记录开始状态
            initial_count = db_service.get_tools_count()
            logger.info(f"{task_name} - 数据库当前工具数量: {initial_count}")
            
            # 获取工具数据
            logger.info(f"{task_name} - 开始获取工具数据...")
            tools = toolify_service.fetch_all_pages(
                start_page=start_page,
                max_pages=max_pages,
                per_page=per_page,
                tool_type=tool_type
            )
            
            if not tools:
                logger.warning(f"{task_name} - 未获取到任何工具数据")
                return
            
            logger.info(f"{task_name} - 成功获取 {len(tools)} 条工具数据")
            
            # 转换数据格式
            transformed_tools = []
            for tool in tools:
                transformed_tool = toolify_service.transform_tool_data(tool)
                transformed_tools.append(transformed_tool)
            
            # 保存到数据库
            logger.info(f"{task_name} - 开始保存数据到数据库...")
            saved_count, errors = db_service.save_tools_to_database(transformed_tools)
            
            # 记录结果
            final_count = db_service.get_tools_count()
            logger.info(f"{task_name} - 任务完成:")
            logger.info(f"  - 爬取工具数: {len(tools)}")
            logger.info(f"  - 成功保存数: {saved_count}")
            logger.info(f"  - 错误数量: {len(errors)}")
            logger.info(f"  - 数据库工具总数: {final_count}")
            
            if errors:
                logger.warning(f"{task_name} - 保存过程中出现错误:")
                for i, error in enumerate(errors[:3]):  # 只记录前3个错误
                    logger.warning(f"  {i+1}. {error}")
                if len(errors) > 3:
                    logger.warning(f"  ... 还有 {len(errors) - 3} 个错误")
            
        except Exception as e:
            logger.error(f"{task_name} - 执行失败: {str(e)}")
            raise
    
    def get_jobs(self):
        """获取所有任务信息"""
        if not self.scheduler:
            return []
        
        jobs = []
        for job in self.scheduler.get_jobs():
            jobs.append({
                'id': job.id,
                'name': job.name,
                'next_run_time': job.next_run_time.isoformat() if job.next_run_time else None,
                'trigger': str(job.trigger)
            })
        
        return jobs
    
    def get_status(self):
        """获取调度器状态"""
        return {
            'running': self.is_running,
            'scheduler_state': self.scheduler.state if self.scheduler else None,
            'jobs_count': len(self.scheduler.get_jobs()) if self.scheduler else 0
        }

# 全局调度器实例
scheduler_service = SchedulerService()

def get_scheduler() -> SchedulerService:
    """获取调度器服务实例"""
    return scheduler_service
