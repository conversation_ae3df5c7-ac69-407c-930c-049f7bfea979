"""
工具描述生成服务

主要业务逻辑服务，协调各个组件完成工具描述的自动生成，包括：
- 工具查询和筛选
- 内容生成协调
- 数据库更新
- 错误处理和重试
- 进度跟踪
"""

import time
import logging
from typing import Dict, Any, Optional, List, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import or_, func

from app.models.tool import Tool, ToolTranslation
from app.services.ollama_service import OllamaService
from app.services.content_generation_service import ContentGenerationService

logger = logging.getLogger(__name__)


class DescriptionGenerationService:
    """工具描述生成服务类"""
    
    def __init__(self, db: Session):
        """
        初始化描述生成服务
        
        Args:
            db: 数据库会话
        """
        self.db = db
        self.ollama_service = None
        self.content_generation_service = None
    
    def initialize_services(
        self, 
        ip_address: Optional[str] = None, 
        port: int = 11434
    ) -> bool:
        """
        初始化Ollama和内容生成服务
        
        Args:
            ip_address: Ollama服务器IP地址
            port: Ollama服务器端口
            
        Returns:
            bool: 是否初始化成功
        """
        try:
            # 初始化Ollama服务
            self.ollama_service = OllamaService(ip_address, port)
            
            # 初始化内容生成服务
            self.content_generation_service = ContentGenerationService(
                self.db, self.ollama_service
            )
            
            logger.info(f"服务初始化成功，使用Ollama服务器: {self.ollama_service.ip_address}:{self.ollama_service.port}")
            return True
            
        except Exception as e:
            logger.error(f"服务初始化失败: {str(e)}")
            return False
    
    def generate_descriptions(
        self,
        locale: str = 'en',
        model_name: str = 'gemma3:latest',
        fallback_model: str = 'deepseek-r1:latest',
        fields: str = 'long_description,usage_instructions,pricing_details,integration_info',
        limit: int = 10,
        max_chunks: int = 5,
        chunk_size: int = 2000,
        max_tokens: int = 2048,
        temperature: float = 0.7,
        dry_run: bool = False
    ) -> Dict[str, Any]:
        """
        批量生成工具描述
        
        Args:
            locale: 语言代码
            model_name: 主要模型名称
            fallback_model: 备用模型名称
            fields: 要生成的字段，逗号分隔
            limit: 处理的工具数量上限
            max_chunks: 最大文本块数量
            chunk_size: 文本块大小
            max_tokens: 最大令牌数
            temperature: 生成温度
            dry_run: 是否为测试模式
            
        Returns:
            Dict[str, Any]: 处理结果统计
        """
        if not self.ollama_service or not self.content_generation_service:
            return {
                "success": False,
                "error": "服务未初始化，请先调用 initialize_services()"
            }
        
        # 解析要生成的字段
        fields_to_generate = [field.strip() for field in fields.split(',') if field.strip()]
        
        logger.info(f"开始批量生成工具描述")
        logger.info(f"配置: locale={locale}, model={model_name}, fields={fields_to_generate}, limit={limit}")
        
        # 查找需要处理的工具翻译
        translations = self._find_tools_to_process(locale, fields_to_generate, limit)
        
        if not translations:
            return {
                "success": True,
                "message": f"没有找到需要生成描述的{locale}语言工具",
                "processed": 0,
                "succeeded": 0,
                "failed": 0,
                "errors": []
            }
        
        logger.info(f"找到 {len(translations)} 个需要处理的工具")
        
        if dry_run:
            return {
                "success": True,
                "message": f"DRY RUN模式：找到 {len(translations)} 个工具需要处理",
                "tools": [{"tool_id": t.tool_id, "name": t.name} for t in translations],
                "processed": 0,
                "succeeded": 0,
                "failed": 0,
                "errors": []
            }
        
        # 处理每个工具
        return self._process_tools(
            translations, fields_to_generate, model_name, fallback_model,
            max_chunks, chunk_size, max_tokens, temperature
        )
    
    def _find_tools_to_process(
        self, 
        locale: str, 
        fields_to_generate: List[str], 
        limit: int
    ) -> List[ToolTranslation]:
        """
        查找需要处理的工具翻译
        
        Args:
            locale: 语言代码
            fields_to_generate: 要生成的字段列表
            limit: 数量限制
            
        Returns:
            List[ToolTranslation]: 需要处理的工具翻译列表
        """
        try:
            # 构建查询条件：任何一个字段为空或None
            conditions = []
            for field in fields_to_generate:
                if hasattr(ToolTranslation, field):
                    field_attr = getattr(ToolTranslation, field)
                    conditions.append(field_attr.is_(None))
                    conditions.append(field_attr == '')
            
            if not conditions:
                logger.warning("没有有效的字段需要生成")
                return []
            
            # 查询需要处理的工具翻译
            query = self.db.query(ToolTranslation).filter(
                ToolTranslation.locale == locale,
                or_(*conditions)
            ).join(Tool).order_by(Tool.created_at)
            
            if limit > 0:
                query = query.limit(limit)
            
            return query.all()
            
        except Exception as e:
            logger.error(f"查询工具时出错: {str(e)}")
            return []
    
    def _process_tools(
        self,
        translations: List[ToolTranslation],
        fields_to_generate: List[str],
        model_name: str,
        fallback_model: str,
        max_chunks: int,
        chunk_size: int,
        max_tokens: int,
        temperature: float
    ) -> Dict[str, Any]:
        """
        处理工具列表
        
        Args:
            translations: 工具翻译列表
            fields_to_generate: 要生成的字段列表
            model_name: 主要模型名称
            fallback_model: 备用模型名称
            max_chunks: 最大文本块数量
            chunk_size: 文本块大小
            max_tokens: 最大令牌数
            temperature: 生成温度
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        processed = 0
        succeeded = 0
        failed = 0
        errors = []
        
        for translation in translations:
            try:
                tool = translation.tool
                logger.info(f"正在处理: {tool.tool_id} - {translation.name}")

                # 刷新数据库连接以避免超时
                try:
                    from sqlalchemy import text
                    self.db.execute(text("SELECT 1"))
                except Exception as conn_error:
                    logger.warning(f"数据库连接异常，尝试重新连接: {str(conn_error)}")
                    self.db.rollback()

                # 处理单个工具
                tool_result = self._process_single_tool(
                    translation, fields_to_generate, model_name, fallback_model,
                    max_chunks, chunk_size, max_tokens, temperature
                )

                processed += 1

                if tool_result["success"]:
                    succeeded += 1
                    logger.info(f"成功处理工具: {tool.tool_id}")
                else:
                    failed += 1
                    errors.extend(tool_result["errors"])
                    logger.error(f"处理工具失败: {tool.tool_id}")

                # 防止API限速和数据库连接超时
                time.sleep(5)

            except Exception as e:
                processed += 1
                failed += 1
                error_msg = f"处理工具 {translation.tool_id} 时发生异常: {str(e)}"
                logger.error(error_msg)
                errors.append(error_msg)

                # 回滚当前事务
                try:
                    self.db.rollback()
                except Exception:
                    pass
        
        return {
            "success": True,
            "message": f"批量处理完成: 处理 {processed} 个，成功 {succeeded} 个，失败 {failed} 个",
            "processed": processed,
            "succeeded": succeeded,
            "failed": failed,
            "errors": errors
        }
    
    def _process_single_tool(
        self,
        translation: ToolTranslation,
        fields_to_generate: List[str],
        model_name: str,
        fallback_model: str,
        max_chunks: int,
        chunk_size: int,
        max_tokens: int,
        temperature: float
    ) -> Dict[str, Any]:
        """
        处理单个工具
        
        Args:
            translation: 工具翻译对象
            fields_to_generate: 要生成的字段列表
            model_name: 主要模型名称
            fallback_model: 备用模型名称
            max_chunks: 最大文本块数量
            chunk_size: 文本块大小
            max_tokens: 最大令牌数
            temperature: 生成温度
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        tool = translation.tool
        success_fields = []
        failed_fields = []
        errors = []
        
        # 生成每个字段的内容
        for field in fields_to_generate:
            if not hasattr(translation, field):
                error_msg = f"字段 {field} 不存在，跳过"
                logger.warning(error_msg)
                failed_fields.append(field)
                errors.append(error_msg)
                continue
            
            # 检查字段是否需要生成(空或None)
            current_value = getattr(translation, field)
            if current_value and current_value.strip():
                logger.info(f"{tool.tool_id} 的 {field} 字段已有内容，跳过")
                continue
            
            # 生成内容
            logger.info(f"正在生成 {field} 内容...")
            try:
                content = self.content_generation_service.generate_tool_content(
                    tool, field, model_name, fallback_model,
                    max_chunks, chunk_size, max_tokens, temperature
                )
                
                if content:
                    # 设置字段值
                    setattr(translation, field, content)
                    success_fields.append(field)
                    logger.info(f"成功为 {tool.tool_id} 生成 {field}")
                else:
                    failed_fields.append(field)
                    error_msg = f"无法为 {tool.tool_id} 生成 {field}"
                    logger.error(error_msg)
                    errors.append(error_msg)
                
                # 防止API限速
                time.sleep(5)
                
            except Exception as e:
                failed_fields.append(field)
                error_msg = f"生成 {tool.tool_id} 的 {field} 时发生错误: {str(e)}"
                logger.error(error_msg)
                errors.append(error_msg)
        
        # 更新数据库 - 添加重试机制
        if success_fields:
            max_retries = 3
            retry_count = 0

            while retry_count < max_retries:
                try:
                    # 设置更新时间戳
                    translation.updated_at = func.now()

                    # 提交事务
                    self.db.commit()
                    logger.info(f"成功更新了 {tool.tool_id} 的以下字段: {', '.join(success_fields)}")
                    break

                except Exception as save_error:
                    retry_count += 1
                    self.db.rollback()

                    error_msg = f"保存数据时出错 (尝试 {retry_count}/{max_retries}): {str(save_error)}"
                    logger.warning(error_msg)

                    if retry_count >= max_retries:
                        # 最终失败
                        logger.error(f"保存数据最终失败: {tool.tool_id}")
                        errors.append(error_msg)
                        return {
                            "success": False,
                            "success_fields": [],
                            "failed_fields": fields_to_generate,
                            "errors": errors
                        }
                    else:
                        # 等待后重试
                        delay = 2 ** retry_count  # 指数退避
                        time.sleep(delay)
                        logger.info(f"等待 {delay} 秒后重试...")
        
        return {
            "success": len(success_fields) > 0,
            "success_fields": success_fields,
            "failed_fields": failed_fields,
            "errors": errors
        }
    
    def get_generation_status(self, locale: str = 'en') -> Dict[str, Any]:
        """
        获取生成状态统计
        
        Args:
            locale: 语言代码
            
        Returns:
            Dict[str, Any]: 状态统计信息
        """
        try:
            # 查询所有工具翻译
            total_tools = self.db.query(ToolTranslation).filter(
                ToolTranslation.locale == locale
            ).count()
            
            # 查询各字段的完成情况
            fields = ['long_description', 'usage_instructions', 'pricing_details', 'integration_info']
            field_stats = {}
            
            for field in fields:
                if hasattr(ToolTranslation, field):
                    field_attr = getattr(ToolTranslation, field)
                    completed = self.db.query(ToolTranslation).filter(
                        ToolTranslation.locale == locale,
                        field_attr.isnot(None),
                        field_attr != ''
                    ).count()
                    
                    field_stats[field] = {
                        "completed": completed,
                        "pending": total_tools - completed,
                        "completion_rate": round((completed / total_tools * 100), 2) if total_tools > 0 else 0
                    }
            
            return {
                "success": True,
                "total_tools": total_tools,
                "locale": locale,
                "field_statistics": field_stats,
                "ollama_service_available": self.ollama_service.is_available() if self.ollama_service else False
            }
            
        except Exception as e:
            logger.error(f"获取生成状态时出错: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_available_models(self) -> List[Dict[str, Any]]:
        """
        获取可用的Ollama模型列表
        
        Returns:
            List[Dict[str, Any]]: 可用模型列表
        """
        if not self.ollama_service:
            return []
        
        return self.ollama_service.list_available_models()
