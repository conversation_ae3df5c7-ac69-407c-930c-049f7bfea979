"""
内容生成服务

使用Ollama AI模型为工具生成详细描述和其他字段内容，包括：
- 长描述生成
- 使用说明生成
- 价格详情生成
- 集成信息生成
- 智能内容处理和优化
"""

import re
import time
import math
import logging
import datetime
from typing import Dict, Any, Optional, List
from urllib.parse import urlparse
from sqlalchemy.orm import Session

from app.models.tool import Tool, ToolTranslation
from app.services.ollama_service import OllamaService
from app.services.content_extractor_service import ContentExtractorService

logger = logging.getLogger(__name__)


class ContentGenerationService:
    """内容生成服务类"""
    
    def __init__(self, db: Session, ollama_service: OllamaService):
        """
        初始化内容生成服务
        
        Args:
            db: 数据库会话
            ollama_service: Ollama服务实例
        """
        self.db = db
        self.ollama_service = ollama_service
        self.content_extractor = ContentExtractorService()
    
    def generate_tool_content(
        self,
        tool: Tool,
        field_type: str,
        model_name: str = "llama3:latest",
        fallback_model: Optional[str] = None,
        max_chunks: int = 5,
        chunk_size: int = 2000,
        max_tokens: int = 2048,
        temperature: float = 0.7,
        retry_count: int = 0,
        max_retries: int = 3
    ) -> Optional[str]:
        """
        为工具生成指定字段的内容
        
        Args:
            tool: 工具对象
            field_type: 要生成的字段类型
            model_name: 使用的模型名称
            fallback_model: 备用模型名称
            max_chunks: 最大文本块数量
            chunk_size: 文本块大小
            max_tokens: 最大令牌数
            temperature: 生成温度
            retry_count: 当前重试次数
            max_retries: 最大重试次数
            
        Returns:
            Optional[str]: 生成的内容或None
        """
        try:
            # 检查数据库连接状态
            if not self.db.is_active:
                logger.warning("数据库连接已关闭，尝试重新连接")
                self.db.rollback()
            
            # 获取工具基本信息
            domain = self.content_extractor.get_domain_from_url(tool.url)
            
            # 获取英文翻译信息
            en_translation = self.db.query(ToolTranslation).filter(
                ToolTranslation.tool_id == tool.tool_id,
                ToolTranslation.locale == 'en'
            ).first()
            
            tool_name = en_translation.name if en_translation else tool.tool_id
            short_desc = en_translation.description if en_translation else ""
            
            # 清理URL
            parsed_url = urlparse(tool.url)
            clean_url = f"{parsed_url.scheme}://{parsed_url.netloc}"
            
            # 爬取网站信息
            website_data = self.content_extractor.extract_website_content(
                clean_url, max_chunks=max_chunks, chunk_size=chunk_size
            )
            
            # 如果网站爬取失败，使用基本描述
            if not website_data["chunks"]:
                logger.warning(f"无法从 {clean_url} 爬取内容，将使用基本描述")
                website_content = short_desc
            else:
                website_content = "\n\n".join(website_data["chunks"])
            
            # 根据字段类型生成提示词
            prompt = self._build_prompt(
                field_type, tool_name, short_desc, clean_url, 
                website_data, website_content
            )
            
            logger.debug(f"正在调用Ollama模型: {model_name}")
            
            # 处理多块内容
            if len(website_data["chunks"]) > 1:
                return self._generate_from_multiple_chunks(
                    tool_name, clean_url, website_data["chunks"], field_type,
                    model_name, prompt, max_tokens, temperature
                )
            else:
                # 直接处理单块内容
                return self._generate_from_single_content(
                    prompt, model_name, max_tokens, temperature,
                    fallback_model, retry_count, max_retries
                )
                
        except Exception as e:
            logger.error(f"生成描述时发生错误: {str(e)}")
            
            # 如果有备用模型且当前模型失败，尝试使用备用模型
            if fallback_model and fallback_model != model_name:
                logger.info(f"发生错误，尝试使用备用模型: {fallback_model}")
                return self.generate_tool_content(
                    tool, field_type, fallback_model, None, 
                    max_chunks, chunk_size, max_tokens, temperature
                )
            
            return None
    
    def _build_prompt(
        self, 
        field_type: str, 
        tool_name: str, 
        short_desc: str, 
        url: str,
        website_data: Dict[str, Any], 
        website_content: str
    ) -> str:
        """构建不同字段类型的提示词"""
        
        base_data = f"""Primary Data:
- Tool Name: {tool_name}
- Basic Description: {short_desc}
- Website: {url}
- Website Title: {website_data["title"]}
- Website Meta Description: {website_data["meta_description"]}"""
        
        if field_type == 'long_description':
            return f"""Task: Create a comprehensive, factual description of the AI tool "{tool_name}".

{base_data}

Content Guidelines:
1. Structure the description with a concise introduction followed by clearly defined sections
2. Focus exclusively on factual information from the provided content
3. Include ONLY the following key aspects:
   - Primary purpose and problem(s) the tool solves
   - Key features and specific capabilities (be precise about what it can do)
   - Target audience and use cases
   - Technical approach or methodology (if available)
   - Differentiators or unique selling points

Content Requirements:
- Keep the total length between 300-400 words
- Format using proper Markdown with ## headings and bullet points
- Ensure the content is factual and derived from the provided information
- Avoid marketing language, subjective claims, or speculation
- Do not include pricing information (this will be covered separately)
- Do not mention integration details (this will be covered separately)
- Do not list numbered steps (use bullet points instead)

Strictly Avoid:
- Do NOT include any footer content such as "Contact & Support", "Help Center", "Terms & Conditions", "Privacy Policy", etc.
- Do NOT include metadata like "(Word count: XXX)" or similar counters
- Do NOT include introductory phrases like "Here's a comprehensive description of X" or "built from source material"
- Do NOT include any navigational elements from the website
- Do NOT list social media links or contact information
- Do NOT include legal disclaimers or copyright notices
- Start your content directly with the actual description without any prefix or meta-comments

Website Content:
{website_content}

Important: Focus strictly on creating a factual, well-structured description. Do not include any irrelevant information, opinions, or content not supported by the source material. Begin directly with the actual content without any introductory statements about what you're doing."""

        elif field_type == 'usage_instructions':
            return f"""Task: Create clear, step-by-step usage instructions for the AI tool "{tool_name}".

{base_data}

Content Guidelines:
1. Structure the instructions in a logical sequence from setup to advanced usage
2. Focus exclusively on actionable steps users need to follow
3. Include ONLY the following key aspects:
   - Getting started (account creation, setup process)
   - Basic usage workflow with specific steps
   - Key features and how to access them
   - Best practices and optimization tips
   - Common issues and troubleshooting (if available)
   - Limitations users should be aware of

Content Requirements:
- Keep the total length between 300-400 words
- Format using proper Markdown with ## headings and bullet points
- Use **bold text** for UI elements, buttons, or important terms
- Provide clear, sequential steps that are easy to follow
- Include specific examples where possible
- Avoid vague instructions; be specific and actionable
- Do not include pricing information (covered elsewhere)
- Do not include general marketing information

Website Content:
{website_content}

Important: Focus strictly on practical, actionable instructions that help users effectively use the tool. Do not include any marketing language, irrelevant information, or speculative content."""

        elif field_type == 'pricing_details':
            return f"""Task: Create a detailed, accurate pricing overview for the AI tool "{tool_name}".

{base_data}

Content Guidelines:
1. Structure the pricing information in a clear, organized format
2. Focus exclusively on factual pricing data from the provided content
3. Include ONLY the following key aspects:
   - Available pricing tiers/plans with exact costs (if available)
   - Free tier or trial details (duration, limitations)
   - Subscription models (monthly/annual) with specific pricing
   - Usage-based pricing models (if applicable)
   - Enterprise or custom pricing options
   - Specific features included in each pricing tier
   - Discount information (if available)

Content Requirements:
- Keep the total length between 300-400 words
- Format using proper Markdown with ## headings and bullet points
- Use **bold text** for plan names and prices
- Create simple tables for pricing comparisons if appropriate
- Be exact with numbers, currencies, and time periods
- If precise pricing isn't available, clearly indicate this
- DO NOT make up or estimate prices not found in the source material
- Clearly distinguish between one-time payments and recurring subscriptions

Website Content:
{website_content}

Important: Focus strictly on providing accurate pricing information based solely on the provided content. If specific pricing details are not available, clearly state this rather than making assumptions."""

        elif field_type == 'integration_info':
            return f"""Task: Create a detailed technical integration overview for the AI tool "{tool_name}".

{base_data}

Content Guidelines:
1. Structure the integration information in a technical, developer-focused format
2. Focus exclusively on factual integration data from the provided content
3. Include ONLY the following key aspects:
   - Available API endpoints and functionality
   - Supported integration methods (REST API, GraphQL, SDKs, etc.)
   - Authentication requirements and methods
   - Specific platforms and services it integrates with
   - Technical requirements and dependencies
   - Integration complexity level
   - Rate limits or usage quotas
   - Documentation resources and example endpoints

Content Requirements:
- Keep the total length between 300-400 words
- Format using proper Markdown with ## headings and bullet points
- Include code snippets or endpoint examples if available
- Be specific about programming languages and frameworks supported
- Use technical terminology appropriate for developers
- Maintain factual accuracy based solely on provided information
- DO NOT include speculative information not supported by the source
- Focus on technical details rather than marketing content

Website Content:
{website_content}

Important: Focus strictly on providing accurate technical integration information that would be useful to developers. Avoid marketing language and focus on specific, actionable technical details."""

        else:
            return f"""Task: Create detailed information about {field_type} for the AI tool "{tool_name}".

{base_data}

Content Guidelines:
1. Structure the information in a clear, organized format
2. Focus exclusively on factual data from the provided content
3. Be specific, detailed, and accurate
4. Avoid marketing language or subjective claims
5. Include only relevant information directly related to {field_type}

Content Requirements:
- Keep the total length between 300-400 words
- Format using proper Markdown with ## headings and bullet points
- Use **bold text** for important terms or highlights
- Ensure all information is factually accurate and from the source
- DO NOT include speculative information or details not found in the source
- Focus on providing valuable, actionable information

Website Content:
{website_content}

Important: Create high-quality, factual content focused specifically on {field_type}. Avoid any irrelevant information or content not supported by the source material."""
    
    def _generate_from_multiple_chunks(
        self,
        tool_name: str,
        url: str,
        chunks: List[str],
        field_type: str,
        model_name: str,
        final_prompt: str,
        max_tokens: int,
        temperature: float
    ) -> Optional[str]:
        """处理多个文本块的内容生成"""
        logger.info(f"网站内容较多，分块处理 {len(chunks)} 个块")
        
        # 处理每个文本块
        summaries = []
        for i, chunk in enumerate(chunks):
            logger.info(f"处理文本块 {i+1}/{len(chunks)}")
            
            # 限制块大小，防止请求过大
            if len(chunk) > 8000:
                chunk = self._truncate_text_for_api(chunk, 8000)
                logger.info(f"文本块 {i+1} 被截断至 8000 字符")
            
            # 为每个块创建提示词
            chunk_prompt = self._build_chunk_extraction_prompt(tool_name, url, chunk)
            
            try:
                # 检查请求大小并处理
                if self._estimate_tokens(chunk_prompt) > 10000:
                    logger.warning(f"块 {i+1} 的提示词过大，估计超过 10000 tokens")
                    chunk_prompt = self._handle_content_too_large(chunk_prompt, 10000)
                
                # 调用Ollama API处理每个块
                system_prompt = "You are a precise information extraction assistant that focuses solely on extracting factual, specific details from content while maintaining accuracy and avoiding speculation."
                
                summary = self.ollama_service.call_ollama(
                    prompt=chunk_prompt,
                    model=model_name,
                    system_prompt=system_prompt,
                    temperature=0.3,
                    max_tokens=500
                )
                
                # 检查返回结果是否包含错误
                if isinstance(summary, dict) and "error" in summary:
                    logger.error(f"调用Ollama API失败: {summary['error']}")
                    continue
                
                if summary and summary.strip():
                    summaries.append(summary.strip())
                
                # 防止API限速
                time.sleep(3)
                
            except Exception as chunk_error:
                logger.error(f"处理文本块 {i+1} 时发生错误: {str(chunk_error)}")
                continue
        
        # 如果成功获取了摘要，使用它们生成最终内容
        if summaries:
            logger.info(f"成功提取了 {len(summaries)} 个摘要，正在生成最终内容")
            return self._integrate_summaries(summaries, final_prompt, model_name, max_tokens, temperature)
        
        logger.warning("摘要处理失败，回退到直接处理")
        return None
    
    def _build_chunk_extraction_prompt(self, tool_name: str, url: str, chunk: str) -> str:
        """构建文本块提取提示词"""
        return f"""Task: Extract and summarize key information about the AI tool "{tool_name}" from the following website content.

Website: {url}

Focus on extracting ONLY the following specific information:
1. What specific problem does this tool solve? (Be precise)
2. What are its exact capabilities and features? (List specific functionalities)
3. Who is the target audience? (Be specific about user types)
4. What is the pricing model? (Include specific tiers and costs if available)
5. What integration capabilities does it offer? (List specific platforms, APIs, etc.)
6. What unique advantages does it offer? (Focus on technical differentiators)

Website Content:
{chunk}

Requirements:
- Focus only on factual information present in the content
- Extract specific details, not general descriptions
- Format the information using Markdown with clear headings
- If certain information is not available, explicitly note this
- Do not include marketing language or subjective claims
- Do not add information not present in the source

Important: This extraction will be used for technical documentation, so accuracy and specificity are essential."""
    
    def _integrate_summaries(
        self,
        summaries: List[str],
        final_prompt: str,
        model_name: str,
        max_tokens: int,
        temperature: float
    ) -> Optional[str]:
        """整合摘要生成最终内容"""
        # 限制摘要总长度
        combined_summaries = " ".join(summaries)
        if len(combined_summaries) > 8000:
            combined_summaries = self._truncate_text_for_api(combined_summaries, 8000)
            logger.info(f"合并的摘要被截断至 8000 字符")
        
        # 构建用于整合摘要的提示词
        integration_prompt = f"""Task: Create a cohesive, well-structured document by synthesizing the following extracted information.

Extracted Information:
{combined_summaries}

Additional Context:
{final_prompt}

Requirements:
1. Create a cohesive, logically structured document that flows naturally
2. Organize information into clear sections with appropriate ## headings
3. Consolidate duplicate information and resolve any contradictions
4. Prioritize specific, factual details over general statements
5. Format with proper Markdown including headings, bullet points, and emphasis
6. Ensure the final document is 300-400 words
7. Maintain objectivity and factual accuracy throughout
8. Do not include information not supported by the provided content
9. Avoid marketing language and subjective claims

Important: You are creating official documentation, so accuracy, clarity and professionalism are essential. Focus only on information present in the provided content."""
        
        try:
            # 检查请求大小
            if self._estimate_tokens(integration_prompt) > 10000:
                logger.warning("最终整合提示词过大，截断内容以适应token限制")
                integration_prompt = self._handle_content_too_large(integration_prompt, 10000)
            
            # 调用Ollama API生成最终内容
            system_prompt = "You are a technical documentation specialist who creates clear, factual, and well-structured content based strictly on provided information, without adding speculation or marketing language."
            
            final_content = self.ollama_service.call_ollama(
                prompt=integration_prompt,
                model=model_name,
                system_prompt=system_prompt,
                temperature=temperature,
                max_tokens=max_tokens
            )
            
            # 检查返回结果是否包含错误
            if isinstance(final_content, dict) and "error" in final_content:
                logger.error(f"调用Ollama API失败: {final_content['error']}")
                return None
            
            if final_content and isinstance(final_content, str) and final_content.strip():
                return final_content.strip()
                
        except Exception as final_error:
            logger.error(f"生成最终内容时发生错误: {str(final_error)}")
        
        return None
    
    def _generate_from_single_content(
        self,
        prompt: str,
        model_name: str,
        max_tokens: int,
        temperature: float,
        fallback_model: Optional[str],
        retry_count: int,
        max_retries: int
    ) -> Optional[str]:
        """处理单个内容的生成"""
        logger.info("直接调用Ollama API生成内容")
        
        try:
            # 检查请求大小
            if self._estimate_tokens(prompt) > 10000:
                logger.warning("直接提示词过大，截断内容以适应token限制")
                prompt = self._handle_content_too_large(prompt, 10000)
            
            system_prompt = "You are a technical documentation specialist who creates clear, factual, and well-structured content based strictly on provided information, without adding speculation or marketing language."
            
            content = self.ollama_service.call_ollama(
                prompt=prompt,
                model=model_name,
                system_prompt=system_prompt,
                temperature=temperature,
                max_tokens=max_tokens
            )
            
            # 检查返回结果是否包含错误
            if isinstance(content, dict) and "error" in content:
                logger.error(f"调用Ollama API失败: {content['error']}")
                
                # 尝试使用备用模型
                if fallback_model and fallback_model != model_name:
                    logger.info(f"尝试使用备用模型: {fallback_model}")
                    return self._generate_from_single_content(
                        prompt, fallback_model, max_tokens, temperature,
                        None, retry_count, max_retries
                    )
                elif retry_count < max_retries:
                    logger.warning(f"等待后重试 ({retry_count+1}/{max_retries})")
                    time.sleep(10)
                    return self._generate_from_single_content(
                        prompt, model_name, max_tokens, temperature,
                        fallback_model, retry_count + 1, max_retries
                    )
                return None
            
            if content and isinstance(content, str) and content.strip():
                return content.strip()
                
        except Exception as direct_error:
            logger.error(f"直接生成内容时发生错误: {str(direct_error)}")
        
        # 如果没有内容或内容为空
        logger.warning(f"模型 {model_name} 返回了空内容")
        
        # 如果有备用模型且当前模型失败，尝试使用备用模型
        if fallback_model and fallback_model != model_name:
            logger.info(f"尝试使用备用模型: {fallback_model}")
            return self._generate_from_single_content(
                prompt, fallback_model, max_tokens, temperature,
                None, retry_count, max_retries
            )
        
        return None
    
    def _truncate_text_for_api(self, text: str, max_chars: int = 8000) -> str:
        """截断文本至适合API请求的大小"""
        if len(text) <= max_chars:
            return text
            
        logger.warning(f"文本过长 ({len(text)} 字符)，截断至 {max_chars} 字符")
        
        # 截断文本，尝试在句子边界截断
        truncated_text = text[:max_chars]
        
        # 尝试找到最后一个完整句子
        last_sentence_end = max(
            truncated_text.rfind('. '), 
            truncated_text.rfind('? '), 
            truncated_text.rfind('! ')
        )
        
        if last_sentence_end > max_chars * 0.7:
            truncated_text = truncated_text[:last_sentence_end + 1]
        
        return truncated_text
    
    def _estimate_tokens(self, text: str) -> int:
        """估计文本的token数量（粗略估计，每4个字符约为1个token）"""
        return len(text) // 4
    
    def _handle_content_too_large(self, content: str, max_tokens: int = 10000) -> str:
        """处理内容过大的问题"""
        # 估计token数量
        estimated_tokens = self._estimate_tokens(content)
        
        if estimated_tokens <= max_tokens:
            return content
            
        # 计算需要截断到的字符长度
        max_chars = (max_tokens * 4) - 500  # 留出一些余量
        
        logger.warning(f"内容过大，估计 {estimated_tokens} tokens，截断至约 {max_tokens} tokens ({max_chars} 字符)")
        
        return self._truncate_text_for_api(content, max_chars)
