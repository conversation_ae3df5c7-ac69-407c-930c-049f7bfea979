import requests
import logging
from typing import Dict, List, Any, Optional
import re
from urllib.parse import urlparse
import time
from app.utils.domain_utils import DomainInfoUtils

logger = logging.getLogger(__name__)

class ToolifyAPIService:
    """
    爬取Toolify.ai API数据的服务类
    """
    
    BASE_URL = "https://www.toolify.ai/self-api/v1/tools/search/list-search-v1"
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36',
            'Accept': 'application/json',
            'Referer': 'https://www.toolify.ai/',
        })
    
    def fetch_tools(self, page: int = 1, per_page: int = 28, tool_type: int = 1, retry_count: int = 0) -> Dict[str, Any]:
        """
        从Toolify API获取工具列表

        参数:
            page (int): 页码，从1开始
            per_page (int): 每页记录数
            tool_type (int): 工具类型
            retry_count (int): 当前重试次数

        返回:
            Dict[str, Any]: API响应数据
        """
        params = {
            'type': tool_type,
            'page': page,
            'per_page': per_page
        }

        try:
            logger.info(f"🚀 [API请求] 开始请求Toolify API")
            logger.info(f"📋 [请求入参] URL: {self.BASE_URL}")
            logger.info(f"📋 [请求入参] 参数: {params}")
            logger.info(f"📋 [请求入参] 页码={page}, 每页={per_page}, 类型={tool_type}, 重试次数={retry_count}")

            response = self.session.get(self.BASE_URL, params=params, timeout=30)

            logger.info(f"📡 [HTTP响应] 状态码: {response.status_code}")
            logger.info(f"📡 [HTTP响应] 响应头: {dict(response.headers)}")
            logger.info(f"📡 [HTTP响应] 响应大小: {len(response.content)} bytes")
            
            # 检查HTTP状态码
            if response.status_code == 429:
                # 处理429 Too Many Requests
                wait_time = 10 * (2 ** retry_count)  # 指数退避
                logger.warning(f"遇到429错误，等待 {wait_time} 秒后重试 (尝试 {retry_count+1}/3)")
                
                if retry_count < 3:
                    time.sleep(wait_time)
                    return self.fetch_tools(page, per_page, tool_type, retry_count + 1)
                else:
                    return {
                        'success': False,
                        'message': f"请求频率过高，已重试3次仍失败",
                        'data': None
                    }
            
            elif response.status_code != 200:
                logger.error(f"API请求失败，状态码: {response.status_code}, 响应: {response.text}")
                return {
                    'success': False,
                    'message': f"API请求失败，状态码: {response.status_code}",
                    'data': None
                }
            
            # 解析JSON响应
            data = response.json()

            logger.info(f"📦 [API响应] JSON数据结构: {list(data.keys()) if isinstance(data, dict) else type(data)}")

            # 验证响应数据结构
            if not isinstance(data, dict) or 'data' not in data:
                logger.error(f"❌ [API错误] 响应格式异常: {data}")
                return {
                    'success': False,
                    'message': "API响应格式异常",
                    'data': None
                }

            # 详细记录响应数据
            response_data = data.get('data', {})
            tools_data = response_data.get('data', []) if isinstance(response_data, dict) else []
            tools_count = len(tools_data)

            logger.info(f"✅ [API成功] 页码={page}, 工具数量={tools_count}")
            logger.info(f"📊 [响应统计] 总分类数: {data.get('category_count', 0)}")
            logger.info(f"📊 [响应统计] 总记录数: {response_data.get('total', 0) if isinstance(response_data, dict) else 0}")
            logger.info(f"📊 [响应统计] 登录状态: {response_data.get('is_logged_in', False) if isinstance(response_data, dict) else False}")

            # 打印前3个工具的基本信息
            if tools_data and len(tools_data) > 0:
                logger.info(f"🔍 [工具预览] 前3个工具:")
                for i, tool in enumerate(tools_data[:3]):
                    if isinstance(tool, dict):
                        logger.info(f"  {i+1}. ID: {tool.get('id', 'N/A')}, 名称: {tool.get('name', 'N/A')}, URL: {tool.get('website', 'N/A')}")

            return {
                'success': True,
                'message': "数据获取成功",
                'data': data
            }
            
        except requests.RequestException as e:
            logger.error(f"请求Toolify API出错: {str(e)}")
            
            # 对于连接错误等，也可以尝试重试
            if retry_count < 3:
                wait_time = 5 * (2 ** retry_count)
                logger.warning(f"网络请求错误，等待 {wait_time} 秒后重试 (尝试 {retry_count+1}/3)")
                time.sleep(wait_time)
                return self.fetch_tools(page, per_page, tool_type, retry_count + 1)
                
            return {
                'success': False,
                'message': f"请求错误: {str(e)}",
                'data': None
            }
        except ValueError as e:
            logger.error(f"解析JSON响应出错: {str(e)}")
            return {
                'success': False,
                'message': f"JSON解析错误: {str(e)}",
                'data': None
            }
    
    def fetch_all_pages(self, start_page: int = 1, max_pages: int = 10, per_page: int = 28, tool_type: int = 1) -> List[Dict[str, Any]]:
        """
        获取多页工具数据
        
        参数:
            start_page (int): 起始页码
            max_pages (int): 最大页数
            per_page (int): 每页记录数
            tool_type (int): 工具类型
            
        返回:
            List[Dict[str, Any]]: 所有获取到的工具数据列表
        """
        all_tools = []
        current_page = start_page
        
        while current_page < start_page + max_pages:
            result = self.fetch_tools(page=current_page, per_page=per_page, tool_type=tool_type)
            
            if not result['success'] or not result.get('data'):
                # 没有更多数据或发生错误时停止
                break

            # 正确的数据访问路径：result['data']['data']['data']
            response_data = result['data'].get('data', {})
            if not response_data or not isinstance(response_data, dict):
                break

            page_tools = response_data.get('data', [])
            all_tools.extend(page_tools)
            
            # 检查是否还有下一页
            if not page_tools or len(page_tools) < per_page:
                break
                
            current_page += 1
            
            # 每页之间添加短暂延迟，避免触发429
            time.sleep(1)
            
        return all_tools

    def transform_tool_data(self, tool_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        将Toolify API返回的工具数据转换为我们的数据库格式

        参数:
            tool_data (Dict[str, Any]): Toolify API返回的单个工具数据

        返回:
            Dict[str, Any]: 转换后符合我们数据库结构的数据
        """
        logger.info(f"🔄 [数据转换] 开始转换工具数据")
        logger.info(f"📥 [原始数据] 工具ID: {tool_data.get('id', 'N/A')}")
        logger.info(f"📥 [原始数据] 工具名称: {tool_data.get('name', 'N/A')}")
        logger.info(f"📥 [原始数据] 网站: {tool_data.get('website', 'N/A')}")
        logger.info(f"📥 [原始数据] 图标: {tool_data.get('image', 'N/A')}")
        logger.info(f"📥 [原始数据] 收藏数: {tool_data.get('collected_count', 0)}")
        logger.info(f"📥 [原始数据] 月访问数: {tool_data.get('month_visited_count', 0)}")
        logger.info(f"📥 [原始数据] 是否广告: {tool_data.get('is_ad', False)}")
        logger.info(f"📥 [原始数据] 是否推荐: {tool_data.get('is_recommend_now', 0)}")
        logger.info(f"📥 [原始数据] 是否显眼: {tool_data.get('is_noticeable', 0)}")
        logger.info(f"📥 [原始数据] 分类数量: {len(tool_data.get('categories', []))}")

        # 获取基本信息
        original_id = str(tool_data.get("id", ""))
        tool_name = tool_data.get("name", "").strip()
        description = (tool_data.get("description", "") or tool_data.get("what_is_summary", "")).strip()

        # 生成tool_id：使用工具名称的slug形式
        tool_id = self._generate_tool_id(tool_name, original_id)

        # 处理评分
        collected_count = tool_data.get("collected_count", 0)
        month_visited_count = tool_data.get("month_visited_count", 0)
        rating = 0.0
        if collected_count > 0:
            rating = min(float(collected_count) / 100.0, 5.0)  # 转换为5分制
        elif month_visited_count > 0:
            rating = min(float(month_visited_count) / 1000.0, 5.0)  # 转换为5分制

        # 处理定价类型
        pricing_type = "free"
        if tool_data.get("is_ad", False):
            pricing_type = "premium"

        # 处理分类信息
        categories = []
        if "categories" in tool_data and isinstance(tool_data["categories"], list):
            for cat in tool_data["categories"]:
                if isinstance(cat, dict):
                    categories.append({
                        "handle": cat.get("handle", ""),
                        "name": cat.get("name", "")
                    })

        # 处理URL
        url = tool_data.get('website', '')
        clean_url = url
        if url:
            try:
                # 替换toolify为aistak（如果需要）
                url = url.replace("toolify", "aistak")
                parsed_url = urlparse(url)
                clean_url = f"{parsed_url.scheme}://{parsed_url.netloc}"
            except Exception as e:
                logger.error(f"URL标准化处理失败: {url}, 错误: {str(e)}")
                clean_url = url

        # 处理域名信息
        domain_registration_date = None
        if clean_url:
            try:
                domain = urlparse(clean_url).netloc
                if domain:
                    result = DomainInfoUtils.get_domain_registration_date(domain)
                    # 只有当结果是datetime对象时才使用，否则设为None
                    if hasattr(result, 'year'):  # 检查是否是datetime对象
                        domain_registration_date = result
                    else:
                        domain_registration_date = None
            except Exception as e:
                logger.warning(f"获取域名注册信息失败: {clean_url}, 错误: {str(e)}")
                domain_registration_date = None

        # 返回标准化的工具数据
        transformed_data = {
            'tool_id': tool_id,
            'original_id': original_id,
            'name': tool_name,
            'description': description,
            'icon_url': tool_data.get('image', ''),
            'url': clean_url,
            'pricing_type': pricing_type,
            'is_premium': bool(tool_data.get('is_ad', False)),
            'is_new': bool(tool_data.get('is_noticeable', 0) == 1),
            'is_featured': bool(tool_data.get('is_recommend_now', 0)),
            'rating': round(rating, 1),
            'categories': categories,
            'domain_registration_date': domain_registration_date,
            # 附加数据
            'collected_count': collected_count,
            'month_visited_count': month_visited_count,
            'handle': tool_data.get('handle', ''),
            'source': 'toolify.ai',
            'locale': 'en'
        }

        logger.info(f"✅ [转换完成] 工具ID: {tool_id}")
        logger.info(f"📤 [转换结果] 名称: {tool_name}")
        logger.info(f"📤 [转换结果] URL: {clean_url}")
        logger.info(f"📤 [转换结果] 定价类型: {pricing_type}")
        logger.info(f"📤 [转换结果] 评分: {round(rating, 1)}")
        logger.info(f"📤 [转换结果] 是否付费: {bool(tool_data.get('is_ad', False))}")
        logger.info(f"📤 [转换结果] 是否新工具: {bool(tool_data.get('is_noticeable', 0) == 1)}")
        logger.info(f"📤 [转换结果] 是否特色: {bool(tool_data.get('is_recommend_now', 0))}")
        logger.info(f"📤 [转换结果] 分类数量: {len(categories)}")
        if categories:
            logger.info(f"📤 [转换结果] 分类列表: {[cat.get('handle', '') for cat in categories]}")
        logger.info(f"📤 [转换结果] 域名注册时间: {domain_registration_date}")

        return transformed_data

    def _generate_tool_id(self, tool_name: str, original_id: str) -> str:
        """
        生成工具ID

        参数:
            tool_name (str): 工具名称
            original_id (str): 原始ID

        返回:
            str: 生成的工具ID
        """
        if not tool_name:
            return f"tool_{original_id}"

        # 将工具名称转换为slug格式
        slug = re.sub(r'[^\w\s-]', '', tool_name.lower())
        slug = re.sub(r'[-\s]+', '-', slug).strip('-')

        # 如果slug为空或太短，使用原始ID
        if not slug or len(slug) < 2:
            return f"tool_{original_id}"

        # 限制长度
        if len(slug) > 40:
            slug = slug[:40].rstrip('-')

        return slug
