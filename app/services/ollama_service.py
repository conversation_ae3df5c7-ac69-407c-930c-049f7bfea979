"""
Ollama服务模块

提供与Ollama API交互的核心功能，包括：
- 服务器健康检查
- 模型调用
- 连接管理
- 错误处理和重试机制
"""

import socket
import requests
import time
import logging
from typing import Dict, Any, Optional, Union, List
from urllib.parse import urlparse

logger = logging.getLogger(__name__)


class OllamaService:
    """Ollama服务类，封装所有Ollama相关操作"""
    
    def __init__(self, ip_address: Optional[str] = None, port: int = 11434):
        """
        初始化Ollama服务
        
        Args:
            ip_address: Ollama服务器IP地址，None时自动查找
            port: Ollama服务器端口
        """
        self.ip_address = ip_address
        self.port = port
        self.server_config = None
        
        # 如果未指定IP地址，自动查找可用服务器
        if ip_address is None:
            self._find_available_server()
        else:
            # 验证指定的服务器是否可用
            if not self.check_ollama_health(ip_address, port):
                logger.warning(f"指定的服务器 {ip_address}:{port} 不可用，尝试自动查找")
                self._find_available_server()
            else:
                self.ip_address = ip_address
                self.port = port
    
    def _find_available_server(self) -> None:
        """查找可用的Ollama服务器"""
        servers = [
            {"ip_address": "127.0.0.1", "port": self.port},
            {"ip_address": "localhost", "port": self.port},
            {"ip_address": "host.docker.internal", "port": self.port},
        ]
        
        self.server_config = self.find_available_ollama_server(servers)
        if self.server_config:
            self.ip_address = self.server_config["ip_address"]
            self.port = self.server_config["port"]
            logger.info(f"找到可用的Ollama服务器: {self.ip_address}:{self.port}")
        else:
            logger.error("无法找到可用的Ollama服务器")
            raise ConnectionError("无法连接到任何Ollama服务器")
    
    @staticmethod
    def check_host_availability(ip_address: str, port: int, timeout: int = 3) -> bool:
        """
        检查主机和端口是否可达
        
        Args:
            ip_address: IP地址
            port: 端口号
            timeout: 超时时间（秒）
            
        Returns:
            bool: 是否可达
        """
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(timeout)
            result = sock.connect_ex((ip_address, port))
            sock.close()
            return result == 0
        except Exception:
            return False
    
    @staticmethod
    def check_ollama_health(ip_address: str, port: int = 11434, timeout: int = 3) -> bool:
        """
        检查Ollama服务是否可用
        
        Args:
            ip_address: Ollama服务器的IP地址
            port: Ollama服务器的端口号
            timeout: 请求超时时间（秒）
            
        Returns:
            bool: 服务是否可用
        """
        # 先检查基本网络连接
        if not OllamaService.check_host_availability(ip_address, port, timeout=1):
            return False
            
        try:
            # 尝试访问 API 列表接口
            url = f"http://{ip_address}:{port}/api/tags"
            response = requests.get(url, timeout=timeout)
            return response.status_code == 200
        except Exception:
            return False
    
    @staticmethod
    def find_available_ollama_server(servers: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """
        从服务器列表中找到第一个可用的Ollama服务器
        
        Args:
            servers: 服务器配置列表，每个元素包含ip_address和port
            
        Returns:
            Dict: 可用的服务器配置，如果没有找到则返回None
        """
        for server in servers:
            ip_address = server.get("ip_address")
            port = server.get("port", 11434)
            
            if OllamaService.check_ollama_health(ip_address, port):
                logger.info(f"找到可用的Ollama服务器: {ip_address}:{port}")
                return server
        
        logger.error("没有找到可用的Ollama服务器")
        return None
    
    def call_ollama(
        self,
        prompt: str,
        model: str = "llama3:latest",
        system_prompt: Optional[str] = None,
        temperature: float = 0.7,
        top_p: float = 0.9,
        max_tokens: int = 2048,
        stream: bool = False,
        max_retries: int = 3,
        retry_delay: int = 2,
        timeout: int = 300,
    ) -> Union[str, Dict[str, Any]]:
        """
        调用Ollama API生成文本
        
        Args:
            prompt: 用户输入的提示文本
            model: 要使用的模型名称
            system_prompt: 系统提示文本
            temperature: 生成文本的随机性，范围0-1
            top_p: 核采样概率，范围0-1
            max_tokens: 生成的最大令牌数
            stream: 是否使用流式响应
            max_retries: 最大重试次数
            retry_delay: 重试之间的延迟（秒）
            timeout: 请求超时时间（秒）
            
        Returns:
            Union[str, Dict[str, Any]]: 生成的文本或错误信息
        """
        if not self.ip_address:
            return {"error": "Ollama服务不可用，请检查服务是否启动"}
        
        url = f"http://{self.ip_address}:{self.port}/api/generate"
        
        payload = {
            "model": model,
            "prompt": prompt,
            "stream": stream,
        }

        # 添加可选参数
        if temperature != 0.7:  # 只有非默认值时才添加
            payload["temperature"] = temperature

        # 添加系统提示（如果提供）
        if system_prompt:
            payload["system"] = system_prompt
        
        for attempt in range(max_retries + 1):
            try:
                logger.debug(f"调用Ollama API (尝试 {attempt + 1}/{max_retries + 1})")
                
                response = requests.post(url, json=payload, timeout=timeout)
                response.raise_for_status()
                
                if stream:
                    return response
                else:
                    # 处理非流式响应
                    try:
                        json_response = response.json()
                        if 'response' in json_response:
                            return json_response['response'].strip()
                        else:
                            return "生成内容为空"
                    except Exception as e:
                        logger.error(f"解析响应JSON失败: {str(e)}")
                        return {"error": f"解析响应失败: {str(e)}"}
                    
            except requests.exceptions.Timeout:
                error_msg = f"请求超时 (尝试 {attempt + 1}/{max_retries + 1})"
                logger.warning(error_msg)
                if attempt < max_retries:
                    time.sleep(retry_delay * (attempt + 1))
                    continue
                return {"error": f"请求超时，已重试 {max_retries} 次"}
                
            except requests.exceptions.ConnectionError:
                error_msg = f"连接错误 (尝试 {attempt + 1}/{max_retries + 1})"
                logger.warning(error_msg)
                if attempt < max_retries:
                    time.sleep(retry_delay * (attempt + 1))
                    continue
                return {"error": f"连接错误，已重试 {max_retries} 次"}
                
            except requests.exceptions.RequestException as e:
                error_msg = f"请求异常: {str(e)} (尝试 {attempt + 1}/{max_retries + 1})"
                logger.error(error_msg)
                if attempt < max_retries:
                    time.sleep(retry_delay * (attempt + 1))
                    continue
                return {"error": f"请求异常: {str(e)}"}
                
            except Exception as e:
                error_msg = f"未知错误: {str(e)} (尝试 {attempt + 1}/{max_retries + 1})"
                logger.error(error_msg)
                if attempt < max_retries:
                    time.sleep(retry_delay * (attempt + 1))
                    continue
                return {"error": f"未知错误: {str(e)}"}
        
        return {"error": f"所有重试都失败了，共尝试 {max_retries + 1} 次"}
    
    def list_available_models(self) -> List[Dict[str, Any]]:
        """
        获取Ollama服务器上可用的模型列表
        
        Returns:
            List[Dict[str, Any]]: 可用模型的列表
        """
        if not self.ip_address:
            logger.error("Ollama服务不可用")
            return []
        
        try:
            url = f"http://{self.ip_address}:{self.port}/api/tags"
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            return data.get("models", [])
            
        except Exception as e:
            logger.error(f"获取模型列表失败: {str(e)}")
            return []
    
    def is_available(self) -> bool:
        """
        检查当前配置的Ollama服务是否可用
        
        Returns:
            bool: 是否可用
        """
        if not self.ip_address:
            return False
        return self.check_ollama_health(self.ip_address, self.port)
