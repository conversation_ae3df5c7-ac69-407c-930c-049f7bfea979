"""
域名注册日期查询服务
用于查询和更新工具域名的注册日期信息
"""

import logging
from datetime import datetime
from typing import List, Optional, Tuple, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import text

from app.models.tool import Tool
from app.utils.domain_utils import DomainInfoUtils
from app.core.database import SessionLocal

logger = logging.getLogger(__name__)


class DomainRegistrationService:
    """
    域名注册日期查询和更新服务
    """
    
    def __init__(self, db: Session = None):
        """
        初始化服务
        
        Args:
            db: 数据库会话，如果为None则创建新会话
        """
        self.db = db
        self._should_close_db = False
        
        if self.db is None:
            self.db = SessionLocal()
            self._should_close_db = True
    
    def __del__(self):
        """析构函数，确保数据库会话正确关闭"""
        if self._should_close_db and self.db:
            self.db.close()
    
    def get_tools_without_registration_date(self, limit: int = 100) -> List[Tool]:
        """
        获取没有域名注册日期的工具列表
        
        Args:
            limit: 限制返回的工具数量
            
        Returns:
            List[Tool]: 工具列表
        """
        try:
            logger.info(f"🔍 [域名服务] 查询没有注册日期的工具，限制数量: {limit}")
            
            tools = self.db.query(Tool).filter(
                Tool.domain_registration_date.is_(None),
                Tool.url.isnot(None),
                Tool.url != ''
            ).limit(limit).all()
            
            logger.info(f"📊 [域名服务] 找到 {len(tools)} 个需要更新注册日期的工具")
            return tools
            
        except Exception as e:
            logger.error(f"❌ [域名服务] 查询工具失败: {str(e)}")
            return []
    
    def extract_domain_from_url(self, url: str) -> Optional[str]:
        """
        从URL中提取域名
        
        Args:
            url: 完整的URL
            
        Returns:
            str: 提取的域名，失败返回None
        """
        try:
            domain = DomainInfoUtils.extract_domain_from_url(url)
            if domain and DomainInfoUtils.is_valid_domain(domain):
                return domain
            return None
        except Exception as e:
            logger.error(f"❌ [域名提取] 从URL提取域名失败: {url}, 错误: {str(e)}")
            return None
    
    def query_domain_registration_date(self, domain: str) -> Optional[datetime]:
        """
        查询域名的注册日期
        
        Args:
            domain: 域名
            
        Returns:
            datetime: 注册日期，失败返回None
        """
        try:
            logger.info(f"🔍 [WHOIS查询] 开始查询域名: {domain}")
            
            result = DomainInfoUtils.get_domain_registration_date(domain)
            
            if isinstance(result, datetime):
                logger.info(f"✅ [WHOIS查询] 域名 {domain} 注册日期: {result}")
                return result
            elif isinstance(result, str):
                logger.warning(f"⚠️ [WHOIS查询] 域名 {domain} 查询失败: {result}")
                return None
            else:
                logger.warning(f"⚠️ [WHOIS查询] 域名 {domain} 未找到注册日期")
                return None
                
        except Exception as e:
            logger.error(f"❌ [WHOIS查询] 查询域名 {domain} 时出错: {str(e)}")
            return None
    
    def update_tool_registration_date(self, tool: Tool, registration_date: datetime) -> bool:
        """
        更新工具的域名注册日期
        
        Args:
            tool: 工具对象
            registration_date: 注册日期
            
        Returns:
            bool: 是否更新成功
        """
        try:
            logger.info(f"💾 [数据库更新] 更新工具 {tool.tool_id} 的注册日期: {registration_date}")
            
            tool.domain_registration_date = registration_date
            self.db.commit()
            
            logger.info(f"✅ [数据库更新] 工具 {tool.tool_id} 注册日期更新成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ [数据库更新] 更新工具 {tool.tool_id} 注册日期失败: {str(e)}")
            self.db.rollback()
            return False
    
    def process_single_tool(self, tool: Tool) -> Tuple[bool, str]:
        """
        处理单个工具的域名注册日期查询和更新
        
        Args:
            tool: 工具对象
            
        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        try:
            logger.info(f"🔄 [处理工具] 开始处理工具: {tool.tool_id} - {tool.url}")
            
            # 提取域名
            domain = self.extract_domain_from_url(tool.url)
            if not domain:
                message = f"无法从URL提取有效域名: {tool.url}"
                logger.warning(f"⚠️ [处理工具] {message}")
                return False, message
            
            logger.info(f"🌐 [处理工具] 提取的域名: {domain}")
            
            # 查询注册日期
            registration_date = self.query_domain_registration_date(domain)
            if not registration_date:
                message = f"无法查询到域名 {domain} 的注册日期"
                logger.warning(f"⚠️ [处理工具] {message}")
                return False, message
            
            # 更新数据库
            success = self.update_tool_registration_date(tool, registration_date)
            if success:
                message = f"成功更新工具 {tool.tool_id} 的注册日期: {registration_date}"
                logger.info(f"✅ [处理工具] {message}")
                return True, message
            else:
                message = f"更新工具 {tool.tool_id} 的注册日期失败"
                logger.error(f"❌ [处理工具] {message}")
                return False, message
                
        except Exception as e:
            message = f"处理工具 {tool.tool_id} 时出错: {str(e)}"
            logger.error(f"❌ [处理工具] {message}")
            return False, message
    
    def batch_update_registration_dates(self, batch_size: int = 50, max_tools: int = 100, retry_failed: bool = False) -> Dict[str, Any]:
        """
        批量更新域名注册日期

        Args:
            batch_size: 每批处理的工具数量
            max_tools: 最大处理工具数量
            retry_failed: 是否重试之前失败的域名

        Returns:
            Dict[str, Any]: 处理结果统计
        """
        try:
            logger.info(f"🚀 [批量更新] 开始批量更新域名注册日期，批次大小: {batch_size}, 最大数量: {max_tools}")
            
            # 获取需要更新的工具
            tools = self.get_tools_without_registration_date(limit=max_tools)
            
            if not tools:
                logger.info("📭 [批量更新] 没有需要更新的工具")
                return {
                    "total_tools": 0,
                    "processed": 0,
                    "success": 0,
                    "failed": 0,
                    "errors": []
                }
            
            total_tools = len(tools)
            processed = 0
            success_count = 0
            failed_count = 0
            errors = []
            error_categories = {
                'domain_extraction_failed': 0,
                'whois_query_failed': 0,
                'database_update_failed': 0,
                'invalid_domain': 0,
                'timeout': 0,
                'other': 0
            }
            
            logger.info(f"📊 [批量更新] 总共需要处理 {total_tools} 个工具")
            
            # 分批处理
            for i in range(0, len(tools), batch_size):
                batch = tools[i:i + batch_size]
                batch_num = i // batch_size + 1
                
                logger.info(f"📦 [批量更新] 处理第 {batch_num} 批，包含 {len(batch)} 个工具")
                
                for tool in batch:
                    processed += 1
                    success, message = self.process_single_tool(tool)
                    
                    if success:
                        success_count += 1
                    else:
                        failed_count += 1

                        # 分类错误类型
                        error_category = self._categorize_error(message)
                        error_categories[error_category] += 1

                        errors.append({
                            "tool_id": tool.tool_id,
                            "url": tool.url,
                            "error": message,
                            "category": error_category
                        })
                    
                    # 记录进度
                    if processed % 10 == 0:
                        logger.info(f"📈 [批量更新] 进度: {processed}/{total_tools} "
                                  f"(成功: {success_count}, 失败: {failed_count})")
                
                # 批次间短暂休息，避免过于频繁的WHOIS查询
                import time
                time.sleep(2)  # 增加到2秒，更好地避免被限制
            
            result = {
                "total_tools": total_tools,
                "processed": processed,
                "success": success_count,
                "failed": failed_count,
                "success_rate": round(success_count / processed * 100, 2) if processed > 0 else 0,
                "error_categories": error_categories,
                "errors": errors[:10]  # 只返回前10个错误
            }
            
            logger.info(f"🎉 [批量更新] 批量更新完成:")
            logger.info(f"  - 总工具数: {total_tools}")
            logger.info(f"  - 已处理: {processed}")
            logger.info(f"  - 成功: {success_count}")
            logger.info(f"  - 失败: {failed_count}")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ [批量更新] 批量更新过程中出错: {str(e)}")
            return {
                "total_tools": 0,
                "processed": 0,
                "success": 0,
                "failed": 0,
                "errors": [{"error": str(e)}]
            }
    
    def add_domain_registration_date_column(self) -> bool:
        """
        添加domain_registration_date字段到tools表（如果不存在）
        
        Returns:
            bool: 是否成功添加字段
        """
        try:
            logger.info("🔧 [数据库迁移] 检查并添加domain_registration_date字段")
            
            # 检查字段是否已存在
            check_sql = text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'tools' 
                AND column_name = 'domain_registration_date'
            """)
            
            result = self.db.execute(check_sql).fetchone()
            
            if result:
                logger.info("✅ [数据库迁移] domain_registration_date字段已存在")
                return True
            
            # 添加字段
            alter_sql = text("""
                ALTER TABLE tools 
                ADD COLUMN domain_registration_date TIMESTAMP WITH TIME ZONE
            """)
            
            self.db.execute(alter_sql)
            self.db.commit()
            
            logger.info("✅ [数据库迁移] 成功添加domain_registration_date字段")
            return True
            
        except Exception as e:
            logger.error(f"❌ [数据库迁移] 添加字段失败: {str(e)}")
            self.db.rollback()
            return False

    def _categorize_error(self, error_message: str) -> str:
        """
        根据错误消息分类错误类型

        Args:
            error_message: 错误消息

        Returns:
            str: 错误类别
        """
        error_message_lower = error_message.lower()

        if "无法从url提取有效域名" in error_message_lower or "invalid" in error_message_lower:
            return "invalid_domain"
        elif "无法从url提取" in error_message_lower:
            return "domain_extraction_failed"
        elif "无法查询到域名" in error_message_lower or "whois" in error_message_lower:
            return "whois_query_failed"
        elif "更新工具" in error_message_lower and "失败" in error_message_lower:
            return "database_update_failed"
        elif "timeout" in error_message_lower or "超时" in error_message_lower:
            return "timeout"
        else:
            return "other"

    def get_query_statistics(self) -> Dict[str, Any]:
        """
        获取查询统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 获取需要更新的工具总数
            total_pending = len(self.get_tools_without_registration_date(limit=1000))

            # 获取已有注册日期的工具数
            total_with_date = self.db.query(Tool).filter(
                Tool.domain_registration_date.isnot(None)
            ).count()

            # 获取总工具数
            total_tools = self.db.query(Tool).count()

            completion_rate = round(total_with_date / total_tools * 100, 2) if total_tools > 0 else 0

            return {
                "total_tools": total_tools,
                "tools_with_registration_date": total_with_date,
                "tools_pending_update": total_pending,
                "completion_rate": completion_rate,
                "last_updated": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"❌ [统计信息] 获取统计信息失败: {str(e)}")
            return {
                "error": str(e),
                "last_updated": datetime.now().isoformat()
            }
