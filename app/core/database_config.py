"""
数据库配置管理模块

专门处理数据库连接的超时、重试和连接池配置
"""

import os
from typing import Dict, Any
from dataclasses import dataclass


@dataclass
class DatabaseTimeoutConfig:
    """数据库超时配置"""
    # 连接超时（秒）
    connect_timeout: int = 60
    
    # 命令执行超时（秒）
    command_timeout: int = 300
    
    # SQL语句超时（毫秒）
    statement_timeout: int = 300000  # 5分钟
    
    # 事务空闲超时（毫秒）
    idle_in_transaction_timeout: int = 600000  # 10分钟
    
    # 连接池配置
    pool_size: int = 10
    max_overflow: int = 20
    pool_timeout: int = 30
    pool_recycle: int = 300
    
    # 重试配置
    max_retries: int = 3
    retry_delay_base: int = 2  # 指数退避基数


@dataclass
class DatabaseOperationConfig:
    """数据库操作配置"""
    # 批量处理配置
    batch_size: int = 10
    batch_commit_interval: int = 5  # 每5个记录提交一次
    
    # 长时间运行任务配置
    heartbeat_interval: int = 30  # 心跳检查间隔（秒）
    connection_refresh_interval: int = 60  # 连接刷新间隔（秒）


def get_database_connect_args(timeout_config: DatabaseTimeoutConfig = None) -> Dict[str, Any]:
    """
    获取数据库连接参数

    Args:
        timeout_config: 超时配置，如果为None则使用默认配置

    Returns:
        Dict[str, Any]: 连接参数字典
    """
    if timeout_config is None:
        timeout_config = DatabaseTimeoutConfig()

    return {
        "connect_timeout": timeout_config.connect_timeout,
        # 注意：command_timeout 不是 PostgreSQL 的有效连接选项，移除它
        "options": f"-c statement_timeout={timeout_config.statement_timeout}ms -c idle_in_transaction_session_timeout={timeout_config.idle_in_transaction_timeout}ms"
    }


def get_engine_config(timeout_config: DatabaseTimeoutConfig = None) -> Dict[str, Any]:
    """
    获取SQLAlchemy引擎配置
    
    Args:
        timeout_config: 超时配置，如果为None则使用默认配置
        
    Returns:
        Dict[str, Any]: 引擎配置字典
    """
    if timeout_config is None:
        timeout_config = DatabaseTimeoutConfig()
    
    return {
        "pool_pre_ping": True,
        "pool_recycle": timeout_config.pool_recycle,
        "pool_size": timeout_config.pool_size,
        "max_overflow": timeout_config.max_overflow,
        "pool_timeout": timeout_config.pool_timeout,
        "echo": False,
        "connect_args": get_database_connect_args(timeout_config)
    }


def create_robust_database_session():
    """
    创建一个健壮的数据库会话，包含重试和错误处理
    
    Returns:
        Session: 数据库会话
    """
    from app.core.database import SessionLocal
    import time
    import logging
    
    logger = logging.getLogger(__name__)
    timeout_config = DatabaseTimeoutConfig()
    
    for attempt in range(timeout_config.max_retries):
        try:
            db = SessionLocal()
            # 测试连接
            from sqlalchemy import text
            db.execute(text("SELECT 1"))
            logger.info(f"数据库连接成功 (尝试 {attempt + 1})")
            return db
            
        except Exception as e:
            logger.warning(f"数据库连接失败 (尝试 {attempt + 1}/{timeout_config.max_retries}): {str(e)}")
            
            if attempt < timeout_config.max_retries - 1:
                delay = timeout_config.retry_delay_base ** attempt
                logger.info(f"等待 {delay} 秒后重试...")
                time.sleep(delay)
            else:
                logger.error("数据库连接最终失败")
                raise
    
    return None


def execute_with_retry(db_session, operation_func, *args, **kwargs):
    """
    执行数据库操作，包含重试机制
    
    Args:
        db_session: 数据库会话
        operation_func: 要执行的操作函数
        *args: 操作函数的位置参数
        **kwargs: 操作函数的关键字参数
        
    Returns:
        操作函数的返回值
    """
    import time
    import logging
    from sqlalchemy.exc import OperationalError, DisconnectionError
    
    logger = logging.getLogger(__name__)
    timeout_config = DatabaseTimeoutConfig()
    
    for attempt in range(timeout_config.max_retries):
        try:
            # 检查连接状态
            from sqlalchemy import text
            db_session.execute(text("SELECT 1"))
            
            # 执行操作
            result = operation_func(*args, **kwargs)
            return result
            
        except (OperationalError, DisconnectionError) as e:
            logger.warning(f"数据库操作失败 (尝试 {attempt + 1}/{timeout_config.max_retries}): {str(e)}")
            
            # 回滚事务
            try:
                db_session.rollback()
            except Exception:
                pass
            
            if attempt < timeout_config.max_retries - 1:
                delay = timeout_config.retry_delay_base ** attempt
                logger.info(f"等待 {delay} 秒后重试...")
                time.sleep(delay)
            else:
                logger.error("数据库操作最终失败")
                raise
        
        except Exception as e:
            logger.error(f"数据库操作发生非连接相关错误: {str(e)}")
            raise
    
    return None


# 默认配置实例
DEFAULT_TIMEOUT_CONFIG = DatabaseTimeoutConfig()
DEFAULT_OPERATION_CONFIG = DatabaseOperationConfig()
