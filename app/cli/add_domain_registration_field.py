#!/usr/bin/env python3
"""
添加domain_registration_date字段到tools表的脚本
"""

import sys
import logging
from sqlalchemy import create_engine, text
from app.core.config import settings

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def add_domain_registration_date_field():
    """添加domain_registration_date字段到tools表"""
    try:
        logger.info("🔧 开始添加domain_registration_date字段到tools表...")
        logger.info(f"数据库URL: {settings.DATABASE_URL}")
        
        # 创建数据库引擎
        engine = create_engine(settings.DATABASE_URL)
        
        with engine.connect() as connection:
            # 检查字段是否已存在
            check_sql = text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'tools' 
                AND column_name = 'domain_registration_date'
            """)
            
            result = connection.execute(check_sql).fetchone()
            
            if result:
                logger.info("✅ domain_registration_date字段已存在，无需添加")
                return True
            
            # 添加字段
            logger.info("📝 添加domain_registration_date字段...")
            alter_sql = text("""
                ALTER TABLE tools 
                ADD COLUMN domain_registration_date TIMESTAMP WITH TIME ZONE
            """)
            
            connection.execute(alter_sql)
            connection.commit()
            
            logger.info("✅ 成功添加domain_registration_date字段到tools表")
            
            # 验证字段是否添加成功
            verify_result = connection.execute(check_sql).fetchone()
            if verify_result:
                logger.info("✅ 字段添加验证成功")
                return True
            else:
                logger.error("❌ 字段添加验证失败")
                return False
        
    except Exception as e:
        logger.error(f"❌ 添加字段失败: {str(e)}")
        return False

def main():
    """主函数"""
    logger.info("🚀 开始执行数据库字段添加脚本...")
    
    success = add_domain_registration_date_field()
    
    if success:
        logger.info("🎉 脚本执行成功！")
        sys.exit(0)
    else:
        logger.error("💥 脚本执行失败！")
        sys.exit(1)

if __name__ == "__main__":
    main()
