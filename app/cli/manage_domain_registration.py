#!/usr/bin/env python3
"""
域名注册日期管理脚本
提供手动运行、监控和管理域名注册日期更新功能
"""

import sys
import argparse
import logging
import json
from datetime import datetime
from app.core.database import SessionLocal
from app.services.domain_registration_service import DomainRegistrationService

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def show_statistics():
    """显示域名注册日期统计信息"""
    print("📊 域名注册日期统计信息")
    print("=" * 60)
    
    db = SessionLocal()
    service = DomainRegistrationService(db)
    
    try:
        stats = service.get_query_statistics()
        
        print(f"📈 总体统计:")
        print(f"  - 工具总数: {stats.get('total_tools', 0)}")
        print(f"  - 已有注册日期: {stats.get('tools_with_registration_date', 0)}")
        print(f"  - 待更新: {stats.get('tools_pending_update', 0)}")
        print(f"  - 完成率: {stats.get('completion_rate', 0)}%")
        print(f"  - 最后更新: {stats.get('last_updated', 'Unknown')}")
        
        # 显示一些待更新的工具示例
        tools = service.get_tools_without_registration_date(limit=5)
        if tools:
            print(f"\n📋 待更新工具示例 (前5个):")
            for i, tool in enumerate(tools, 1):
                print(f"  {i}. {tool.tool_id}: {tool.url}")
        
    finally:
        db.close()

def run_batch_update(batch_size=20, max_tools=50, dry_run=False):
    """运行批量更新"""
    print(f"🚀 {'模拟' if dry_run else '执行'}批量域名注册日期更新")
    print("=" * 60)
    print(f"⚙️ 配置: 批次大小={batch_size}, 最大工具数={max_tools}")
    
    if dry_run:
        print("🔍 这是模拟运行，不会实际更新数据库")
    
    db = SessionLocal()
    service = DomainRegistrationService(db)
    
    try:
        if dry_run:
            # 模拟运行，只显示会处理的工具
            tools = service.get_tools_without_registration_date(limit=max_tools)
            print(f"\n📋 将要处理的工具 ({len(tools)} 个):")
            for i, tool in enumerate(tools[:10], 1):  # 只显示前10个
                print(f"  {i}. {tool.tool_id}: {tool.url}")
            if len(tools) > 10:
                print(f"  ... 还有 {len(tools) - 10} 个工具")
            
            print(f"\n✅ 模拟运行完成，实际运行时将处理 {len(tools)} 个工具")
        else:
            # 实际运行
            result = service.batch_update_registration_dates(
                batch_size=batch_size,
                max_tools=max_tools
            )
            
            print(f"\n🎉 批量更新完成:")
            print(f"  - 总工具数: {result['total_tools']}")
            print(f"  - 已处理: {result['processed']}")
            print(f"  - 成功: {result['success']}")
            print(f"  - 失败: {result['failed']}")
            print(f"  - 成功率: {result.get('success_rate', 0)}%")
            
            # 显示错误分类
            if result.get('error_categories'):
                print(f"\n📊 错误分类:")
                for category, count in result['error_categories'].items():
                    if count > 0:
                        print(f"  - {category}: {count}")
            
            # 显示错误详情
            if result.get('errors'):
                print(f"\n❌ 错误详情 (前5个):")
                for i, error in enumerate(result['errors'][:5], 1):
                    category = error.get('category', 'unknown')
                    print(f"  {i}. {error.get('tool_id', 'Unknown')} [{category}]:")
                    print(f"     {error.get('error', 'Unknown error')}")
            
            # 保存结果到文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"domain_update_result_{timestamp}.json"
            
            # 转换datetime对象为字符串以便JSON序列化
            json_result = result.copy()
            for error in json_result.get('errors', []):
                if 'timestamp' in error:
                    error['timestamp'] = str(error['timestamp'])
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(json_result, f, indent=2, ensure_ascii=False, default=str)
            
            print(f"\n💾 结果已保存到: {filename}")
    
    finally:
        db.close()

def test_single_domain(domain):
    """测试单个域名查询"""
    print(f"🔍 测试域名查询: {domain}")
    print("=" * 60)
    
    from app.utils.domain_utils import DomainInfoUtils
    
    # 清理域名
    cleaned_domain = DomainInfoUtils._clean_domain(domain)
    print(f"🧹 清理后域名: {cleaned_domain}")
    
    # 验证域名
    is_valid = DomainInfoUtils.is_valid_domain(cleaned_domain)
    print(f"✅ 域名有效性: {is_valid}")
    
    if not is_valid:
        print("❌ 域名无效，跳过查询")
        return
    
    # 查询注册日期
    print(f"\n🌐 开始查询注册日期...")
    start_time = datetime.now()
    
    result = DomainInfoUtils.get_domain_registration_date(domain)
    
    end_time = datetime.now()
    query_time = (end_time - start_time).total_seconds()
    
    print(f"⏱️ 查询耗时: {query_time:.2f}秒")
    
    if isinstance(result, datetime):
        print(f"✅ 查询成功: {result}")
    elif isinstance(result, str):
        print(f"❌ 查询失败: {result}")
    else:
        print(f"⚠️ 无结果")

def test_all_methods(domain):
    """测试所有查询方法"""
    print(f"🔧 测试所有查询方法: {domain}")
    print("=" * 60)

    from app.utils.domain_utils import DomainInfoUtils
    import time

    # 清理域名
    cleaned_domain = DomainInfoUtils._clean_domain(domain)
    print(f"🧹 清理后域名: {cleaned_domain}")

    # 验证域名
    is_valid = DomainInfoUtils.is_valid_domain(cleaned_domain)
    print(f"✅ 域名有效性: {is_valid}")

    if not is_valid:
        print("❌ 域名无效，跳过查询")
        return

    # 定义所有查询方法（按优化后的优先级排序）
    methods = [
        ("Who.sb API", DomainInfoUtils._query_with_whosb_api),
        ("系统whois命令", DomainInfoUtils._query_with_system_whois),
        ("备用WHOIS服务", DomainInfoUtils._query_with_backup_whois),
        ("Python-whois库", DomainInfoUtils._query_with_python_whois),
        ("WHOIS API", DomainInfoUtils._query_with_whois_api),
    ]

    results = []

    for i, (method_name, method_func) in enumerate(methods, 1):
        print(f"\n🔍 测试方法 {i}/{len(methods)}: {method_name}")
        print("-" * 40)

        try:
            start_time = time.time()
            result = method_func(cleaned_domain)
            end_time = time.time()
            query_time = end_time - start_time

            if isinstance(result, datetime):
                print(f"   ✅ 成功: {result} ({query_time:.2f}s)")
                results.append({
                    'method': method_name,
                    'status': 'success',
                    'result': result,
                    'time': query_time
                })
            elif isinstance(result, str):
                print(f"   ❌ 失败: {result[:100]}... ({query_time:.2f}s)")
                results.append({
                    'method': method_name,
                    'status': 'error',
                    'error': result,
                    'time': query_time
                })
            else:
                print(f"   ⚠️  无结果 ({query_time:.2f}s)")
                results.append({
                    'method': method_name,
                    'status': 'no_result',
                    'time': query_time
                })

        except Exception as e:
            print(f"   💥 异常: {str(e)}")
            results.append({
                'method': method_name,
                'status': 'exception',
                'error': str(e),
                'time': 0
            })

        # 方法间隔
        if i < len(methods):
            time.sleep(1)

    # 输出总结
    print(f"\n📊 测试总结:")
    print("-" * 40)
    success_count = sum(1 for r in results if r['status'] == 'success')
    total_time = sum(r['time'] for r in results)

    print(f"成功方法: {success_count}/{len(methods)}")
    print(f"总耗时: {total_time:.2f}秒")

    if success_count > 0:
        print(f"\n✅ 成功的方法:")
        for result in results:
            if result['status'] == 'success':
                print(f"   - {result['method']}: {result['result']} ({result['time']:.2f}s)")

def setup_database():
    """设置数据库（添加字段）"""
    print("🔧 设置数据库字段")
    print("=" * 60)

    db = SessionLocal()
    service = DomainRegistrationService(db)

    try:
        success = service.add_domain_registration_date_column()
        if success:
            print("✅ 数据库字段设置成功")
        else:
            print("❌ 数据库字段设置失败")
    finally:
        db.close()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='域名注册日期管理工具')
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 统计信息命令
    subparsers.add_parser('stats', help='显示统计信息')
    
    # 批量更新命令
    update_parser = subparsers.add_parser('update', help='批量更新域名注册日期')
    update_parser.add_argument('--batch-size', type=int, default=20, help='批次大小 (默认: 20)')
    update_parser.add_argument('--max-tools', type=int, default=50, help='最大工具数 (默认: 50)')
    update_parser.add_argument('--dry-run', action='store_true', help='模拟运行，不实际更新')
    
    # 测试单个域名命令
    test_parser = subparsers.add_parser('test', help='测试单个域名查询')
    test_parser.add_argument('domain', help='要测试的域名')

    # 测试所有查询方法命令
    methods_parser = subparsers.add_parser('test-methods', help='测试所有查询方法')
    methods_parser.add_argument('domain', help='要测试的域名')

    # 设置数据库命令
    subparsers.add_parser('setup', help='设置数据库字段')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    print(f"🎯 域名注册日期管理工具")
    print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        if args.command == 'stats':
            show_statistics()
        elif args.command == 'update':
            run_batch_update(
                batch_size=args.batch_size,
                max_tools=args.max_tools,
                dry_run=args.dry_run
            )
        elif args.command == 'test':
            test_single_domain(args.domain)
        elif args.command == 'test-methods':
            test_all_methods(args.domain)
        elif args.command == 'setup':
            setup_database()
        
        print(f"\n🎉 命令执行完成!")
        
    except KeyboardInterrupt:
        print(f"\n⏹️ 操作被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ 执行过程中出错: {str(e)}")
        print(f"\n💥 错误: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
