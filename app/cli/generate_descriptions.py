#!/usr/bin/env python3
"""
工具描述生成CLI脚本

提供命令行接口来执行工具描述的自动生成，功能包括：
- 批量生成工具描述
- 支持多种配置参数
- 干运行模式
- 进度显示和日志记录
- 错误处理和重试

使用方法:
    python -m app.cli.generate_descriptions --help
    python -m app.cli.generate_descriptions --locale en --limit 10
    python -m app.cli.generate_descriptions --dry-run
"""

import os
import sys
import argparse
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.services.description_generation_service import DescriptionGenerationService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('generate_descriptions.log')
    ]
)
logger = logging.getLogger(__name__)


class DescriptionGeneratorCLI:
    """工具描述生成CLI类"""
    
    def __init__(self):
        """初始化CLI"""
        self.db = None
        self.service = None
    
    def setup_database(self) -> bool:
        """设置数据库连接"""
        try:
            self.db = SessionLocal()
            logger.info("数据库连接建立成功")
            return True
        except Exception as e:
            logger.error(f"数据库连接失败: {str(e)}")
            return False
    
    def setup_service(self, ip_address: str = None, port: int = 11434) -> bool:
        """设置描述生成服务"""
        try:
            self.service = DescriptionGenerationService(self.db)
            success = self.service.initialize_services(ip_address, port)
            if success:
                logger.info("描述生成服务初始化成功")
            else:
                logger.error("描述生成服务初始化失败")
            return success
        except Exception as e:
            logger.error(f"服务初始化失败: {str(e)}")
            return False
    
    def run_generation(self, args) -> bool:
        """运行描述生成"""
        try:
            logger.info("开始执行工具描述生成任务")
            logger.info(f"配置参数: {vars(args)}")
            
            # 执行生成
            result = self.service.generate_descriptions(
                locale=args.locale,
                model_name=args.model,
                fallback_model=args.fallback_model,
                fields=args.fields,
                limit=args.limit,
                max_chunks=args.max_chunks,
                chunk_size=args.chunk_size,
                max_tokens=args.max_tokens,
                temperature=args.temperature,
                dry_run=args.dry_run
            )
            
            # 显示结果
            if result["success"]:
                logger.info("任务执行完成")
                logger.info(f"结果: {result['message']}")
                
                if not args.dry_run:
                    logger.info(f"处理: {result['processed']} 个工具")
                    logger.info(f"成功: {result['succeeded']} 个")
                    logger.info(f"失败: {result['failed']} 个")
                    
                    if result["errors"]:
                        logger.warning("错误详情:")
                        for error in result["errors"]:
                            logger.warning(f"  - {error}")
                else:
                    if "tools" in result:
                        logger.info("将要处理的工具:")
                        for tool in result["tools"]:
                            logger.info(f"  - {tool['tool_id']}: {tool['name']}")
                
                return True
            else:
                logger.error(f"任务执行失败: {result.get('error', '未知错误')}")
                return False
                
        except Exception as e:
            logger.error(f"执行生成任务时发生错误: {str(e)}")
            return False
    
    def show_status(self, locale: str = 'en'):
        """显示生成状态"""
        try:
            logger.info("获取生成状态...")
            status = self.service.get_generation_status(locale)
            
            if status["success"]:
                logger.info(f"=== 生成状态统计 (语言: {status['locale']}) ===")
                logger.info(f"总工具数: {status['total_tools']}")
                logger.info(f"Ollama服务状态: {'可用' if status['ollama_service_available'] else '不可用'}")
                logger.info("")
                
                logger.info("字段完成情况:")
                for field, stats in status["field_statistics"].items():
                    logger.info(f"  {field}:")
                    logger.info(f"    已完成: {stats['completed']}")
                    logger.info(f"    待处理: {stats['pending']}")
                    logger.info(f"    完成率: {stats['completion_rate']}%")
                    logger.info("")
            else:
                logger.error(f"获取状态失败: {status.get('error', '未知错误')}")
                
        except Exception as e:
            logger.error(f"显示状态时发生错误: {str(e)}")
    
    def list_models(self):
        """列出可用模型"""
        try:
            logger.info("获取可用模型列表...")
            models = self.service.get_available_models()
            
            if models:
                logger.info("=== 可用的Ollama模型 ===")
                for model in models:
                    name = model.get('name', 'Unknown')
                    size = model.get('size', 'Unknown size')
                    logger.info(f"  - {name} ({size})")
            else:
                logger.warning("没有找到可用模型或Ollama服务不可用")
                
        except Exception as e:
            logger.error(f"列出模型时发生错误: {str(e)}")
    
    def cleanup(self):
        """清理资源"""
        if self.db:
            self.db.close()
            logger.info("数据库连接已关闭")


def create_argument_parser():
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description='使用Ollama本地大模型为工具自动生成详细描述和其他字段内容',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 基本用法
  python -m app.cli.generate_descriptions --locale en --limit 10
  
  # 干运行模式（仅显示将要处理的工具）
  python -m app.cli.generate_descriptions --dry-run
  
  # 指定模型和字段
  python -m app.cli.generate_descriptions --model llama3:latest --fields long_description,pricing_details
  
  # 显示状态
  python -m app.cli.generate_descriptions --status
  
  # 列出可用模型
  python -m app.cli.generate_descriptions --list-models
        """
    )
    
    # 基本参数
    parser.add_argument(
        '--locale',
        type=str,
        default='en',
        help='语言代码，默认为英语(en)'
    )
    
    parser.add_argument(
        '--model',
        type=str,
        default='gemma3:latest',
        help='使用的Ollama模型名称，默认为gemma3:latest'
    )
    
    parser.add_argument(
        '--fallback-model',
        type=str,
        default='tinyllama:latest',
        help='当主模型失败时使用的备用模型'
    )
    
    parser.add_argument(
        '--ip-address',
        type=str,
        help='Ollama服务器IP地址，默认自动寻找可用服务器'
    )
    
    parser.add_argument(
        '--port',
        type=int,
        default=11434,
        help='Ollama服务器端口，默认11434'
    )
    
    parser.add_argument(
        '--limit',
        type=int,
        default=10,
        help='每次处理的工具数量上限'
    )
    
    parser.add_argument(
        '--fields',
        type=str,
        default='long_description,usage_instructions,pricing_details,integration_info',
        help='要生成的字段，用逗号分隔'
    )
    
    # 内容生成参数
    parser.add_argument(
        '--max-chunks',
        type=int,
        default=5,
        help='从网站提取的最大文本块数量'
    )
    
    parser.add_argument(
        '--chunk-size',
        type=int,
        default=2000,
        help='每个文本块的最大字符数'
    )
    
    parser.add_argument(
        '--max-tokens',
        type=int,
        default=2048,
        help='生成的最大令牌数'
    )
    
    parser.add_argument(
        '--temperature',
        type=float,
        default=0.7,
        help='生成文本的随机性，范围0-1，越高越随机'
    )
    
    # 操作模式
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='仅显示将要处理的工具，不实际更新数据库'
    )
    
    parser.add_argument(
        '--status',
        action='store_true',
        help='显示当前生成状态统计'
    )
    
    parser.add_argument(
        '--list-models',
        action='store_true',
        help='列出可用的Ollama模型'
    )
    
    # 日志级别
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='启用详细日志输出'
    )
    
    return parser


def main():
    """主函数"""
    parser = create_argument_parser()
    args = parser.parse_args()
    
    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.debug("启用详细日志模式")
    
    # 创建CLI实例
    cli = DescriptionGeneratorCLI()
    
    try:
        # 设置数据库连接
        if not cli.setup_database():
            logger.error("数据库连接失败，程序退出")
            return 1
        
        # 设置服务
        if not cli.setup_service(args.ip_address, args.port):
            logger.error("服务初始化失败，程序退出")
            return 1
        
        # 根据参数执行相应操作
        if args.status:
            cli.show_status(args.locale)
        elif args.list_models:
            cli.list_models()
        else:
            # 执行生成任务
            success = cli.run_generation(args)
            if not success:
                return 1
        
        logger.info("程序执行完成")
        return 0
        
    except KeyboardInterrupt:
        logger.info("用户中断程序执行")
        return 1
    except Exception as e:
        logger.error(f"程序执行过程中发生未预期的错误: {str(e)}")
        return 1
    finally:
        cli.cleanup()


if __name__ == '__main__':
    sys.exit(main())
