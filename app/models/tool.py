from sqlalchemy import Column, Integer, String, Text, Boolean, DECIMAL, DateTime, ForeignKey, Table
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.core.database import Base

# 工具-分类关联表
tool_categories = Table(
    'tool_categories',
    Base.metadata,
    Column('tool_id', String(50), Foreign<PERSON>ey('tools.tool_id'), primary_key=True),
    Column('category_id', Integer, ForeignKey('categories.id'), primary_key=True),
    Column('created_at', DateTime(timezone=True), server_default=func.now())
)

# 工具-标签关联表
tool_tags = Table(
    'tool_tags',
    Base.metadata,
    Column('tool_id', String(50), ForeignKey('tools.tool_id'), primary_key=True),
    Column('tag_id', Integer, ForeignKey('tags.id'), primary_key=True),
    Column('created_at', DateTime(timezone=True), server_default=func.now())
)

class Category(Base):
    """分类模型"""
    __tablename__ = 'categories'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    slug = Column(String(50), unique=True, nullable=False, index=True)
    icon_url = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # 关系
    translations = relationship("CategoryTranslation", back_populates="category", cascade="all, delete-orphan")
    tools = relationship("Tool", secondary=tool_categories, back_populates="categories")

class CategoryTranslation(Base):
    """分类翻译模型"""
    __tablename__ = 'category_translations'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    category_id = Column(Integer, ForeignKey('categories.id'), nullable=False, index=True)
    locale = Column(String(10), nullable=False, index=True)
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # 关系
    category = relationship("Category", back_populates="translations")
    
    # 唯一约束
    __table_args__ = (
        {'extend_existing': True}
    )

class Tag(Base):
    """标签模型"""
    __tablename__ = 'tags'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    slug = Column(String(50), unique=True, nullable=False, index=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # 关系
    translations = relationship("TagTranslation", back_populates="tag", cascade="all, delete-orphan")
    tools = relationship("Tool", secondary=tool_tags, back_populates="tags")

class TagTranslation(Base):
    """标签翻译模型"""
    __tablename__ = 'tag_translations'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    tag_id = Column(Integer, ForeignKey('tags.id'), nullable=False, index=True)
    locale = Column(String(10), nullable=False, index=True)
    name = Column(String(50), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # 关系
    tag = relationship("Tag", back_populates="translations")

class Tool(Base):
    """工具模型"""
    __tablename__ = 'tools'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    tool_id = Column(String(50), unique=True, nullable=False, index=True)
    icon_url = Column(Text, nullable=False)
    url = Column(Text, nullable=False)
    pricing_type = Column(String(20), nullable=False)
    is_premium = Column(Boolean, default=False, index=True)
    is_new = Column(Boolean, default=False, index=True)
    is_featured = Column(Boolean, default=False, index=True)
    rating = Column(DECIMAL(2, 1), nullable=True)
    api_available = Column(Boolean, default=False)
    publisher = Column(String(100), nullable=True)
    publisher_url = Column(Text, nullable=True)
    terms_url = Column(Text, nullable=True)
    privacy_url = Column(Text, nullable=True)
    domain_registration_date = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # 关系
    translations = relationship("ToolTranslation", back_populates="tool", cascade="all, delete-orphan")
    features = relationship("ToolFeature", back_populates="tool", cascade="all, delete-orphan")
    categories = relationship("Category", secondary=tool_categories, back_populates="tools")
    tags = relationship("Tag", secondary=tool_tags, back_populates="tools")

class ToolTranslation(Base):
    """工具翻译模型"""
    __tablename__ = 'tool_translations'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    tool_id = Column(String(50), ForeignKey('tools.tool_id'), nullable=False, index=True)
    locale = Column(String(10), nullable=False, index=True)
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=False)
    long_description = Column(Text, nullable=True)
    usage_instructions = Column(Text, nullable=True)
    pricing_details = Column(Text, nullable=True)
    integration_info = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # 关系
    tool = relationship("Tool", back_populates="translations")

class ToolFeature(Base):
    """工具功能特性模型"""
    __tablename__ = 'tool_features'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    tool_id = Column(String(50), ForeignKey('tools.tool_id'), nullable=False, index=True)
    locale = Column(String(10), nullable=False)
    feature = Column(String(100), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # 关系
    tool = relationship("Tool", back_populates="features")
