#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据库连接
"""

import asyncio
import os
import sys
from traffic_data_manager import TrafficDataManager, DatabaseConfig


def parse_database_url(database_url: str) -> DatabaseConfig:
    """解析数据库URL并创建配置"""
    import re
    
    # 解析 postgresql://user:password@host:port/database?options
    pattern = r'postgresql://([^:]+):([^@]+)@([^:]+):(\d+)/([^?]+)'
    match = re.match(pattern, database_url)
    
    if match:
        user, password, host, port, database = match.groups()
        return DatabaseConfig(
            host=host,
            port=int(port),
            database=database,
            user=user,
            password=password
        )
    else:
        raise ValueError(f"无法解析数据库URL: {database_url}")


async def test_connection():
    """测试数据库连接"""
    print("🔍 测试数据库连接...")
    
    # 获取数据库配置
    database_url = os.getenv('DATABASE_URL')
    if database_url:
        print(f"使用环境变量DATABASE_URL: {database_url}")
        db_config = parse_database_url(database_url)
    else:
        print("使用默认数据库配置")
        db_config = DatabaseConfig()
    
    print(f"连接参数: {db_config.user}@{db_config.host}:{db_config.port}/{db_config.database}")
    
    # 创建管理器并测试连接
    manager = TrafficDataManager(db_config)
    
    try:
        # 初始化连接池
        await manager.init_db_pool()
        print("✅ 数据库连接成功!")
        
        # 测试简单查询
        async with manager.db_pool.acquire() as conn:
            result = await conn.fetchval("SELECT 1")
            if result == 1:
                print("✅ 数据库查询测试成功!")
            else:
                print("❌ 数据库查询测试失败!")
        
        # 检查表是否存在
        async with manager.db_pool.acquire() as conn:
            tables = await conn.fetch("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name LIKE 'traffic_%'
                ORDER BY table_name
            """)
            
            print(f"\n📊 找到 {len(tables)} 个流量相关表:")
            for table in tables:
                print(f"  ✅ {table['table_name']}")
            
            if len(tables) == 0:
                print("⚠️ 未找到流量数据表，请先运行:")
                print("   psql -d aistak_db -f create_traffic_tables.sql")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False
    
    finally:
        await manager.close_db_pool()


async def main():
    """主函数"""
    print("🚀 数据库连接测试")
    print("=" * 50)
    
    success = await test_connection()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 数据库连接测试成功!")
        print("现在可以运行主程序:")
        print("  python main_traffic_processor.py --domain yourware.so")
    else:
        print("💥 数据库连接测试失败!")
        print("请检查:")
        print("1. 数据库服务是否运行")
        print("2. 用户名和密码是否正确")
        print("3. 数据库是否存在")
        print("4. 网络连接是否正常")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n💥 测试异常: {e}")
        sys.exit(1)
