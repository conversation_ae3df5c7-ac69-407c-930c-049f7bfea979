#!/bin/bash

# Aistak FastAPI 服务启动脚本
# 用于快速启动所有相关服务

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 命令未找到，请先安装"
        return 1
    fi
    return 0
}

# 检查端口是否被占用
check_port() {
    if lsof -i :$1 &> /dev/null; then
        log_warning "端口 $1 已被占用"
        return 1
    fi
    return 0
}

# 等待服务启动
wait_for_service() {
    local url=$1
    local service_name=$2
    local max_attempts=30
    local attempt=1
    
    log_info "等待 $service_name 服务启动..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s $url > /dev/null 2>&1; then
            log_success "$service_name 服务启动成功"
            return 0
        fi
        
        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    log_error "$service_name 服务启动失败或超时"
    return 1
}

# 主函数
main() {
    echo "========================================"
    echo "    Aistak FastAPI 服务启动脚本"
    echo "========================================"
    echo
    
    # 检查必要的命令
    log_info "检查必要的命令..."
    check_command "python" || exit 1
    check_command "curl" || exit 1
    
    # 检查Python环境
    if [ -f ".venv/bin/activate" ]; then
        log_info "激活Python虚拟环境..."
        source .venv/bin/activate
    elif command -v uv &> /dev/null; then
        log_info "使用uv环境..."
    else
        log_warning "未找到虚拟环境，使用系统Python"
    fi
    
    # 检查环境变量文件
    if [ ! -f ".env" ]; then
        log_warning ".env 文件不存在，请确保数据库配置正确"
        if [ -f ".env.example" ]; then
            log_info "复制 .env.example 到 .env"
            cp .env.example .env
        fi
    fi
    
    # 启动Ollama服务
    log_info "检查Ollama服务..."
    if ! pgrep -f ollama > /dev/null; then
        log_info "启动Ollama服务..."
        if command -v systemctl &> /dev/null && systemctl is-active --quiet ollama; then
            log_info "使用systemd启动Ollama"
            sudo systemctl start ollama
        else
            log_info "后台启动Ollama"
            nohup ollama serve > ollama.log 2>&1 &
        fi
        
        # 等待Ollama启动
        wait_for_service "http://localhost:11434/api/tags" "Ollama"
    else
        log_success "Ollama服务已在运行"
    fi
    
    # 检查Ollama模型
    log_info "检查Ollama模型..."
    if ! ollama list | grep -q "gemma3:latest"; then
        log_warning "gemma3:latest 模型未找到，正在下载..."
        ollama pull gemma3:latest
    fi
    
    # 启动FastAPI应用
    log_info "启动FastAPI应用..."
    if check_port 8000; then
        if command -v systemctl &> /dev/null && systemctl is-active --quiet aistak-fastapi; then
            log_info "使用systemd启动FastAPI"
            sudo systemctl start aistak-fastapi
        else
            log_info "后台启动FastAPI应用"
            if command -v uv &> /dev/null; then
                nohup uv run uvicorn main:app --host 0.0.0.0 --port 8000 > app.log 2>&1 &
            else
                nohup python -m uvicorn main:app --host 0.0.0.0 --port 8000 > app.log 2>&1 &
            fi
        fi
        
        # 等待FastAPI启动
        wait_for_service "http://localhost:8000/api/v1/health/" "FastAPI"
    else
        log_warning "端口8000被占用，FastAPI可能已在运行"
    fi
    
    # 运行健康检查
    log_info "运行系统健康检查..."
    sleep 5
    
    echo
    echo "========================================"
    echo "           服务状态检查"
    echo "========================================"
    
    # 检查FastAPI
    if curl -s http://localhost:8000/api/v1/health/ > /dev/null; then
        log_success "FastAPI服务: 运行正常"
        echo "  - API文档: http://localhost:8000/docs"
        echo "  - 健康检查: http://localhost:8000/api/v1/health/"
    else
        log_error "FastAPI服务: 异常"
    fi
    
    # 检查数据库
    DB_STATUS=$(curl -s http://localhost:8000/api/v1/health/database | jq -r '.status' 2>/dev/null)
    if [ "$DB_STATUS" = "healthy" ]; then
        log_success "数据库连接: 正常"
    else
        log_error "数据库连接: 异常"
    fi
    
    # 检查Ollama
    if curl -s http://localhost:11434/api/tags > /dev/null; then
        log_success "Ollama服务: 运行正常"
        MODEL_COUNT=$(curl -s http://localhost:11434/api/tags | jq '.models | length' 2>/dev/null)
        echo "  - 可用模型数量: $MODEL_COUNT"
        echo "  - API地址: http://localhost:11434"
    else
        log_error "Ollama服务: 异常"
    fi
    
    echo
    echo "========================================"
    echo "           快速操作命令"
    echo "========================================"
    echo "查看生成状态: python generate_descriptions.py --status"
    echo "生成工具描述: python generate_descriptions.py --limit 5"
    echo "查看应用日志: tail -f app.log"
    echo "查看Ollama日志: tail -f ollama.log"
    echo "停止所有服务: ./stop_services.sh"
    echo "系统监控: ./system_monitor.sh"
    echo
    
    log_success "所有服务启动完成！"
}

# 处理中断信号
trap 'log_warning "启动过程被中断"; exit 1' INT TERM

# 执行主函数
main "$@"
