#!/usr/bin/env python3
"""
工具描述生成独立脚本

这是一个独立的脚本，可以直接运行来生成工具描述，无需启动FastAPI服务器。
它是CLI模块的简化版本，提供最常用的功能。

使用方法:
    python generate_descriptions.py --help
    python generate_descriptions.py --locale en --limit 10
    python generate_descriptions.py --dry-run
    python generate_descriptions.py --status
"""

import os
import sys
import argparse
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from sqlalchemy.orm import Session
from sqlalchemy import text
from app.core.database import SessionLocal
from app.services.description_generation_service import DescriptionGenerationService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('generate_descriptions.log')
    ]
)
logger = logging.getLogger(__name__)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='使用Ollama本地大模型为工具自动生成详细描述',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 基本用法 - 生成10个工具的长描述
  python generate_descriptions.py --locale en --limit 10
  
  # 干运行模式 - 仅显示将要处理的工具
  python generate_descriptions.py --dry-run
  
  # 生成多个字段
  python generate_descriptions.py --fields long_description,pricing_details --limit 5
  
  # 显示当前状态
  python generate_descriptions.py --status
  
  # 列出可用模型
  python generate_descriptions.py --list-models
        """
    )
    
    # 基本参数
    parser.add_argument(
        '--locale',
        type=str,
        default='en',
        help='语言代码，默认为英语(en)'
    )
    
    parser.add_argument(
        '--model',
        type=str,
        default='gemma3:latest',
        help='使用的Ollama模型名称，默认为gemma3:latest'
    )
    
    parser.add_argument(
        '--limit',
        type=int,
        default=10,
        help='每次处理的工具数量上限'
    )
    
    parser.add_argument(
        '--fields',
        type=str,
        default='long_description',
        help='要生成的字段，用逗号分隔。可选: long_description,usage_instructions,pricing_details,integration_info'
    )
    
    parser.add_argument(
        '--ip-address',
        type=str,
        help='Ollama服务器IP地址，默认自动寻找可用服务器'
    )
    
    parser.add_argument(
        '--port',
        type=int,
        default=11434,
        help='Ollama服务器端口，默认11434'
    )
    
    # 操作模式
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='仅显示将要处理的工具，不实际更新数据库'
    )
    
    parser.add_argument(
        '--status',
        action='store_true',
        help='显示当前生成状态统计'
    )
    
    parser.add_argument(
        '--list-models',
        action='store_true',
        help='列出可用的Ollama模型'
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='启用详细日志输出'
    )
    
    args = parser.parse_args()
    
    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.debug("启用详细日志模式")
    
    # 创建数据库会话
    db = None
    service = None
    
    try:
        # 建立数据库连接
        logger.info("建立数据库连接...")
        db = SessionLocal()

        # 测试数据库连接
        try:
            db.execute(text("SELECT 1"))
            logger.info("数据库连接测试成功")
        except Exception as e:
            logger.error(f"数据库连接测试失败: {str(e)}")
            return 1

        # 创建服务实例
        service = DescriptionGenerationService(db)

        # 初始化Ollama服务
        logger.info("初始化Ollama服务...")
        success = service.initialize_services(args.ip_address, args.port)
        if not success:
            logger.error("无法连接到Ollama服务，请检查服务是否启动")
            return 1

        # 根据参数执行相应操作
        if args.status:
            show_status(service, args.locale)
        elif args.list_models:
            list_models(service)
        else:
            # 执行生成任务
            success = run_generation(service, args)
            if not success:
                return 1

        logger.info("程序执行完成")
        return 0
        
    except KeyboardInterrupt:
        logger.info("用户中断程序执行")
        return 1
    except Exception as e:
        logger.error(f"程序执行过程中发生未预期的错误: {str(e)}")
        return 1
    finally:
        if db:
            db.close()
            logger.info("数据库连接已关闭")


def run_generation(service: DescriptionGenerationService, args) -> bool:
    """运行描述生成"""
    try:
        logger.info("开始执行工具描述生成任务")
        logger.info(f"配置参数: locale={args.locale}, model={args.model}, fields={args.fields}, limit={args.limit}")
        
        # 执行生成
        result = service.generate_descriptions(
            locale=args.locale,
            model_name=args.model,
            fields=args.fields,
            limit=args.limit,
            dry_run=args.dry_run
        )
        
        # 显示结果
        if result["success"]:
            logger.info("任务执行完成")
            logger.info(f"结果: {result['message']}")
            
            if not args.dry_run:
                logger.info(f"处理: {result['processed']} 个工具")
                logger.info(f"成功: {result['succeeded']} 个")
                logger.info(f"失败: {result['failed']} 个")
                
                if result["errors"]:
                    logger.warning("错误详情:")
                    for error in result["errors"]:
                        logger.warning(f"  - {error}")
            else:
                if "tools" in result:
                    logger.info("将要处理的工具:")
                    for tool in result["tools"]:
                        logger.info(f"  - {tool['tool_id']}: {tool['name']}")
            
            return True
        else:
            logger.error(f"任务执行失败: {result.get('error', '未知错误')}")
            return False
            
    except Exception as e:
        logger.error(f"执行生成任务时发生错误: {str(e)}")
        return False


def show_status(service: DescriptionGenerationService, locale: str = 'en'):
    """显示生成状态"""
    try:
        logger.info("获取生成状态...")
        status = service.get_generation_status(locale)
        
        if status["success"]:
            print(f"\n=== 生成状态统计 (语言: {status['locale']}) ===")
            print(f"总工具数: {status['total_tools']}")
            print(f"Ollama服务状态: {'可用' if status['ollama_service_available'] else '不可用'}")
            print("")
            
            print("字段完成情况:")
            for field, stats in status["field_statistics"].items():
                print(f"  {field}:")
                print(f"    已完成: {stats['completed']}")
                print(f"    待处理: {stats['pending']}")
                print(f"    完成率: {stats['completion_rate']}%")
                print("")
        else:
            logger.error(f"获取状态失败: {status.get('error', '未知错误')}")
            
    except Exception as e:
        logger.error(f"显示状态时发生错误: {str(e)}")


def list_models(service: DescriptionGenerationService):
    """列出可用模型"""
    try:
        logger.info("获取可用模型列表...")
        models = service.get_available_models()
        
        if models:
            print("\n=== 可用的Ollama模型 ===")
            for model in models:
                name = model.get('name', 'Unknown')
                size = model.get('size', 'Unknown size')
                print(f"  - {name} ({size})")
        else:
            logger.warning("没有找到可用模型或Ollama服务不可用")
            
    except Exception as e:
        logger.error(f"列出模型时发生错误: {str(e)}")


if __name__ == '__main__':
    sys.exit(main())
