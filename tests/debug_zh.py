#!/usr/bin/env python3
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import logging
from app.core.database import SessionLocal
from app.models.tool import Tool, ToolTranslation
from app.services.ollama_service import OllamaService

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def debug_translation_logic():
    """调试翻译逻辑"""
    db = SessionLocal()
    
    try:
        # 1. 检查英文翻译数量
        english_count = db.query(ToolTranslation).filter(
            ToolTranslation.locale == 'en'
        ).count()
        print(f"✅ 英文翻译总数: {english_count}")
        
        # 2. 检查中文翻译数量
        chinese_count = db.query(ToolTranslation).filter(
            ToolTranslation.locale == 'zh'
        ).count()
        print(f"✅ 中文翻译总数: {chinese_count}")
        
        # 3. 查找有英文描述的工具
        english_with_desc = db.query(ToolTranslation).filter(
            ToolTranslation.locale == 'en',
            ToolTranslation.description.isnot(None),
            ToolTranslation.description != ''
        ).count()
        print(f"✅ 有描述的英文翻译: {english_with_desc}")
        
        # 4. 查找需要翻译的工具（有英文但没有中文的）
        # 先获取所有有英文翻译的工具ID
        english_tool_ids = [row[0] for row in db.query(ToolTranslation.tool_id).filter(
            ToolTranslation.locale == 'en',
            ToolTranslation.description.isnot(None),
            ToolTranslation.description != ''
        ).all()]
        print(f"✅ 有英文描述的工具ID数量: {len(english_tool_ids)}")
        
        # 获取已有中文翻译的工具ID
        chinese_tool_ids = [row[0] for row in db.query(ToolTranslation.tool_id).filter(
            ToolTranslation.locale == 'zh'
        ).all()]
        print(f"✅ 有中文翻译的工具ID数量: {len(chinese_tool_ids)}")
        
        # 找出需要翻译的工具ID
        need_translation_ids = set(english_tool_ids) - set(chinese_tool_ids)
        print(f"✅ 需要翻译的工具ID数量: {len(need_translation_ids)}")
        
        if need_translation_ids:
            # 显示前5个需要翻译的工具
            sample_ids = list(need_translation_ids)[:5]
            print(f"✅ 示例需要翻译的工具ID: {sample_ids}")
            
            # 获取第一个工具的详细信息
            first_tool_id = sample_ids[0]
            english_translation = db.query(ToolTranslation).filter(
                ToolTranslation.tool_id == first_tool_id,
                ToolTranslation.locale == 'en'
            ).first()
            
            if english_translation:
                print(f"\n=== 第一个需要翻译的工具详情 ===")
                print(f"工具ID: {english_translation.tool_id}")
                print(f"名称: {english_translation.name}")
                print(f"描述: {english_translation.description[:200]}...")
                print(f"长描述: {'有' if english_translation.long_description else '无'}")
                print(f"使用说明: {'有' if english_translation.usage_instructions else '无'}")
                print(f"价格详情: {'有' if english_translation.pricing_details else '无'}")
                print(f"集成信息: {'有' if english_translation.integration_info else '无'}")
                
                # 测试Ollama服务
                print(f"\n=== 测试Ollama服务 ===")
                try:
                    ollama_service = OllamaService()
                    if ollama_service.is_available():
                        print("✅ Ollama服务可用")
                        
                        # 测试翻译
                        prompt = f"""请将以下工具名称翻译成中文，保持简洁：

{english_translation.name}

要求：
1. 翻译要准确、自然、符合中文表达习惯
2. 只返回翻译结果，不要添加其他说明

翻译结果："""
                        
                        print("🔄 测试翻译...")
                        response = ollama_service.call_ollama(
                            prompt=prompt,
                            model='gemma3:latest',
                            temperature=0.3,
                            max_tokens=512,
                            timeout=60
                        )
                        
                        if isinstance(response, str) and response.strip():
                            chinese_name = response.strip()
                            # 清理翻译结果
                            prefixes_to_remove = ['翻译结果：', '翻译结果:', '中文翻译：', '中文翻译:', '翻译：', '翻译:']
                            for prefix in prefixes_to_remove:
                                if chinese_name.startswith(prefix):
                                    chinese_name = chinese_name[len(prefix):].strip()
                                    break
                            
                            print(f"✅ 翻译成功: {english_translation.name} -> {chinese_name}")
                            return True
                        else:
                            print(f"❌ 翻译失败: {response}")
                            return False
                    else:
                        print("❌ Ollama服务不可用")
                        return False
                except Exception as e:
                    print(f"❌ Ollama服务错误: {e}")
                    return False
        else:
            print("⚠️  没有找到需要翻译的工具")
            return False
            
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()

if __name__ == '__main__':
    success = debug_translation_logic()
    if success:
        print("\n🎉 调试完成，逻辑正常！")
    else:
        print("\n❌ 调试发现问题，需要修复")