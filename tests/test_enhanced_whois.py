#!/usr/bin/env python3
"""
增强版WHOIS查询功能测试脚本
"""

import logging
import time
from datetime import datetime
from app.utils.domain_utils import DomainInfoUtils

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_enhanced_whois_query():
    """测试增强版WHOIS查询功能"""
    print("=" * 80)
    print("🚀 增强版WHOIS查询功能测试")
    print("=" * 80)
    
    # 测试域名列表 - 包含不同类型和难度的域名
    test_domains = [
        # 知名域名（应该容易查询到）
        "google.com",
        "github.com",
        "stackoverflow.com",
        
        # 不同TLD的域名
        "example.org",
        "test.net",
        "sample.info",
        
        # 国际域名
        "baidu.com",
        "yandex.ru",
        
        # 新兴TLD
        "example.io",
        "test.co",
        
        # 可能较难查询的域名
        "obscure-domain-test.com",
    ]
    
    results = []
    
    for i, domain in enumerate(test_domains, 1):
        print(f"\n{'='*60}")
        print(f"🔍 测试 {i}/{len(test_domains)}: {domain}")
        print(f"{'='*60}")
        
        start_time = time.time()
        
        try:
            result = DomainInfoUtils.get_domain_registration_date(domain)
            end_time = time.time()
            query_time = end_time - start_time
            
            if isinstance(result, datetime):
                print(f"✅ 查询成功!")
                print(f"   📅 注册日期: {result}")
                print(f"   ⏱️  查询耗时: {query_time:.2f}秒")
                results.append({
                    'domain': domain,
                    'status': 'success',
                    'date': result,
                    'time': query_time
                })
            elif isinstance(result, str):
                print(f"❌ 查询失败: {result}")
                print(f"   ⏱️  查询耗时: {query_time:.2f}秒")
                results.append({
                    'domain': domain,
                    'status': 'error',
                    'error': result,
                    'time': query_time
                })
            else:
                print(f"⚠️  查询无结果")
                print(f"   ⏱️  查询耗时: {query_time:.2f}秒")
                results.append({
                    'domain': domain,
                    'status': 'no_result',
                    'time': query_time
                })
                
        except Exception as e:
            end_time = time.time()
            query_time = end_time - start_time
            print(f"💥 查询异常: {str(e)}")
            print(f"   ⏱️  查询耗时: {query_time:.2f}秒")
            results.append({
                'domain': domain,
                'status': 'exception',
                'error': str(e),
                'time': query_time
            })
        
        # 避免查询过于频繁
        if i < len(test_domains):
            print("⏳ 等待2秒...")
            time.sleep(2)
    
    # 输出测试总结
    print("\n" + "="*80)
    print("📊 测试结果总结")
    print("="*80)
    
    success_count = sum(1 for r in results if r['status'] == 'success')
    error_count = sum(1 for r in results if r['status'] == 'error')
    no_result_count = sum(1 for r in results if r['status'] == 'no_result')
    exception_count = sum(1 for r in results if r['status'] == 'exception')
    
    total_time = sum(r['time'] for r in results)
    avg_time = total_time / len(results) if results else 0
    
    print(f"📈 总测试域名: {len(test_domains)}")
    print(f"✅ 查询成功: {success_count} ({success_count/len(test_domains)*100:.1f}%)")
    print(f"❌ 查询失败: {error_count} ({error_count/len(test_domains)*100:.1f}%)")
    print(f"⚠️  无结果: {no_result_count} ({no_result_count/len(test_domains)*100:.1f}%)")
    print(f"💥 异常: {exception_count} ({exception_count/len(test_domains)*100:.1f}%)")
    print(f"⏱️  总耗时: {total_time:.2f}秒")
    print(f"⏱️  平均耗时: {avg_time:.2f}秒/域名")
    
    # 显示成功的查询结果
    if success_count > 0:
        print(f"\n🎉 成功查询的域名:")
        for result in results:
            if result['status'] == 'success':
                print(f"   📅 {result['domain']}: {result['date']} ({result['time']:.2f}s)")
    
    # 显示失败的查询
    if error_count > 0 or exception_count > 0:
        print(f"\n❌ 失败的查询:")
        for result in results:
            if result['status'] in ['error', 'exception']:
                error_msg = result.get('error', 'Unknown error')
                print(f"   ❌ {result['domain']}: {error_msg[:100]}...")
    
    return results

def test_individual_methods():
    """测试各个查询方法"""
    print("\n" + "="*80)
    print("🔧 测试各个查询方法")
    print("="*80)
    
    test_domain = "google.com"
    print(f"🎯 测试域名: {test_domain}")
    
    methods = [
        ("Python-whois库", DomainInfoUtils._query_with_python_whois),
        ("WHOIS API", DomainInfoUtils._query_with_whois_api),
        ("系统whois命令", DomainInfoUtils._query_with_system_whois),
        ("备用WHOIS服务", DomainInfoUtils._query_with_backup_whois),
    ]
    
    for method_name, method_func in methods:
        print(f"\n🔍 测试方法: {method_name}")
        print("-" * 40)
        
        try:
            start_time = time.time()
            result = method_func(test_domain)
            end_time = time.time()
            query_time = end_time - start_time
            
            if isinstance(result, datetime):
                print(f"   ✅ 成功: {result} ({query_time:.2f}s)")
            elif isinstance(result, str):
                print(f"   ❌ 失败: {result[:100]}... ({query_time:.2f}s)")
            else:
                print(f"   ⚠️  无结果 ({query_time:.2f}s)")
                
        except Exception as e:
            print(f"   💥 异常: {str(e)}")
        
        time.sleep(1)  # 方法间隔

def test_date_parsing():
    """测试日期解析功能"""
    print("\n" + "="*80)
    print("📅 测试日期解析功能")
    print("="*80)
    
    test_date_strings = [
        "2020-01-15",
        "2020-01-15 10:30:00",
        "2020-01-15T10:30:00Z",
        "2020/01/15",
        "15-01-2020",
        "15/01/2020",
        "15.01.2020",
        "01/15/2020",
        "15 Jan 2020",
        "15 January 2020",
        "Jan 15 2020",
        "January 15 2020",
        "2020.01.15",
        "15-Jan-2020",
        "20200115",
        "Creation Date: 2020-01-15T10:30:00Z",
        "Invalid date string",
        "",
        None
    ]
    
    for date_str in test_date_strings:
        try:
            result = DomainInfoUtils._parse_date_string(date_str)
            if result:
                print(f"✅ '{date_str}' -> {result}")
            else:
                print(f"❌ '{date_str}' -> 解析失败")
        except Exception as e:
            print(f"💥 '{date_str}' -> 异常: {str(e)}")

def main():
    """主函数"""
    print("🎯 增强版WHOIS查询功能完整测试")
    print("📅 " + datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    
    try:
        # 测试日期解析功能
        test_date_parsing()
        
        # 测试各个查询方法
        test_individual_methods()
        
        # 询问是否进行完整的域名查询测试
        response = input("\n是否进行完整的域名查询测试？这可能需要较长时间 (y/N): ")
        if response.lower() in ['y', 'yes']:
            results = test_enhanced_whois_query()
            
            # 保存测试结果
            import json
            with open('whois_test_results.json', 'w', encoding='utf-8') as f:
                # 转换datetime对象为字符串以便JSON序列化
                json_results = []
                for result in results:
                    json_result = result.copy()
                    if 'date' in json_result and isinstance(json_result['date'], datetime):
                        json_result['date'] = json_result['date'].isoformat()
                    json_results.append(json_result)
                
                json.dump(json_results, f, indent=2, ensure_ascii=False)
            
            print(f"\n💾 测试结果已保存到: whois_test_results.json")
        else:
            print("⏭️ 跳过完整域名查询测试")
        
        print("\n🎉 所有测试完成！")
        
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
    except Exception as e:
        logger.error(f"❌ 测试过程中出错: {str(e)}")
        print(f"\n💥 错误: {str(e)}")

if __name__ == "__main__":
    main()
