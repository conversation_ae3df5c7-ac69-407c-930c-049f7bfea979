#!/usr/bin/env python3
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from app.core.database import SessionLocal
    print("✅ Database import successful")
    
    from app.models.tool import Tool, ToolTranslation
    print("✅ Models import successful")
    
    from app.services.ollama_service import OllamaService
    print("✅ OllamaService import successful")
    
    # 测试数据库连接
    db = SessionLocal()
    print("✅ Database connection successful")
    
    # 测试查询
    count = db.query(ToolTranslation).filter(ToolTranslation.locale == 'en').count()
    print(f"✅ Found {count} English translations")
    
    count_zh = db.query(ToolTranslation).filter(ToolTranslation.locale == 'zh').count()
    print(f"✅ Found {count_zh} Chinese translations")
    
    db.close()
    print("✅ All tests passed!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()