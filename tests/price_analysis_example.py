#!/usr/bin/env python3
"""
域名价格分析使用示例

这个示例展示了如何使用 generate_price.py 脚本来分析域名的价格信息。
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from generate_price import DomainPriceAnalysisService


def example_single_domain_analysis():
    """示例：分析单个域名的价格信息"""
    print("=== 单个域名价格分析示例 ===")
    
    # 创建数据库会话
    db = SessionLocal()
    
    try:
        # 创建价格分析服务
        service = DomainPriceAnalysisService(db)
        
        # 初始化服务
        if not service.initialize_services():
            print("❌ 服务初始化失败，请检查Ollama是否启动")
            return
        
        # 分析域名价格
        test_domains = [
            "openai.com",
            "github.com", 
            "notion.so",
            "figma.com"
        ]
        
        for domain in test_domains:
            print(f"\n--- 分析域名: {domain} ---")
            
            result = service.analyze_domain_pricing(
                domain=domain,
                model_name='gemma3:latest'
            )
            
            if result["success"]:
                print("✅ 分析成功")
                pricing = result["pricing_analysis"]
                
                # 显示关键信息
                if isinstance(pricing, dict):
                    if "pricing_model" in pricing:
                        print(f"定价模式: {pricing.get('pricing_model', '未知')}")
                    
                    if "plans" in pricing and pricing["plans"]:
                        print("价格方案:")
                        for plan in pricing["plans"][:3]:  # 只显示前3个方案
                            name = plan.get("name", "未知方案")
                            price = plan.get("price", "未知价格")
                            print(f"  - {name}: {price}")
                    
                    if "free_trial" in pricing:
                        trial = pricing["free_trial"]
                        if trial.get("available"):
                            print(f"免费试用: {trial.get('duration', '未知期限')}")
                    
                    if "notes" in pricing:
                        print(f"备注: {pricing['notes']}")
                else:
                    print(f"原始分析结果: {pricing}")
            else:
                print(f"❌ 分析失败: {result.get('error', '未知错误')}")
            
            print("-" * 50)
    
    except Exception as e:
        print(f"❌ 示例执行失败: {str(e)}")
    
    finally:
        db.close()


def example_batch_analysis():
    """示例：批量分析工具价格"""
    print("\n=== 批量工具价格分析示例 ===")
    
    # 创建数据库会话
    db = SessionLocal()
    
    try:
        # 创建价格分析服务
        service = DomainPriceAnalysisService(db)
        
        # 初始化服务
        if not service.initialize_services():
            print("❌ 服务初始化失败，请检查Ollama是否启动")
            return
        
        # 先进行干运行，查看将要处理的工具
        print("--- 干运行模式：查看将要处理的工具 ---")
        dry_run_result = service.batch_analyze_tools_pricing(
            locale='en',
            limit=5,
            dry_run=True
        )
        
        if dry_run_result["success"] and "tools" in dry_run_result:
            print(f"找到 {len(dry_run_result['tools'])} 个工具:")
            for tool in dry_run_result["tools"]:
                print(f"  - {tool['name']} ({tool['url']})")
        
        # 询问用户是否继续
        user_input = input("\n是否继续执行实际分析？(y/N): ")
        if user_input.lower() != 'y':
            print("用户取消操作")
            return
        
        # 执行实际分析
        print("\n--- 执行实际价格分析 ---")
        result = service.batch_analyze_tools_pricing(
            locale='en',
            model_name='gemma3:latest',
            limit=3,  # 限制为3个工具以节省时间
            dry_run=False
        )
        
        if result["success"]:
            print(f"✅ 批量分析完成")
            print(f"处理: {result['processed']} 个工具")
            print(f"成功: {result['succeeded']} 个")
            print(f"失败: {result['failed']} 个")
            
            if result.get("results"):
                print("\n分析结果摘要:")
                for item in result["results"]:
                    print(f"工具: {item['tool_name']}")
                    print(f"域名: {item['domain']}")
                    pricing = item.get('pricing_analysis', {})
                    if isinstance(pricing, dict) and 'pricing_model' in pricing:
                        print(f"定价模式: {pricing.get('pricing_model', '未知')}")
                    print("---")
            
            if result.get("errors"):
                print("\n错误信息:")
                for error in result["errors"]:
                    print(f"  - {error}")
        else:
            print(f"❌ 批量分析失败: {result.get('error', '未知错误')}")
    
    except Exception as e:
        print(f"❌ 示例执行失败: {str(e)}")
    
    finally:
        db.close()


def example_service_status():
    """示例：检查服务状态"""
    print("\n=== 服务状态检查示例 ===")
    
    # 创建数据库会话
    db = SessionLocal()
    
    try:
        # 创建价格分析服务
        service = DomainPriceAnalysisService(db)
        
        # 初始化服务
        print("正在初始化服务...")
        success = service.initialize_services()
        
        if success:
            print("✅ 服务初始化成功")
            
            # 检查Ollama服务状态
            if service.ollama_service:
                print(f"Ollama服务地址: {service.ollama_service.ip_address}:{service.ollama_service.port}")
                
                # 获取可用模型
                models = service.get_available_models()
                print(f"可用模型数量: {len(models)}")
                
                if models:
                    print("可用模型:")
                    for model in models[:5]:  # 只显示前5个模型
                        name = model.get('name', 'Unknown')
                        size = model.get('size', 'Unknown size')
                        print(f"  - {name} ({size})")
            
            # 检查内容提取服务
            if service.content_extractor:
                print("✅ 内容提取服务可用")
            else:
                print("❌ 内容提取服务不可用")
        else:
            print("❌ 服务初始化失败")
    
    except Exception as e:
        print(f"❌ 状态检查失败: {str(e)}")
    
    finally:
        db.close()


def main():
    """主函数"""
    print("域名价格分析功能示例")
    print("=" * 50)
    
    # 检查服务状态
    example_service_status()
    
    # 单个域名分析示例
    example_single_domain_analysis()
    
    # 批量分析示例
    example_batch_analysis()
    
    print("\n示例执行完成！")
    print("\n使用说明:")
    print("1. 确保Ollama服务正在运行")
    print("2. 确保已安装所需的模型（如 gemma3:latest）")
    print("3. 运行 python generate_price.py --help 查看更多选项")


if __name__ == '__main__':
    main()
