#!/usr/bin/env python3
"""
数据库表创建脚本
运行此脚本来创建所有必要的数据库表
"""

import sys
import logging
from sqlalchemy import create_engine
from app.core.config import settings
from app.core.database import Base
from app.models.tool import Tool, ToolTranslation, Category, CategoryTranslation, Tag, TagTranslation, ToolFeature

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_tables():
    """创建所有数据库表"""
    try:
        logger.info("开始创建数据库表...")
        logger.info(f"数据库URL: {settings.DATABASE_URL}")
        
        # 创建数据库引擎
        engine = create_engine(settings.DATABASE_URL)
        
        # 创建所有表
        Base.metadata.create_all(bind=engine)
        
        logger.info("✅ 数据库表创建成功！")
        logger.info("创建的表包括:")
        logger.info("  - categories (分类表)")
        logger.info("  - category_translations (分类翻译表)")
        logger.info("  - tags (标签表)")
        logger.info("  - tag_translations (标签翻译表)")
        logger.info("  - tools (工具表)")
        logger.info("  - tool_translations (工具翻译表)")
        logger.info("  - tool_features (工具特性表)")
        logger.info("  - tool_categories (工具-分类关联表)")
        logger.info("  - tool_tags (工具-标签关联表)")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 创建数据库表失败: {str(e)}")
        return False

def drop_tables():
    """删除所有数据库表（谨慎使用）"""
    try:
        logger.warning("⚠️  准备删除所有数据库表...")
        
        # 确认操作
        confirm = input("确定要删除所有表吗？这将删除所有数据！(输入 'YES' 确认): ")
        if confirm != 'YES':
            logger.info("操作已取消")
            return False
        
        logger.info("开始删除数据库表...")
        
        # 创建数据库引擎
        engine = create_engine(settings.DATABASE_URL)
        
        # 删除所有表
        Base.metadata.drop_all(bind=engine)
        
        logger.info("✅ 数据库表删除成功！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 删除数据库表失败: {str(e)}")
        return False

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法:")
        print("  python create_tables.py create   # 创建表")
        print("  python create_tables.py drop     # 删除表")
        print("  python create_tables.py recreate # 重新创建表")
        return
    
    command = sys.argv[1].lower()
    
    if command == "create":
        create_tables()
    elif command == "drop":
        drop_tables()
    elif command == "recreate":
        logger.info("重新创建数据库表...")
        if drop_tables():
            create_tables()
    else:
        logger.error(f"未知命令: {command}")
        print("可用命令: create, drop, recreate")

if __name__ == "__main__":
    main()
