#!/usr/bin/env python3
"""
测试增强后的日志功能
"""

import logging
import asyncio
from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.services.toolify_service import ToolifyAPIService
from app.services.database_service import DatabaseService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_enhanced_crawler_logs():
    """测试增强后的爬虫日志功能"""
    try:
        logger.info("=" * 80)
        logger.info("🧪 [测试开始] 测试增强后的爬虫日志功能")
        logger.info("=" * 80)
        
        # 创建数据库会话
        db = SessionLocal()
        
        try:
            # 创建服务实例
            service = ToolifyAPIService()
            db_service = DatabaseService(db)
            
            # 记录数据库初始状态
            initial_tools_count = db_service.get_tools_count()
            initial_categories_count = db_service.get_categories_count()
            logger.info(f"📊 [初始状态] 工具数量: {initial_tools_count}")
            logger.info(f"📊 [初始状态] 分类数量: {initial_categories_count}")
            
            # 测试单页数据获取
            logger.info("🌐 [API测试] 开始测试单页数据获取...")
            result = service.fetch_tools(page=1, per_page=2, tool_type=1)
            
            if not result['success']:
                logger.error(f"❌ [API测试] 获取数据失败: {result['message']}")
                return
            
            # 获取工具数据
            response_data = result['data'].get('data', {})
            tools = response_data.get('data', [])
            
            logger.info(f"✅ [API测试] 成功获取 {len(tools)} 条工具数据")
            
            if not tools:
                logger.warning("⚠️ [API测试] 没有获取到工具数据")
                return
            
            # 测试数据转换
            logger.info("🔄 [数据转换测试] 开始测试数据转换...")
            transformed_tools = []
            
            for i, tool in enumerate(tools[:2], 1):  # 只测试前2个工具
                logger.info(f"🔄 [数据转换测试] 转换第 {i} 个工具")
                transformed_tool = service.transform_tool_data(tool)
                transformed_tools.append(transformed_tool)
            
            logger.info(f"✅ [数据转换测试] 成功转换 {len(transformed_tools)} 条工具数据")
            
            # 测试数据库保存
            logger.info("💾 [数据库测试] 开始测试数据库保存...")
            saved_count, errors = db_service.save_tools_to_database(transformed_tools)
            
            # 记录最终状态
            final_tools_count = db_service.get_tools_count()
            final_categories_count = db_service.get_categories_count()
            
            logger.info("=" * 80)
            logger.info("🎉 [测试完成] 增强日志功能测试结果:")
            logger.info(f"📊 [最终结果] 成功保存工具数: {saved_count}")
            logger.info(f"📊 [最终结果] 错误数量: {len(errors)}")
            logger.info(f"📊 [最终结果] 工具数量变化: {initial_tools_count} → {final_tools_count} (增加: {final_tools_count - initial_tools_count})")
            logger.info(f"📊 [最终结果] 分类数量变化: {initial_categories_count} → {final_categories_count} (增加: {final_categories_count - initial_categories_count})")
            
            if errors:
                logger.warning("⚠️ [错误列表]:")
                for i, error in enumerate(errors, 1):
                    logger.warning(f"  {i}. {error}")
            
            logger.info("=" * 80)
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"❌ [测试异常] 测试过程中出现异常: {str(e)}")
        import traceback
        logger.error(f"❌ [异常详情] {traceback.format_exc()}")

if __name__ == "__main__":
    test_enhanced_crawler_logs()
