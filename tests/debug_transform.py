#!/usr/bin/env python3
"""
调试数据转换问题
"""

import logging
import traceback
from app.services.toolify_service import ToolifyAPIService

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def debug_transform():
    """调试数据转换"""
    try:
        service = ToolifyAPIService()
        result = service.fetch_tools(page=1, per_page=1, tool_type=1)
        
        if not result['success']:
            print("❌ API调用失败")
            return
        
        tools = result['data']['data']['data']  # 正确的数据路径
        if not tools:
            print("❌ 没有获取到工具数据")
            return
        
        print(f"✅ 获取到 {len(tools)} 条工具数据")
        print(f"tools的类型: {type(tools)}")
        print(f"tools的内容: {tools}")

        # 检查第一个工具
        if isinstance(tools, list) and len(tools) > 0:
            first_tool = tools[0]
        else:
            print("❌ tools不是列表或为空")
            return
        print(f"第一个工具数据类型: {type(first_tool)}")
        print(f"第一个工具keys: {list(first_tool.keys())[:10]}")
        print(f"工具名称: {first_tool.get('name', 'Unknown')}")
        
        # 尝试转换
        print("开始转换数据...")
        try:
            transformed = service.transform_tool_data(first_tool)
            print("✅ 数据转换成功!")
            print(f"转换后的tool_id: {transformed.get('tool_id')}")
            print(f"转换后的name: {transformed.get('name')}")
            print(f"转换后的categories数量: {len(transformed.get('categories', []))}")
        except Exception as e:
            print(f"❌ 数据转换失败: {str(e)}")
            print("详细错误信息:")
            traceback.print_exc()
            
    except Exception as e:
        print(f"❌ 调试过程出错: {str(e)}")
        traceback.print_exc()

if __name__ == "__main__":
    debug_transform()
