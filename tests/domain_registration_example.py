#!/usr/bin/env python3
"""
域名注册日期功能使用示例
"""

import logging
from datetime import datetime
from app.services.domain_registration_service import DomainRegistrationService
from app.core.database import SessionLocal

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def example_basic_usage():
    """基础使用示例"""
    print("=" * 60)
    print("🚀 域名注册日期服务基础使用示例")
    print("=" * 60)
    
    # 创建服务实例
    service = DomainRegistrationService()
    
    # 1. 测试域名提取
    print("\n1️⃣ 域名提取测试:")
    test_urls = [
        "https://www.google.com/search",
        "http://github.com/user/repo",
        "https://openai.com/api/v1",
        "www.example.com",
        "chatgpt.com"
    ]
    
    for url in test_urls:
        domain = service.extract_domain_from_url(url)
        print(f"   📎 {url:<35} -> {domain}")
    
    # 2. 测试WHOIS查询（选择几个知名域名）
    print("\n2️⃣ WHOIS查询测试:")
    test_domains = ["google.com", "github.com"]
    
    for domain in test_domains:
        print(f"   🔍 查询域名: {domain}")
        registration_date = service.query_domain_registration_date(domain)
        if registration_date:
            print(f"   ✅ 注册日期: {registration_date}")
        else:
            print(f"   ❌ 查询失败")
        print()

def example_database_operations():
    """数据库操作示例"""
    print("=" * 60)
    print("🗄️ 数据库操作示例")
    print("=" * 60)
    
    # 创建数据库会话
    db = SessionLocal()
    service = DomainRegistrationService(db)
    
    try:
        # 1. 确保数据库字段存在
        print("\n1️⃣ 检查数据库字段:")
        success = service.add_domain_registration_date_column()
        if success:
            print("   ✅ domain_registration_date字段已就绪")
        else:
            print("   ❌ 字段添加失败")
            return
        
        # 2. 查询需要更新的工具
        print("\n2️⃣ 查询需要更新的工具:")
        tools = service.get_tools_without_registration_date(limit=5)
        print(f"   📊 找到 {len(tools)} 个工具需要更新注册日期")
        
        if tools:
            print("   📋 工具列表:")
            for i, tool in enumerate(tools[:3], 1):
                print(f"      {i}. {tool.tool_id}: {tool.url}")
            if len(tools) > 3:
                print(f"      ... 还有 {len(tools) - 3} 个工具")
        
    finally:
        db.close()

def example_batch_update():
    """批量更新示例"""
    print("=" * 60)
    print("🔄 批量更新示例")
    print("=" * 60)
    
    # 询问用户是否执行实际更新
    response = input("\n⚠️  这将实际更新数据库中的数据，是否继续？(y/N): ")
    if response.lower() not in ['y', 'yes']:
        print("   ⏭️ 跳过批量更新示例")
        return
    
    service = DomainRegistrationService()
    
    print("\n🚀 开始小批量测试更新...")
    print("   ⚙️ 配置: 批次大小=3, 最大工具数=5")
    
    # 执行小批量更新
    result = service.batch_update_registration_dates(
        batch_size=3,
        max_tools=5
    )
    
    print("\n📊 更新结果:")
    print(f"   📈 总工具数: {result['total_tools']}")
    print(f"   ✅ 处理成功: {result['success']}")
    print(f"   ❌ 处理失败: {result['failed']}")
    print(f"   📝 已处理: {result['processed']}")
    
    if result['errors']:
        print("\n❌ 错误详情:")
        for i, error in enumerate(result['errors'][:3], 1):
            print(f"   {i}. {error.get('tool_id', 'Unknown')}: {error.get('error', 'Unknown error')}")

def example_manual_processing():
    """手动处理单个工具示例"""
    print("=" * 60)
    print("🔧 手动处理单个工具示例")
    print("=" * 60)
    
    # 创建数据库会话
    db = SessionLocal()
    service = DomainRegistrationService(db)
    
    try:
        # 获取一个需要更新的工具
        tools = service.get_tools_without_registration_date(limit=1)
        
        if not tools:
            print("   📭 没有需要更新的工具")
            return
        
        tool = tools[0]
        print(f"\n🔍 处理工具:")
        print(f"   ID: {tool.tool_id}")
        print(f"   URL: {tool.url}")
        
        # 手动处理
        success, message = service.process_single_tool(tool)
        
        if success:
            print(f"   ✅ 处理成功: {message}")
        else:
            print(f"   ❌ 处理失败: {message}")
    
    finally:
        db.close()

def main():
    """主函数"""
    print("🎯 域名注册日期功能完整示例")
    print("📅 " + datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    
    try:
        # 基础功能示例
        example_basic_usage()
        
        # 数据库操作示例
        example_database_operations()
        
        # 手动处理示例
        example_manual_processing()
        
        # 批量更新示例
        example_batch_update()
        
        print("\n" + "=" * 60)
        print("🎉 所有示例执行完成！")
        print("=" * 60)
        
    except Exception as e:
        logger.error(f"❌ 示例执行过程中出错: {str(e)}")
        print(f"\n💥 错误: {str(e)}")

if __name__ == "__main__":
    main()
