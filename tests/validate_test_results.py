#!/usr/bin/env python3
"""
测试结果验证脚本

验证域名价格分析的测试结果是否符合预期
"""

import json
import sys
from pathlib import Path


def validate_json_structure(result_data):
    """验证JSON结构是否正确"""
    print("🔍 验证JSON结构...")
    
    required_fields = ["success", "domain", "url", "pricing_analysis"]
    missing_fields = []
    
    for field in required_fields:
        if field not in result_data:
            missing_fields.append(field)
    
    if missing_fields:
        print(f"❌ 缺少必需字段: {missing_fields}")
        return False
    
    print("✅ JSON结构正确")
    return True


def validate_pricing_analysis(pricing_data):
    """验证价格分析数据"""
    print("🔍 验证价格分析数据...")
    
    required_fields = ["pricing_model", "plans", "free_trial", "currency"]
    missing_fields = []
    
    for field in required_fields:
        if field not in pricing_data:
            missing_fields.append(field)
    
    if missing_fields:
        print(f"❌ 价格分析缺少字段: {missing_fields}")
        return False
    
    # 验证plans数组
    if not isinstance(pricing_data["plans"], list):
        print("❌ plans字段应该是数组")
        return False
    
    if len(pricing_data["plans"]) == 0:
        print("❌ plans数组不能为空")
        return False
    
    # 验证每个plan的结构
    for i, plan in enumerate(pricing_data["plans"]):
        plan_fields = ["name", "price", "billing_cycle", "features", "is_free"]
        for field in plan_fields:
            if field not in plan:
                print(f"❌ Plan {i+1} 缺少字段: {field}")
                return False
    
    print("✅ 价格分析数据结构正确")
    return True


def analyze_gumloop_results(pricing_data):
    """分析Gumloop的具体结果"""
    print("🔍 分析Gumloop价格信息...")
    
    plans = pricing_data["plans"]
    plan_names = [plan["name"] for plan in plans]
    
    print(f"发现 {len(plans)} 个价格方案:")
    for plan in plans:
        price = plan["price"]
        name = plan["name"]
        is_free = plan["is_free"]
        features_count = len(plan["features"])
        
        status = "免费" if is_free else "付费"
        print(f"  - {name}: {price} ({status}, {features_count}个功能)")
    
    # 检查是否有免费方案
    free_plans = [plan for plan in plans if plan["is_free"]]
    if free_plans:
        print(f"✅ 发现 {len(free_plans)} 个免费方案")
    else:
        print("⚠️ 没有发现免费方案")
    
    # 检查价格范围
    paid_plans = [plan for plan in plans if not plan["is_free"] and plan["price"] != "Custom"]
    if paid_plans:
        prices = []
        for plan in paid_plans:
            try:
                # 提取数字价格
                price_str = plan["price"].replace("$", "").replace(",", "")
                if price_str.isdigit():
                    prices.append(int(price_str))
            except:
                pass
        
        if prices:
            min_price = min(prices)
            max_price = max(prices)
            print(f"💰 价格范围: ${min_price} - ${max_price}")
    
    return True


def validate_test_file(file_path):
    """验证测试结果文件"""
    print(f"📁 验证文件: {file_path}")
    
    if not Path(file_path).exists():
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except json.JSONDecodeError as e:
        print(f"❌ JSON格式错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return False
    
    print("✅ 文件读取成功")
    
    # 验证基本结构
    if not validate_json_structure(data):
        return False
    
    # 检查是否成功
    if not data.get("success", False):
        print(f"❌ 分析失败: {data.get('error', '未知错误')}")
        return False
    
    print("✅ 分析成功")
    
    # 验证价格分析
    pricing_data = data.get("pricing_analysis", {})
    if not validate_pricing_analysis(pricing_data):
        return False
    
    # 分析具体结果
    domain = data.get("domain", "")
    if "gumloop" in domain.lower():
        analyze_gumloop_results(pricing_data)
    
    return True


def run_comprehensive_test():
    """运行综合测试"""
    print("🚀 开始综合测试验证")
    print("=" * 50)
    
    # 测试文件列表
    test_files = [
        "test_result.json",
        "gumloop_pricing_analysis.json"
    ]
    
    success_count = 0
    total_count = len(test_files)
    
    for file_path in test_files:
        print(f"\n📋 测试文件: {file_path}")
        print("-" * 30)
        
        if validate_test_file(file_path):
            print("✅ 验证通过")
            success_count += 1
        else:
            print("❌ 验证失败")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {success_count}/{total_count} 通过")
    
    if success_count == total_count:
        print("🎉 所有测试都通过了！")
        return True
    else:
        print("⚠️ 部分测试失败，请检查上述错误信息")
        return False


def main():
    """主函数"""
    print("域名价格分析测试结果验证工具")
    print("=" * 50)
    
    if len(sys.argv) > 1:
        # 验证指定文件
        file_path = sys.argv[1]
        success = validate_test_file(file_path)
    else:
        # 运行综合测试
        success = run_comprehensive_test()
    
    if success:
        print("\n✅ 验证完成，所有测试都符合预期！")
        return 0
    else:
        print("\n❌ 验证失败，请检查测试结果或代码")
        return 1


if __name__ == '__main__':
    sys.exit(main())
