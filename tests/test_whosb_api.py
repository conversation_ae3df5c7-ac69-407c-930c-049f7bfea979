#!/usr/bin/env python3
"""
Who.sb API查询功能测试脚本
"""

import logging
import time
import asyncio
from datetime import datetime
from app.utils.domain_utils import DomainInfoUtils

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_whosb_api_method():
    """测试Who.sb API查询方法"""
    print("🧪 测试Who.sb API查询方法")
    print("=" * 60)
    
    # 测试域名列表
    test_domains = [
        "google.com",
        "github.com",
        "stackoverflow.com",
        "example.com",
        "openai.com"
    ]
    
    results = []
    
    for i, domain in enumerate(test_domains, 1):
        print(f"\n🔍 测试 {i}/{len(test_domains)}: {domain}")
        print("-" * 40)
        
        start_time = time.time()
        
        try:
            result = DomainInfoUtils._query_with_whosb_api(domain)
            end_time = time.time()
            query_time = end_time - start_time
            
            if isinstance(result, datetime):
                print(f"✅ 查询成功: {result}")
                print(f"⏱️ 查询耗时: {query_time:.2f}秒")
                results.append({
                    'domain': domain,
                    'status': 'success',
                    'date': result,
                    'time': query_time
                })
            elif isinstance(result, str):
                print(f"❌ 查询失败: {result}")
                print(f"⏱️ 查询耗时: {query_time:.2f}秒")
                results.append({
                    'domain': domain,
                    'status': 'error',
                    'error': result,
                    'time': query_time
                })
            else:
                print(f"⚠️ 查询无结果")
                print(f"⏱️ 查询耗时: {query_time:.2f}秒")
                results.append({
                    'domain': domain,
                    'status': 'no_result',
                    'time': query_time
                })
                
        except Exception as e:
            end_time = time.time()
            query_time = end_time - start_time
            print(f"💥 查询异常: {str(e)}")
            print(f"⏱️ 查询耗时: {query_time:.2f}秒")
            results.append({
                'domain': domain,
                'status': 'exception',
                'error': str(e),
                'time': query_time
            })
        
        # 避免查询过于频繁
        if i < len(test_domains):
            print("⏳ 等待2秒...")
            time.sleep(2)
    
    # 输出测试总结
    print("\n" + "="*60)
    print("📊 Who.sb API测试结果总结")
    print("="*60)
    
    success_count = sum(1 for r in results if r['status'] == 'success')
    error_count = sum(1 for r in results if r['status'] == 'error')
    no_result_count = sum(1 for r in results if r['status'] == 'no_result')
    exception_count = sum(1 for r in results if r['status'] == 'exception')
    
    total_time = sum(r['time'] for r in results)
    avg_time = total_time / len(results) if results else 0
    
    print(f"📈 总测试域名: {len(test_domains)}")
    print(f"✅ 查询成功: {success_count} ({success_count/len(test_domains)*100:.1f}%)")
    print(f"❌ 查询失败: {error_count} ({error_count/len(test_domains)*100:.1f}%)")
    print(f"⚠️ 无结果: {no_result_count} ({no_result_count/len(test_domains)*100:.1f}%)")
    print(f"💥 异常: {exception_count} ({exception_count/len(test_domains)*100:.1f}%)")
    print(f"⏱️ 总耗时: {total_time:.2f}秒")
    print(f"⏱️ 平均耗时: {avg_time:.2f}秒/域名")
    
    # 显示成功的查询结果
    if success_count > 0:
        print(f"\n🎉 成功查询的域名:")
        for result in results:
            if result['status'] == 'success':
                print(f"   📅 {result['domain']}: {result['date']} ({result['time']:.2f}s)")
    
    return results

def test_async_function_directly():
    """直接测试异步函数"""
    print("\n" + "="*60)
    print("🔧 直接测试异步函数")
    print("="*60)
    
    async def test_async():
        domain = "google.com"
        print(f"🌐 直接测试异步查询: {domain}")
        
        try:
            result = await DomainInfoUtils._get_domain_info_async(domain)
            if result:
                print(f"✅ 异步查询成功:")
                print(f"   📅 注册时间: {result[0]}")
                print(f"   🔄 更新时间: {result[1]}")
                print(f"   ⏰ 过期时间: {result[2]}")
            else:
                print(f"❌ 异步查询失败")
        except Exception as e:
            print(f"💥 异步查询异常: {str(e)}")
    
    # 运行异步测试
    asyncio.run(test_async())

def test_integration_with_main_function():
    """测试与主查询函数的集成"""
    print("\n" + "="*60)
    print("🔗 测试与主查询函数的集成")
    print("="*60)
    
    test_domain = "github.com"
    print(f"🎯 测试域名: {test_domain}")
    
    start_time = time.time()
    result = DomainInfoUtils.get_domain_registration_date(test_domain)
    end_time = time.time()
    query_time = end_time - start_time
    
    print(f"⏱️ 总查询耗时: {query_time:.2f}秒")
    
    if isinstance(result, datetime):
        print(f"✅ 集成查询成功: {result}")
    elif isinstance(result, str):
        print(f"❌ 集成查询失败: {result}")
    else:
        print(f"⚠️ 集成查询无结果")

def main():
    """主函数"""
    print("🎯 Who.sb API查询功能完整测试")
    print("📅 " + datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    
    try:
        # 测试Who.sb API方法
        results = test_whosb_api_method()
        
        # 直接测试异步函数
        test_async_function_directly()
        
        # 测试集成
        test_integration_with_main_function()
        
        print("\n🎉 所有测试完成！")
        
        # 保存测试结果
        import json
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"whosb_api_test_results_{timestamp}.json"
        
        # 转换datetime对象为字符串以便JSON序列化
        json_results = []
        for result in results:
            json_result = result.copy()
            if 'date' in json_result and isinstance(json_result['date'], datetime):
                json_result['date'] = json_result['date'].isoformat()
            json_results.append(json_result)
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(json_results, f, indent=2, ensure_ascii=False)
        
        print(f"💾 测试结果已保存到: {filename}")
        
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
    except Exception as e:
        logger.error(f"❌ 测试过程中出错: {str(e)}")
        print(f"\n💥 错误: {str(e)}")

if __name__ == "__main__":
    main()
