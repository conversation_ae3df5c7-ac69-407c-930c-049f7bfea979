#!/usr/bin/env python3
import sys
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

print("脚本开始执行...")

def main():
    print("main函数开始...")
    
    parser = argparse.ArgumentParser(description='测试脚本')
    parser.add_argument('--status', action='store_true', help='显示状态')
    parser.add_argument('--limit', type=int, default=1, help='限制数量')
    
    args = parser.parse_args()
    print(f"解析参数: status={args.status}, limit={args.limit}")
    
    if args.status:
        print("显示状态...")
        from app.core.database import SessionLocal
        from app.models.tool import ToolTranslation
        
        db = SessionLocal()
        try:
            english_count = db.query(ToolTranslation).filter(ToolTranslation.locale == 'en').count()
            chinese_count = db.query(ToolTranslation).filter(ToolTranslation.locale == 'zh').count()
            print(f"英文翻译: {english_count}")
            print(f"中文翻译: {chinese_count}")
        finally:
            db.close()
    else:
        print("执行翻译...")
        # 这里可以添加翻译逻辑
    
    print("main函数完成")
    return 0

if __name__ == '__main__':
    print("__main__ 开始...")
    try:
        result = main()
        print(f"脚本完成，返回码: {result}")
        sys.exit(result)
    except Exception as e:
        print(f"脚本出错: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)