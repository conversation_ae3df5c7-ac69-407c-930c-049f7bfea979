#!/usr/bin/env python3
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.core.database import SessionLocal
from app.models.tool import Tool, ToolTranslation
from app.services.ollama_service import OllamaService

def test_translation():
    """测试翻译功能"""
    db = SessionLocal()
    
    try:
        # 查找一个需要翻译的英文工具
        english_translation = db.query(ToolTranslation).filter(
            ToolTranslation.locale == 'en',
            ToolTranslation.description.isnot(None),
            ToolTranslation.description != ''
        ).first()
        
        if not english_translation:
            print("❌ 没有找到英文翻译")
            return
        
        print(f"✅ 找到英文翻译: {english_translation.tool_id}")
        print(f"   名称: {english_translation.name}")
        print(f"   描述: {english_translation.description[:100]}...")
        
        # 检查是否已有中文翻译
        chinese_translation = db.query(ToolTranslation).filter(
            ToolTranslation.tool_id == english_translation.tool_id,
            ToolTranslation.locale == 'zh'
        ).first()
        
        if chinese_translation:
            print("⚠️  已存在中文翻译，跳过")
            return
        
        # 初始化Ollama服务
        print("🔄 初始化Ollama服务...")
        ollama_service = OllamaService()
        
        if not ollama_service.is_available():
            print("❌ Ollama服务不可用")
            return
        
        print("✅ Ollama服务可用")
        
        # 测试翻译
        prompt = f"""请将以下工具名称翻译成中文，保持简洁：

{english_translation.name}

要求：
1. 翻译要准确、自然、符合中文表达习惯
2. 只返回翻译结果，不要添加其他说明

翻译结果："""
        
        print("🔄 开始翻译...")
        response = ollama_service.call_ollama(
            prompt=prompt,
            model='gemma3:latest',
            temperature=0.3,
            max_tokens=512,
            timeout=60
        )
        
        if isinstance(response, dict) and 'error' in response:
            print(f"❌ 翻译失败: {response['error']}")
            return
        
        if isinstance(response, str) and response.strip():
            chinese_name = response.strip()
            print(f"✅ 翻译成功: {english_translation.name} -> {chinese_name}")
            
            # 创建中文翻译记录（仅测试，不实际保存）
            print("🔄 测试创建中文翻译记录...")
            chinese_translation = ToolTranslation(
                tool_id=english_translation.tool_id,
                locale='zh',
                name=chinese_name,
                description=f"[测试] {english_translation.description}",  # 简单测试
            )
            
            print("✅ 测试成功！可以创建中文翻译")
        else:
            print("❌ 翻译返回空结果")
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == '__main__':
    test_translation()