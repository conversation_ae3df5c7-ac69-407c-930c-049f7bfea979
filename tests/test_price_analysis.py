#!/usr/bin/env python3
"""
价格分析功能测试脚本

用于测试修复后的价格分析功能是否正常工作
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试导入是否正常"""
    try:
        from app.services.ollama_service import OllamaService
        from app.services.content_extractor_service import ContentExtractorService
        print("✅ 导入测试通过")
        return True
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_content_extraction():
    """测试内容提取功能"""
    try:
        from app.services.content_extractor_service import ContentExtractorService
        
        extractor = ContentExtractorService()
        
        # 测试简单的网站
        test_url = "https://httpbin.org/html"
        result = extractor.extract_website_content(test_url, max_chunks=2, chunk_size=1000)
        
        if result and result.get("chunks"):
            print(f"✅ 内容提取测试通过 - 提取到 {len(result['chunks'])} 个内容块")
            return True
        else:
            print("❌ 内容提取测试失败 - 未能提取到内容")
            return False
            
    except Exception as e:
        print(f"❌ 内容提取测试异常: {e}")
        return False

def test_ollama_connection():
    """测试Ollama连接"""
    try:
        from app.services.ollama_service import OllamaService
        
        # 尝试连接Ollama
        ollama = OllamaService()
        
        if ollama.is_available():
            print("✅ Ollama连接测试通过")
            
            # 测试模型列表
            models = ollama.list_available_models()
            print(f"   可用模型数量: {len(models)}")
            
            return True
        else:
            print("❌ Ollama连接测试失败 - 服务不可用")
            return False
            
    except Exception as e:
        print(f"❌ Ollama连接测试异常: {e}")
        return False

def test_database_connection():
    """测试数据库连接"""
    try:
        from app.core.database import test_database_connection
        
        success, message = test_database_connection()
        if success:
            print("✅ 数据库连接测试通过")
            return True
        else:
            print(f"❌ 数据库连接测试失败: {message}")
            return False
            
    except Exception as e:
        print(f"❌ 数据库连接测试异常: {e}")
        return False

def main():
    """运行所有测试"""
    print("🧪 开始运行价格分析功能测试\n")
    
    tests = [
        ("导入测试", test_imports),
        ("内容提取测试", test_content_extraction),
        ("Ollama连接测试", test_ollama_connection),
        ("数据库连接测试", test_database_connection),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"🔍 运行 {test_name}...")
        try:
            if test_func():
                passed += 1
            print()
        except Exception as e:
            print(f"❌ {test_name} 发生异常: {e}\n")
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！价格分析功能应该可以正常工作")
        return 0
    else:
        print("⚠️  部分测试失败，请检查相关配置")
        return 1

if __name__ == "__main__":
    sys.exit(main())