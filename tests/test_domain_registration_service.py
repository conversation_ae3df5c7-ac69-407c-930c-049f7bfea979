#!/usr/bin/env python3
"""
域名注册日期服务测试脚本
"""

import sys
import logging
from datetime import datetime
from app.core.database import SessionLocal
from app.services.domain_registration_service import DomainRegistrationService
from app.models.tool import Tool

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_domain_extraction():
    """测试域名提取功能"""
    logger.info("🧪 测试域名提取功能...")
    
    service = DomainRegistrationService()
    
    test_urls = [
        "https://www.google.com",
        "http://github.com/user/repo",
        "https://openai.com/api",
        "www.example.com",
        "invalid-url",
        ""
    ]
    
    for url in test_urls:
        domain = service.extract_domain_from_url(url)
        logger.info(f"  URL: {url} -> 域名: {domain}")
    
    logger.info("✅ 域名提取测试完成")

def test_whois_query():
    """测试WHOIS查询功能"""
    logger.info("🧪 测试WHOIS查询功能...")
    
    service = DomainRegistrationService()
    
    test_domains = [
        "google.com",
        "github.com",
        "example.com"
    ]
    
    for domain in test_domains:
        logger.info(f"🔍 查询域名: {domain}")
        registration_date = service.query_domain_registration_date(domain)
        if registration_date:
            logger.info(f"  ✅ 注册日期: {registration_date}")
        else:
            logger.warning(f"  ⚠️ 未能获取注册日期")
    
    logger.info("✅ WHOIS查询测试完成")

def test_database_operations():
    """测试数据库操作"""
    logger.info("🧪 测试数据库操作...")
    
    db = SessionLocal()
    service = DomainRegistrationService(db)
    
    try:
        # 测试添加字段
        logger.info("🔧 测试添加数据库字段...")
        success = service.add_domain_registration_date_column()
        if success:
            logger.info("  ✅ 字段添加成功")
        else:
            logger.warning("  ⚠️ 字段添加失败")
        
        # 测试查询没有注册日期的工具
        logger.info("🔍 测试查询没有注册日期的工具...")
        tools = service.get_tools_without_registration_date(limit=5)
        logger.info(f"  📊 找到 {len(tools)} 个工具需要更新")
        
        for tool in tools[:3]:  # 只显示前3个
            logger.info(f"    - {tool.tool_id}: {tool.url}")
        
    finally:
        db.close()
    
    logger.info("✅ 数据库操作测试完成")

def test_batch_update():
    """测试批量更新功能（小批量测试）"""
    logger.info("🧪 测试批量更新功能...")
    
    service = DomainRegistrationService()
    
    # 小批量测试，只处理2个工具
    result = service.batch_update_registration_dates(
        batch_size=2,
        max_tools=2
    )
    
    logger.info("📊 批量更新测试结果:")
    logger.info(f"  - 总工具数: {result['total_tools']}")
    logger.info(f"  - 已处理: {result['processed']}")
    logger.info(f"  - 成功: {result['success']}")
    logger.info(f"  - 失败: {result['failed']}")
    
    if result['errors']:
        logger.info("  - 错误信息:")
        for error in result['errors'][:2]:
            logger.info(f"    * {error}")
    
    logger.info("✅ 批量更新测试完成")

def main():
    """主函数"""
    logger.info("🚀 开始域名注册日期服务测试...")
    
    try:
        # 基础功能测试
        test_domain_extraction()
        print("-" * 50)
        
        test_whois_query()
        print("-" * 50)
        
        test_database_operations()
        print("-" * 50)
        
        # 询问是否进行批量更新测试
        response = input("是否进行批量更新测试？这将实际更新数据库中的数据 (y/N): ")
        if response.lower() in ['y', 'yes']:
            test_batch_update()
        else:
            logger.info("⏭️ 跳过批量更新测试")
        
        logger.info("🎉 所有测试完成！")
        
    except Exception as e:
        logger.error(f"❌ 测试过程中出错: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
