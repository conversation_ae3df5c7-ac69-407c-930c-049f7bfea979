#!/usr/bin/env python3
"""
爬虫功能测试脚本
测试完整的爬虫采集和入库流程
"""

import asyncio
import logging
import sys
from sqlalchemy.orm import Session

from app.core.database import SessionLocal, test_database_connection
from app.services.toolify_service import ToolifyAPIService
from app.services.database_service import DatabaseService
from app.utils.data_analyzer import analyze_toolify_data

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_database_connection_func():
    """测试数据库连接"""
    logger.info("=== 测试数据库连接 ===")
    
    is_connected, message = test_database_connection()
    if is_connected:
        logger.info(f"✅ {message}")
        return True
    else:
        logger.error(f"❌ {message}")
        return False

def test_toolify_api():
    """测试Toolify API连接"""
    logger.info("=== 测试Toolify API连接 ===")
    
    try:
        service = ToolifyAPIService()
        result = service.fetch_tools(page=1, per_page=5, tool_type=1)
        
        if result['success']:
            data = result['data']
            tools_list = data['data']['data']  # 正确的数据路径
            tools_count = len(tools_list)
            logger.info(f"✅ API连接成功，获取到 {tools_count} 条工具数据")

            # 显示第一个工具的信息
            if tools_count > 0:
                first_tool = tools_list[0]
                logger.info(f"示例工具: {first_tool.get('name', 'Unknown')} - {first_tool.get('website', 'No URL')}")
            
            return True, data
        else:
            logger.error(f"❌ API连接失败: {result['message']}")
            return False, None
            
    except Exception as e:
        logger.error(f"❌ API测试出错: {str(e)}")
        return False, None

def test_toolify_api_simple():
    """简单的API测试，只返回布尔值"""
    result = test_toolify_api()
    return result[0] if result else False

def test_data_transformation():
    """测试数据转换功能"""
    logger.info("=== 测试数据转换功能 ===")
    
    try:
        service = ToolifyAPIService()
        result = service.fetch_tools(page=1, per_page=3, tool_type=1)
        
        if not result['success']:
            logger.error("❌ 无法获取测试数据")
            return False
        
        tools = result['data']['data']['data']  # 正确的数据路径
        if not tools:
            logger.error("❌ 没有获取到工具数据")
            return False
        
        # 测试转换第一个工具
        first_tool = tools[0]
        transformed = service.transform_tool_data(first_tool)
        
        logger.info("✅ 数据转换成功")
        logger.info(f"原始工具名: {first_tool.get('name', 'Unknown')}")
        logger.info(f"转换后tool_id: {transformed.get('tool_id', 'Unknown')}")
        logger.info(f"转换后评分: {transformed.get('rating', 0)}")
        logger.info(f"分类数量: {len(transformed.get('categories', []))}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据转换测试出错: {str(e)}")
        return False

def test_database_operations():
    """测试数据库操作"""
    logger.info("=== 测试数据库操作 ===")
    
    try:
        # 创建数据库会话
        db = SessionLocal()
        
        try:
            db_service = DatabaseService(db)
            
            # 获取当前统计信息
            initial_tools_count = db_service.get_tools_count()
            initial_categories_count = db_service.get_categories_count()
            
            logger.info(f"数据库当前状态:")
            logger.info(f"  - 工具数量: {initial_tools_count}")
            logger.info(f"  - 分类数量: {initial_categories_count}")
            
            # 获取测试数据
            service = ToolifyAPIService()
            result = service.fetch_tools(page=1, per_page=2, tool_type=1)
            
            if not result['success']:
                logger.error("❌ 无法获取测试数据")
                return False
            
            tools = result['data']['data']['data']  # 正确的数据路径
            if not tools:
                logger.error("❌ 没有获取到工具数据")
                return False
            
            # 转换数据
            transformed_tools = []
            for tool in tools:
                transformed = service.transform_tool_data(tool)
                transformed_tools.append(transformed)
            
            # 保存到数据库
            logger.info(f"尝试保存 {len(transformed_tools)} 条工具数据...")
            saved_count, errors = db_service.save_tools_to_database(transformed_tools)
            
            # 获取最终统计信息
            final_tools_count = db_service.get_tools_count()
            final_categories_count = db_service.get_categories_count()
            
            logger.info(f"✅ 数据库操作完成:")
            logger.info(f"  - 成功保存: {saved_count} 条")
            logger.info(f"  - 错误数量: {len(errors)}")
            logger.info(f"  - 最终工具数量: {final_tools_count}")
            logger.info(f"  - 最终分类数量: {final_categories_count}")
            
            if errors:
                logger.warning("保存过程中的错误:")
                for error in errors[:3]:  # 只显示前3个错误
                    logger.warning(f"  - {error}")
            
            return True
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"❌ 数据库操作测试出错: {str(e)}")
        return False

def test_data_analysis():
    """测试数据分析功能"""
    logger.info("=== 测试数据分析功能 ===")
    
    try:
        service = ToolifyAPIService()
        result = service.fetch_tools(page=1, per_page=10, tool_type=1)
        
        if not result['success']:
            logger.error("❌ 无法获取测试数据")
            return False
        
        # 分析数据
        analysis = analyze_toolify_data(result['data'])
        
        if 'error' in analysis:
            logger.error(f"❌ 数据分析失败: {analysis['error']}")
            return False
        
        logger.info("✅ 数据分析成功:")
        
        stats = analysis.get('statistics', {})
        logger.info(f"  - 总工具数: {stats.get('total_tools', 0)}")
        logger.info(f"  - 付费工具数: {stats.get('premium_tools', 0)}")
        logger.info(f"  - 新工具数: {stats.get('new_tools', 0)}")
        logger.info(f"  - 特色工具数: {stats.get('featured_tools', 0)}")
        logger.info(f"  - 分类数: {stats.get('total_categories', 0)}")
        logger.info(f"  - 平均评分: {stats.get('average_rating', 0)}")
        
        categories = analysis.get('categories', {})
        logger.info(f"  - 发现的分类: {list(categories.keys())[:5]}...")  # 只显示前5个
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据分析测试出错: {str(e)}")
        return False

def test_batch_crawl():
    """测试批量爬取功能"""
    logger.info("=== 测试批量爬取功能 ===")
    
    try:
        service = ToolifyAPIService()
        
        # 批量获取2页数据
        logger.info("开始批量爬取2页数据...")
        tools = service.fetch_all_pages(
            start_page=1,
            max_pages=2,
            per_page=10,
            tool_type=1
        )
        
        logger.info(f"✅ 批量爬取成功，获取到 {len(tools)} 条工具数据")
        
        if tools:
            # 显示一些统计信息
            premium_count = sum(1 for tool in tools if tool.get('is_ad', False))
            new_count = sum(1 for tool in tools if tool.get('is_noticeable', 0) == 1)
            
            logger.info(f"  - 付费工具: {premium_count}")
            logger.info(f"  - 新工具: {new_count}")
            
            # 显示前3个工具名称
            logger.info("前3个工具:")
            for i, tool in enumerate(tools[:3]):
                logger.info(f"  {i+1}. {tool.get('name', 'Unknown')}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 批量爬取测试出错: {str(e)}")
        return False

def run_all_tests():
    """运行所有测试"""
    logger.info("🚀 开始运行爬虫功能完整测试")
    logger.info("=" * 50)
    
    tests = [
        ("数据库连接", test_database_connection_func),
        ("Toolify API连接", lambda: test_toolify_api_simple()),
        ("数据转换", test_data_transformation),
        ("数据分析", test_data_analysis),
        ("批量爬取", test_batch_crawl),
        ("数据库操作", test_database_operations),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                logger.info(f"✅ {test_name} 测试通过")
            else:
                logger.error(f"❌ {test_name} 测试失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 测试异常: {str(e)}")
            results.append((test_name, False))
    
    # 总结
    logger.info("\n" + "=" * 50)
    logger.info("🏁 测试结果总结:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        logger.info("🎉 所有测试都通过了！爬虫功能完全正常！")
        return True
    else:
        logger.warning(f"⚠️  有 {len(results) - passed} 个测试失败，请检查相关功能")
        return False

def main():
    """主函数"""
    if len(sys.argv) > 1:
        test_name = sys.argv[1].lower()
        
        if test_name == "db":
            test_database_connection_func()
        elif test_name == "api":
            test_toolify_api()
        elif test_name == "transform":
            test_data_transformation()
        elif test_name == "analysis":
            test_data_analysis()
        elif test_name == "batch":
            test_batch_crawl()
        elif test_name == "database":
            test_database_operations()
        else:
            print("可用的测试:")
            print("  db        - 测试数据库连接")
            print("  api       - 测试API连接")
            print("  transform - 测试数据转换")
            print("  analysis  - 测试数据分析")
            print("  batch     - 测试批量爬取")
            print("  database  - 测试数据库操作")
            print("  (无参数)   - 运行所有测试")
    else:
        run_all_tests()

if __name__ == "__main__":
    main()
