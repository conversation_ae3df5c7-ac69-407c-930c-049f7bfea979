#!/usr/bin/env python3
"""
调试内容提取脚本

展示爬虫如何从网站提取内容，以及AI如何分析这些内容
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.services.content_extractor_service import ContentExtractorService


def show_extracted_content(url: str):
    """展示从网站提取的原始内容"""
    print(f"=== 正在提取网站内容: {url} ===")
    
    # 创建内容提取服务
    extractor = ContentExtractorService()
    
    try:
        # 提取网站内容
        result = extractor.extract_website_content(
            url=url,
            max_chunks=3,
            chunk_size=2000
        )
        
        if result["chunks"]:
            print(f"✅ 成功提取了 {len(result['chunks'])} 个文本块")
            print(f"总字符数: {sum(len(chunk) for chunk in result['chunks'])}")
            print("\n" + "="*80)
            
            for i, chunk in enumerate(result["chunks"], 1):
                print(f"\n--- 文本块 {i} ---")
                print(f"字符数: {len(chunk)}")
                print(f"内容预览:")
                print("-" * 40)
                # 显示前500个字符
                preview = chunk[:500] + "..." if len(chunk) > 500 else chunk
                print(preview)
                print("-" * 40)
                
                if i < len(result["chunks"]):
                    input("按回车键查看下一个文本块...")
        else:
            print("❌ 无法提取网站内容")
            
    except Exception as e:
        print(f"❌ 提取失败: {str(e)}")


def main():
    """主函数"""
    print("网站内容提取调试工具")
    print("=" * 50)
    
    # 测试 gumloop.com 的定价页面
    test_urls = [
        "https://gumloop.com/pricing",
        "https://gumloop.com",
        "https://openai.com/pricing",
        "https://github.com/pricing"
    ]
    
    for url in test_urls:
        print(f"\n{'='*80}")
        show_extracted_content(url)
        
        user_input = input(f"\n是否继续测试下一个URL？(y/N): ")
        if user_input.lower() != 'y':
            break
    
    print("\n调试完成！")


if __name__ == '__main__':
    main()
