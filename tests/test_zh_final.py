#!/usr/bin/env python3
"""
最终测试脚本 - 验证中文翻译功能
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import logging
import time
from app.core.database import SessionLocal
from app.models.tool import Tool, ToolTranslation
from app.services.ollama_service import OllamaService

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_and_create_translation():
    """测试并创建一个中文翻译"""
    db = SessionLocal()
    
    try:
        print("=== 开始测试中文翻译功能 ===")
        
        # 1. 查找一个需要翻译的英文工具
        chinese_tool_ids = [row[0] for row in db.query(ToolTranslation.tool_id).filter(
            ToolTranslation.locale == 'zh'
        ).all()]
        
        english_translation = db.query(ToolTranslation).filter(
            ToolTranslation.locale == 'en',
            ToolTranslation.description.isnot(None),
            ToolTranslation.description != ''
        )
        
        if chinese_tool_ids:
            english_translation = english_translation.filter(
                ToolTranslation.tool_id.notin_(chinese_tool_ids)
            )
        
        english_translation = english_translation.first()
        
        if not english_translation:
            print("❌ 没有找到需要翻译的英文工具")
            return False
        
        print(f"✅ 找到需要翻译的工具: {english_translation.tool_id}")
        print(f"   英文名称: {english_translation.name}")
        print(f"   英文描述: {english_translation.description[:100]}...")
        
        # 2. 初始化Ollama服务
        print("🔄 初始化Ollama服务...")
        ollama_service = OllamaService()
        
        if not ollama_service.is_available():
            print("❌ Ollama服务不可用")
            return False
        
        print("✅ Ollama服务可用")
        
        # 3. 翻译名称
        print("🔄 翻译工具名称...")
        name_prompt = f"""请将以下工具名称翻译成中文，保持简洁：

{english_translation.name}

要求：
1. 翻译要准确、自然、符合中文表达习惯
2. 只返回翻译结果，不要添加其他说明

翻译结果："""
        
        name_response = ollama_service.call_ollama(
            prompt=name_prompt,
            model='gemma3:latest',
            temperature=0.3,
            max_tokens=512,
            timeout=60
        )
        
        if isinstance(name_response, dict) and 'error' in name_response:
            print(f"❌ 翻译名称失败: {name_response['error']}")
            return False
        
        if not isinstance(name_response, str) or not name_response.strip():
            print("❌ 翻译名称返回空结果")
            return False
        
        chinese_name = name_response.strip()
        # 清理翻译结果
        prefixes_to_remove = ['翻译结果：', '翻译结果:', '中文翻译：', '中文翻译:', '翻译：', '翻译:']
        for prefix in prefixes_to_remove:
            if chinese_name.startswith(prefix):
                chinese_name = chinese_name[len(prefix):].strip()
                break
        
        print(f"✅ 名称翻译成功: {english_translation.name} -> {chinese_name}")
        
        # 4. 翻译描述
        time.sleep(2)  # 防止API限速
        print("🔄 翻译工具描述...")
        desc_prompt = f"""请将以下工具简介翻译成中文，保持简洁明了：

{english_translation.description}

要求：
1. 翻译要准确、自然、符合中文表达习惯
2. 保持原文的专业性和技术准确性
3. 如果有专业术语，请使用通用的中文翻译
4. 只返回翻译结果，不要添加其他说明

翻译结果："""
        
        desc_response = ollama_service.call_ollama(
            prompt=desc_prompt,
            model='gemma3:latest',
            temperature=0.3,
            max_tokens=1024,
            timeout=60
        )
        
        if isinstance(desc_response, dict) and 'error' in desc_response:
            print(f"❌ 翻译描述失败: {desc_response['error']}")
            return False
        
        if not isinstance(desc_response, str) or not desc_response.strip():
            print("❌ 翻译描述返回空结果")
            return False
        
        chinese_description = desc_response.strip()
        # 清理翻译结果
        for prefix in prefixes_to_remove:
            if chinese_description.startswith(prefix):
                chinese_description = chinese_description[len(prefix):].strip()
                break
        
        print(f"✅ 描述翻译成功: {chinese_description[:100]}...")
        
        # 5. 翻译长描述（如果存在）
        chinese_long_description = None
        if english_translation.long_description and english_translation.long_description.strip():
            time.sleep(2)
            print("🔄 翻译长描述...")
            long_desc_prompt = f"""请将以下工具详细描述翻译成中文，保持专业性和可读性：

{english_translation.long_description}

要求：
1. 翻译要准确、自然、符合中文表达习惯
2. 保持原文的专业性和技术准确性
3. 保持原文的格式和结构
4. 只返回翻译结果，不要添加其他说明

翻译结果："""
            
            long_desc_response = ollama_service.call_ollama(
                prompt=long_desc_prompt,
                model='gemma3:latest',
                temperature=0.3,
                max_tokens=2048,
                timeout=120
            )
            
            if isinstance(long_desc_response, str) and long_desc_response.strip():
                chinese_long_description = long_desc_response.strip()
                for prefix in prefixes_to_remove:
                    if chinese_long_description.startswith(prefix):
                        chinese_long_description = chinese_long_description[len(prefix):].strip()
                        break
                print(f"✅ 长描述翻译成功: {chinese_long_description[:100]}...")
            else:
                print("⚠️  长描述翻译失败，但继续处理")
        
        # 6. 创建中文翻译记录
        print("🔄 创建中文翻译记录...")
        chinese_translation = ToolTranslation(
            tool_id=english_translation.tool_id,
            locale='zh',
            name=chinese_name,
            description=chinese_description,
            long_description=chinese_long_description,
            usage_instructions=None,  # 暂时不翻译这些可选字段
            pricing_details=None,
            integration_info=None
        )
        
        # 7. 保存到数据库
        db.add(chinese_translation)
        db.flush()  # 检查约束
        db.commit()
        
        print(f"🎉 成功为工具 {english_translation.tool_id} 创建中文翻译！")
        print(f"   中文名称: {chinese_name}")
        print(f"   中文描述: {chinese_description}")
        
        return True
        
    except Exception as e:
        db.rollback()
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()

if __name__ == '__main__':
    success = test_and_create_translation()
    if success:
        print("\n🎉 测试成功！中文翻译功能正常工作！")
    else:
        print("\n❌ 测试失败！")
    
    # 验证结果
    print("\n=== 验证翻译结果 ===")
    db = SessionLocal()
    try:
        chinese_count = db.query(ToolTranslation).filter(ToolTranslation.locale == 'zh').count()
        print(f"当前中文翻译总数: {chinese_count}")
    finally:
        db.close()