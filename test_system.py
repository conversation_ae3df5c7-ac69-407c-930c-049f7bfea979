#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
流量数据系统测试脚本
用于验证系统各个组件的功能
"""

import asyncio
import sys
import traceback
from datetime import datetime
from traffic_data_manager import TrafficDataManager, DatabaseConfig
from traffic_cv_spider import TrafficC<PERSON>pider


def test_data_conversion():
    """测试数据转换功能"""
    print("🧪 测试数据转换功能")
    print("-" * 40)
    
    manager = TrafficDataManager(DatabaseConfig())
    
    # 测试访问量转换
    test_visits = ["15.41M", "1.2K", "500", "2.5B", "invalid"]
    print("访问量转换测试:")
    for visit in test_visits:
        result = manager.convert_visits_to_number(visit)
        print(f"  {visit:>8} -> {result:>12,}")
    
    # 测试时长转换
    test_durations = ["00:06:56", "01:23:45", "00:00:30", "invalid"]
    print("\n时长转换测试:")
    for duration in test_durations:
        result = manager.convert_duration_to_seconds(duration)
        print(f"  {duration:>8} -> {result:>6} 秒")
    
    # 测试百分比转换
    test_percentages = ["34.31%", "-5.20%", "100%", "0%", "invalid"]
    print("\n百分比转换测试:")
    for percent in test_percentages:
        result = manager.extract_percentage(percent)
        print(f"  {percent:>8} -> {result}")
    
    # 测试日期转换
    test_dates = ["2024-03-26", "2024/03/26", "03/26/2024", "invalid"]
    print("\n日期转换测试:")
    for date_str in test_dates:
        result = manager.parse_date(date_str)
        print(f"  {date_str:>12} -> {result}")
    
    print("✅ 数据转换测试完成\n")


def test_spider():
    """测试爬虫功能"""
    print("🕷️ 测试爬虫功能")
    print("-" * 40)
    
    spider = TrafficCVSpider()
    
    try:
        # 测试获取数据
        result = spider.get_domain_data("klingai.com")
        
        if result.get('success', False):
            print("✅ 爬虫数据获取成功")
            
            data = result.get('data', {})
            
            # 检查各个数据部分
            sections = [
                ('basic_info', '基本信息'),
                ('traffic_metrics', '流量指标'),
                ('domain_info', '域名信息'),
                ('traffic_sources', '流量来源'),
                ('visits_over_time', '访问量趋势'),
                ('top_regions', '热门地区'),
                ('top_keywords', '热门关键词'),
                ('whois_info', 'WHOIS信息')
            ]
            
            for key, name in sections:
                section_data = data.get(key, {})
                if isinstance(section_data, list):
                    count = len(section_data)
                    status = "✅" if count > 0 else "⚠️"
                    print(f"  {status} {name}: {count} 项")
                elif isinstance(section_data, dict):
                    count = len(section_data)
                    status = "✅" if count > 0 else "⚠️"
                    print(f"  {status} {name}: {count} 字段")
                else:
                    print(f"  ⚠️ {name}: 无数据")
        else:
            print("❌ 爬虫数据获取失败")
            print(f"   错误: {result.get('error', 'Unknown error')}")
    
    except Exception as e:
        print(f"❌ 爬虫测试异常: {e}")
        traceback.print_exc()
    
    print("✅ 爬虫测试完成\n")


async def test_database_connection():
    """测试数据库连接"""
    print("🗄️ 测试数据库连接")
    print("-" * 40)
    
    # 使用默认配置
    db_config = DatabaseConfig()
    manager = TrafficDataManager(db_config)
    
    try:
        # 尝试初始化连接池
        await manager.init_db_pool()
        print("✅ 数据库连接成功")
        
        # 测试简单查询
        async with manager.db_pool.acquire() as conn:
            result = await conn.fetchval("SELECT 1")
            if result == 1:
                print("✅ 数据库查询测试成功")
            else:
                print("❌ 数据库查询测试失败")
        
        # 检查表是否存在
        async with manager.db_pool.acquire() as conn:
            tables = await conn.fetch("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name LIKE 'traffic_%'
                ORDER BY table_name
            """)
            
            expected_tables = [
                'traffic_site_overview',
                'traffic_monthly_trends', 
                'traffic_source_analysis',
                'traffic_region_distribution',
                'traffic_keyword_analysis',
                'traffic_domain_whois',
                'traffic_extraction_logs'
            ]
            
            existing_tables = [row['table_name'] for row in tables]
            
            print(f"数据库表检查:")
            for table in expected_tables:
                status = "✅" if table in existing_tables else "❌"
                print(f"  {status} {table}")
            
            missing_tables = set(expected_tables) - set(existing_tables)
            if missing_tables:
                print(f"\n⚠️ 缺少表: {', '.join(missing_tables)}")
                print("请运行: psql -d aistak_db -f create_traffic_tables.sql")
    
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        print("请检查数据库配置和服务状态")
    
    finally:
        await manager.close_db_pool()
    
    print("✅ 数据库测试完成\n")


async def test_full_process():
    """测试完整流程"""
    print("🔄 测试完整数据处理流程")
    print("-" * 40)
    
    db_config = DatabaseConfig()
    manager = TrafficDataManager(db_config)
    
    try:
        await manager.init_db_pool()
        
        # 测试处理单个域名
        test_domain = "klingai.com"
        print(f"处理测试域名: {test_domain}")
        
        result = await manager.process_domain_traffic(test_domain)
        
        if result.get('success', False):
            print("✅ 完整流程处理成功")
            print(f"   Tool ID: {result.get('tool_id')}")
            print(f"   数据点数: {result.get('data_points')}")
            print(f"   响应时间: {result.get('response_time_ms')}ms")
            
            # 测试数据查询
            tool_id = result.get('tool_id')
            summary = await manager.get_traffic_summary(tool_id)
            
            if summary.get('overview'):
                print("✅ 数据查询成功")
                overview = summary['overview']
                print(f"   域名: {overview.get('domain')}")
                print(f"   排名: #{overview.get('global_rank')}")
                print(f"   访问量: {overview.get('total_visits_raw')}")
            else:
                print("⚠️ 数据查询无结果")
        else:
            print("❌ 完整流程处理失败")
            print(f"   错误: {result.get('error')}")
    
    except Exception as e:
        print(f"❌ 完整流程测试异常: {e}")
        traceback.print_exc()
    
    finally:
        await manager.close_db_pool()
    
    print("✅ 完整流程测试完成\n")


async def main():
    """主测试函数"""
    print("🚀 流量数据系统测试")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    try:
        # 1. 测试数据转换功能
        test_data_conversion()
        
        # 2. 测试爬虫功能
        test_spider()
        
        # 3. 测试数据库连接
        await test_database_connection()
        
        # 4. 测试完整流程（需要数据库）
        print("是否测试完整流程？这将向数据库写入数据。(y/N): ", end="")
        try:
            choice = input().strip().lower()
            if choice in ['y', 'yes']:
                await test_full_process()
            else:
                print("⏭️ 跳过完整流程测试\n")
        except (EOFError, KeyboardInterrupt):
            print("⏭️ 跳过完整流程测试\n")
        
        print("🎉 所有测试完成!")
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {e}")
        traceback.print_exc()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 测试结束")
    except Exception as e:
        print(f"\n💥 程序异常退出: {e}")
        sys.exit(1)
