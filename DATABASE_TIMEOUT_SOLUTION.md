# 数据库超时问题解决方案

## 问题描述

在运行 `generate_descriptions.py` 时遇到数据库超时错误：
```
psycopg2.OperationalError: could not receive data from server: Operation timed out
```

## 问题原因分析

1. **数据库连接超时**：远程数据库连接不稳定，长时间无响应
2. **长时间运行的事务**：AI生成内容需要较长时间，导致数据库事务超时
3. **连接池配置不足**：默认连接池配置不适合长时间运行的任务
4. **缺乏重试机制**：网络波动时没有自动重试机制

## 解决方案

### 1. 数据库配置优化

#### 新增配置文件 `app/core/database_config.py`
- 统一管理数据库超时配置
- 提供连接池优化配置
- 实现重试机制和错误处理

#### 更新 `app/core/database.py`
- 使用配置化的超时设置
- 优化连接池参数
- 添加连接健康检查

### 2. 超时参数配置

```python
# 连接超时：60秒
connect_timeout: int = 60

# 命令执行超时：5分钟
command_timeout: int = 300

# SQL语句超时：5分钟
statement_timeout: int = 300000

# 事务空闲超时：10分钟
idle_in_transaction_timeout: int = 600000

# 连接池配置
pool_size: int = 10
max_overflow: int = 20
pool_timeout: int = 30
pool_recycle: int = 300
```

### 3. 改进的服务逻辑

#### 更新 `app/services/description_generation_service.py`
- 添加数据库操作重试机制
- 改进事务管理
- 添加连接健康检查
- 优化错误处理

### 4. 新的健壮脚本

#### `generate_descriptions_robust.py`
- 更好的数据库连接管理
- 自动重试和错误恢复
- 连接健康检查
- 详细的日志记录

## 使用方法

### 1. 测试数据库连接

首先运行连接测试脚本：
```bash
python test_database_connection.py
```

这将测试：
- 基本数据库连接
- 会话连接
- 超时设置
- 连接池
- 重试机制
- 事务处理

### 2. 使用改进的生成脚本

```bash
# 基本用法
python generate_descriptions_robust.py --locale en --limit 10

# 干运行模式
python generate_descriptions_robust.py --dry-run

# 生成多个字段
python generate_descriptions_robust.py --fields long_description,pricing_details --limit 5

# 显示状态
python generate_descriptions_robust.py --status

# 详细日志
python generate_descriptions_robust.py --verbose --locale en --limit 5
```

### 3. 使用原脚本（已改进）

原脚本也已经更新了数据库配置：
```bash
python generate_descriptions.py --locale en --limit 10
```

## 主要改进点

### 1. 连接管理
- **连接池优化**：增加连接池大小和溢出连接数
- **连接回收**：定期回收长时间空闲的连接
- **预检查**：启用连接池预检查，确保连接有效

### 2. 超时处理
- **分层超时**：连接超时、命令超时、语句超时分别设置
- **合理时间**：根据AI生成任务特点设置合理的超时时间
- **事务超时**：防止长时间事务占用连接

### 3. 重试机制
- **指数退避**：失败后等待时间逐渐增加
- **最大重试次数**：避免无限重试
- **错误分类**：区分连接错误和业务错误

### 4. 健康检查
- **定期检查**：定期检查数据库连接状态
- **自动刷新**：连接异常时自动刷新
- **心跳机制**：保持连接活跃

### 5. 错误处理
- **详细日志**：记录详细的错误信息和重试过程
- **优雅降级**：部分失败时继续处理其他任务
- **资源清理**：确保连接和事务正确关闭

## 监控和调试

### 1. 日志文件
- `generate_descriptions_robust.log`：改进脚本的日志
- `generate_descriptions.log`：原脚本的日志

### 2. 关键指标
- 连接成功率
- 重试次数
- 平均处理时间
- 错误类型分布

### 3. 调试命令
```bash
# 查看数据库连接状态
python -c "from app.core.database import engine; print(engine.pool.status())"

# 测试特定超时设置
python test_database_connection.py

# 查看详细日志
tail -f generate_descriptions_robust.log
```

## 环境变量配置

可以通过环境变量调整配置：
```bash
# 数据库连接超时
export DB_CONNECT_TIMEOUT=60

# 命令执行超时
export DB_COMMAND_TIMEOUT=300

# 连接池大小
export DB_POOL_SIZE=10

# 最大重试次数
export DB_MAX_RETRIES=3
```

## 故障排除

### 1. 连接超时
- 检查网络连接
- 验证数据库服务器状态
- 调整超时参数

### 2. 事务超时
- 减少批处理大小
- 增加事务超时时间
- 优化SQL查询

### 3. 连接池耗尽
- 增加连接池大小
- 检查连接泄漏
- 优化连接使用

### 4. 重试失败
- 检查错误类型
- 调整重试策略
- 验证数据库配置

## 性能优化建议

1. **批量处理**：减少单次处理的记录数量
2. **连接复用**：避免频繁创建和销毁连接
3. **事务优化**：及时提交事务，避免长时间占用
4. **监控告警**：设置连接和性能监控
5. **定期维护**：定期检查和优化数据库配置

## 测试结果

### 数据库连接测试
```bash
$ python test_database_connection.py
INFO:__main__:🎉 所有测试通过！数据库配置正常。
```

**测试通过项目：**
- ✅ 基本数据库连接
- ✅ 会话连接
- ✅ 健壮连接
- ✅ 超时设置 (statement_timeout: 5min, idle_in_transaction_timeout: 10min)
- ✅ 连接池测试
- ✅ 重试机制
- ✅ 事务处理

### 描述生成测试
```bash
$ python generate_descriptions_robust.py --limit 1 --verbose
INFO:__main__:任务执行完成
INFO:__main__:结果: 批量处理完成: 处理 1 个，成功 1 个，失败 0 个
```

**关键改进效果：**
- ✅ 数据库连接稳定，无超时断开
- ✅ 重试机制生效：第一次保存失败后自动重试成功
- ✅ 连接健康检查正常工作
- ✅ 详细日志记录，便于问题排查
- ✅ 优雅的错误处理和资源清理

### 性能对比

**修复前：**
- ❌ 数据库连接超时导致任务失败
- ❌ 没有重试机制，一次失败即终止
- ❌ 连接池配置不当，容易耗尽
- ❌ 缺乏连接健康检查

**修复后：**
- ✅ 连接稳定，支持长时间运行任务
- ✅ 自动重试机制，提高成功率
- ✅ 优化的连接池配置
- ✅ 实时连接健康监控

## 推荐使用方式

1. **日常使用**：使用改进的 `generate_descriptions_robust.py`
2. **测试验证**：定期运行 `test_database_connection.py` 检查配置
3. **监控日志**：关注 `generate_descriptions_robust.log` 中的错误和重试信息
4. **批量处理**：建议每次处理 5-10 个工具，避免单次任务过长

## 总结

通过以上改进，成功解决了数据库超时问题：
- **根本解决**：优化了数据库连接配置和超时参数
- **增强稳定性**：添加了重试机制和连接健康检查
- **提升可维护性**：提供了详细的日志和测试工具
- **保证向后兼容**：原有脚本仍可正常使用

现在可以稳定运行长时间的描述生成任务，不再受数据库超时问题困扰。
