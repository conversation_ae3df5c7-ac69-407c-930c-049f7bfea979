#!/usr/bin/env python3
"""
中文工具描述生成独立脚本

这个脚本专门用于生成中文工具描述，逻辑是：
1. 查找数据库中存在英文描述(locale='en')但没有对应中文描述(locale='zh')的工具
2. 将英文的 description 和 long_description 翻译成中文并插入到数据库中
3. name 字段保持英文原文不翻译
4. 确保数据唯一性：tool_id和locale字段组合唯一

使用方法:
    python generate_descriptions_zh.py --help
    python generate_descriptions_zh.py --limit 10
    python generate_descriptions_zh.py --dry-run
    python generate_descriptions_zh.py --status
"""

import os
import sys
import argparse
import logging
import time
from pathlib import Path
from typing import Dict, Any, List, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.models.tool import Tool, ToolTranslation
from app.services.ollama_service import OllamaService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('generate_descriptions_zh.log')
    ]
)
logger = logging.getLogger(__name__)


class ChineseDescriptionGenerator:
    """中文描述生成器"""
    
    def __init__(self, db: Session):
        """
        初始化中文描述生成器
        
        Args:
            db: 数据库会话
        """
        self.db = db
        self.ollama_service = None
    
    def initialize_ollama_service(self, ip_address: Optional[str] = None, port: int = 11434) -> bool:
        """
        初始化Ollama服务
        
        Args:
            ip_address: Ollama服务器IP地址
            port: Ollama服务器端口
            
        Returns:
            bool: 是否初始化成功
        """
        try:
            self.ollama_service = OllamaService(ip_address, port)
            logger.info(f"Ollama服务初始化成功: {self.ollama_service.ip_address}:{self.ollama_service.port}")
            return True
        except Exception as e:
            logger.error(f"Ollama服务初始化失败: {str(e)}")
            return False
    
    def find_tools_needing_chinese_translation(self, limit: int = 10) -> List[ToolTranslation]:
        """
        查找需要中文翻译的工具
        
        逻辑：找到有英文翻译但没有中文翻译的工具
        
        Args:
            limit: 返回结果数量限制
            
        Returns:
            List[ToolTranslation]: 需要翻译的英文工具翻译列表
        """
        try:
            # 获取已有中文翻译的工具ID列表
            chinese_tool_ids = [row[0] for row in self.db.query(ToolTranslation.tool_id).filter(
                ToolTranslation.locale == 'zh'
            ).all()]
            
            logger.debug(f"已有中文翻译的工具数量: {len(chinese_tool_ids)}")
            
            # 查询有英文翻译但没有中文翻译的工具
            query = self.db.query(ToolTranslation).filter(
                ToolTranslation.locale == 'en',
                ToolTranslation.description.isnot(None),
                ToolTranslation.description != ''
            )
            
            # 如果有中文翻译，排除这些工具
            if chinese_tool_ids:
                query = query.filter(ToolTranslation.tool_id.notin_(chinese_tool_ids))
            
            # 按创建时间排序
            query = query.order_by(ToolTranslation.created_at)
            
            # 应用限制
            if limit > 0:
                query = query.limit(limit)
            
            results = query.all()
            logger.info(f"找到 {len(results)} 个需要中文翻译的工具")
            
            # 调试信息
            if results:
                logger.debug("需要翻译的工具:")
                for result in results[:3]:  # 只显示前3个
                    logger.debug(f"  - {result.tool_id}: {result.name}")
            
            return results
            
        except Exception as e:
            logger.error(f"查询需要翻译的工具时出错: {str(e)}")
            logger.exception("详细错误信息:")
            return []
    
    def translate_to_chinese(self, english_text: str, field_name: str, model_name: str = 'gemma3:latest') -> Optional[str]:
        """
        将英文文本翻译成中文
        
        Args:
            english_text: 英文文本
            field_name: 字段名称（用于生成合适的提示词）
            model_name: 使用的模型名称
            
        Returns:
            Optional[str]: 翻译后的中文文本
        """
        if not self.ollama_service:
            logger.error("Ollama服务未初始化")
            return None
        
        # 检查输入文本
        if not english_text or not english_text.strip():
            logger.warning(f"输入文本为空，跳过翻译 {field_name}")
            return None
        
        try:
            # 根据字段类型生成不同的提示词
            field_prompts = {
                'description': '请将以下工具简介翻译成中文，保持简洁明了：',
                'long_description': '请将以下工具详细描述翻译成中文，保持专业性和可读性：',
            }
            
            prompt_prefix = field_prompts.get(field_name, '请将以下内容翻译成中文：')
            
            prompt = f"""{prompt_prefix}

{english_text.strip()}

要求：
1. 翻译要准确、自然、符合中文表达习惯
2. 保持原文的专业性和技术准确性
3. 如果有专业术语，请使用通用的中文翻译
4. 保持原文的格式和结构
5. 只返回翻译结果，不要添加其他说明

翻译结果："""

            logger.debug(f"开始翻译 {field_name}，使用模型: {model_name}")
            
            # 使用正确的方法名 call_ollama
            response = self.ollama_service.call_ollama(
                prompt=prompt,
                model=model_name,
                temperature=0.3,  # 较低的温度以确保翻译准确性
                max_tokens=2048,
                timeout=120  # 2分钟超时
            )
            
            # 检查响应类型
            if isinstance(response, dict) and 'error' in response:
                logger.error(f"翻译 {field_name} 失败: {response['error']}")
                return None
            
            if isinstance(response, str) and response.strip():
                # 清理翻译结果
                chinese_text = response.strip()
                
                # 移除可能的前缀说明
                prefixes_to_remove = ['翻译结果：', '翻译结果:', '中文翻译：', '中文翻译:', '翻译：', '翻译:']
                for prefix in prefixes_to_remove:
                    if chinese_text.startswith(prefix):
                        chinese_text = chinese_text[len(prefix):].strip()
                        break
                
                # 验证翻译结果不为空且不是原文
                if chinese_text and chinese_text != english_text.strip():
                    logger.info(f"成功翻译 {field_name}: {english_text[:50]}... -> {chinese_text[:50]}...")
                    return chinese_text
                else:
                    logger.warning(f"翻译结果无效或与原文相同: {field_name}")
                    return None
            else:
                logger.error(f"翻译 {field_name} 失败：模型返回空结果或格式错误")
                return None
                
        except Exception as e:
            logger.error(f"翻译 {field_name} 时发生错误: {str(e)}")
            return None
    
    def create_chinese_translation(self, english_translation: ToolTranslation, model_name: str = 'gemma3:latest') -> bool:
        """
        为英文翻译创建对应的中文翻译
        
        Args:
            english_translation: 英文翻译对象
            model_name: 使用的模型名称
            
        Returns:
            bool: 是否创建成功
        """
        try:
            logger.info(f"开始为工具 {english_translation.tool_id} 创建中文翻译")
            
            # 检查是否已存在中文翻译
            existing_chinese = self.db.query(ToolTranslation).filter(
                ToolTranslation.tool_id == english_translation.tool_id,
                ToolTranslation.locale == 'zh'
            ).first()
            
            if existing_chinese:
                logger.warning(f"工具 {english_translation.tool_id} 已存在中文翻译，跳过")
                return False
            
            # 验证英文翻译数据
            if not english_translation.name or not english_translation.name.strip():
                logger.error(f"英文工具名称为空: {english_translation.tool_id}")
                return False
            
            if not english_translation.description or not english_translation.description.strip():
                logger.error(f"英文工具描述为空: {english_translation.tool_id}")
                return False
            
            # 名称保持英文原文，不翻译
            chinese_name = english_translation.name
            logger.info(f"工具名称保持英文: {chinese_name}")
            
            logger.info(f"翻译工具描述: {english_translation.description[:100]}...")
            chinese_description = self.translate_to_chinese(english_translation.description, 'description', model_name)
            if not chinese_description:
                logger.error(f"翻译工具描述失败: {english_translation.tool_id}")
                return False
            
            # 翻译长描述（如果存在且不为空）
            chinese_long_description = None
            if english_translation.long_description and english_translation.long_description.strip():
                logger.info("翻译长描述...")
                time.sleep(2)  # 防止API限速
                chinese_long_description = self.translate_to_chinese(
                    english_translation.long_description, 'long_description', model_name
                )
                # 如果翻译失败，记录警告但不阻止整个流程
                if not chinese_long_description:
                    logger.warning(f"翻译长描述失败，但继续处理其他字段: {english_translation.tool_id}")
            
            # 其他字段保持为空，只翻译 description 和 long_description
            chinese_usage_instructions = None
            chinese_pricing_details = None
            chinese_integration_info = None
            
            # 创建中文翻译记录
            logger.info("创建中文翻译记录...")
            chinese_translation = ToolTranslation(
                tool_id=english_translation.tool_id,
                locale='zh',
                name=chinese_name,  # 保持英文
                description=chinese_description,
                long_description=chinese_long_description,
                usage_instructions=chinese_usage_instructions,
                pricing_details=chinese_pricing_details,
                integration_info=chinese_integration_info
            )
            
            # 保存到数据库
            self.db.add(chinese_translation)
            self.db.flush()  # 先flush检查是否有约束错误
            self.db.commit()
            
            logger.info(f"✅ 成功为工具 {english_translation.tool_id} 创建中文翻译")
            logger.info(f"   名称（保持英文）: {chinese_name}")
            logger.info(f"   中文描述: {chinese_description[:100]}...")
            return True
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"❌ 创建中文翻译失败 {english_translation.tool_id}: {str(e)}")
            logger.exception("详细错误信息:")
            return False
    
    def generate_chinese_descriptions(
        self, 
        limit: int = 10, 
        model_name: str = 'gemma3:latest',
        dry_run: bool = False
    ) -> Dict[str, Any]:
        """
        批量生成中文描述
        
        Args:
            limit: 处理数量限制
            model_name: 使用的模型名称
            dry_run: 是否为测试模式
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        if not self.ollama_service:
            return {
                "success": False,
                "error": "Ollama服务未初始化"
            }
        
        # 查找需要翻译的工具
        english_translations = self.find_tools_needing_chinese_translation(limit)
        
        if not english_translations:
            return {
                "success": True,
                "message": "没有找到需要中文翻译的工具",
                "processed": 0,
                "succeeded": 0,
                "failed": 0,
                "errors": []
            }
        
        if dry_run:
            return {
                "success": True,
                "message": f"DRY RUN模式：找到 {len(english_translations)} 个工具需要中文翻译",
                "tools": [{"tool_id": t.tool_id, "name": t.name} for t in english_translations],
                "processed": 0,
                "succeeded": 0,
                "failed": 0,
                "errors": []
            }
        
        # 处理每个工具
        processed = 0
        succeeded = 0
        failed = 0
        errors = []
        
        for english_translation in english_translations:
            try:
                processed += 1
                success = self.create_chinese_translation(english_translation, model_name)
                
                if success:
                    succeeded += 1
                else:
                    failed += 1
                    errors.append(f"翻译工具 {english_translation.tool_id} 失败")
                
                # 防止API限速
                time.sleep(3)
                
            except Exception as e:
                failed += 1
                error_msg = f"处理工具 {english_translation.tool_id} 时发生异常: {str(e)}"
                logger.error(error_msg)
                errors.append(error_msg)
        
        return {
            "success": True,
            "message": f"中文翻译完成: 处理 {processed} 个，成功 {succeeded} 个，失败 {failed} 个",
            "processed": processed,
            "succeeded": succeeded,
            "failed": failed,
            "errors": errors
        }
    
    def get_translation_status(self) -> Dict[str, Any]:
        """
        获取翻译状态统计
        
        Returns:
            Dict[str, Any]: 状态统计信息
        """
        try:
            # 统计英文翻译数量
            english_count = self.db.query(ToolTranslation).filter(
                ToolTranslation.locale == 'en'
            ).count()
            
            # 统计中文翻译数量
            chinese_count = self.db.query(ToolTranslation).filter(
                ToolTranslation.locale == 'zh'
            ).count()
            
            # 统计需要翻译的数量
            need_translation_count = len(self.find_tools_needing_chinese_translation(0))
            
            return {
                "success": True,
                "english_translations": english_count,
                "chinese_translations": chinese_count,
                "need_translation": need_translation_count,
                "translation_rate": round((chinese_count / english_count * 100), 2) if english_count > 0 else 0,
                "ollama_service_available": self.ollama_service.is_available() if self.ollama_service else False
            }
            
        except Exception as e:
            logger.error(f"获取翻译状态时出错: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='将英文工具描述翻译成中文（只翻译description和long_description字段）',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 基本用法 - 翻译10个工具
  python generate_descriptions_zh.py --limit 10
  
  # 干运行模式 - 仅显示将要处理的工具
  python generate_descriptions_zh.py --dry-run
  
  # 显示当前翻译状态
  python generate_descriptions_zh.py --status
  
  # 使用指定模型
  python generate_descriptions_zh.py --model deepseek-r1:latest --limit 5
        """
    )
    
    parser.add_argument(
        '--limit',
        type=int,
        default=10,
        help='每次处理的工具数量上限'
    )
    
    parser.add_argument(
        '--model',
        type=str,
        default='gemma3:latest',
        help='使用的Ollama模型名称，默认为gemma3:latest'
    )
    
    parser.add_argument(
        '--ip-address',
        type=str,
        help='Ollama服务器IP地址，默认自动寻找可用服务器'
    )
    
    parser.add_argument(
        '--port',
        type=int,
        default=11434,
        help='Ollama服务器端口，默认11434'
    )
    
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='仅显示将要处理的工具，不实际更新数据库'
    )
    
    parser.add_argument(
        '--status',
        action='store_true',
        help='显示当前翻译状态统计'
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='启用详细日志输出'
    )
    
    args = parser.parse_args()
    
    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.debug("启用详细日志模式")
    
    # 创建数据库会话
    db = None
    generator = None
    
    try:
        # 建立数据库连接
        logger.info("建立数据库连接...")
        db = SessionLocal()
        
        # 创建生成器实例
        generator = ChineseDescriptionGenerator(db)
        
        # 初始化Ollama服务
        logger.info("初始化Ollama服务...")
        success = generator.initialize_ollama_service(args.ip_address, args.port)
        if not success:
            logger.error("无法连接到Ollama服务，请检查服务是否启动")
            return 1
        
        # 根据参数执行相应操作
        if args.status:
            show_status(generator)
        else:
            # 执行翻译任务
            success = run_translation(generator, args)
            if not success:
                return 1
        
        logger.info("程序执行完成")
        return 0
        
    except KeyboardInterrupt:
        logger.info("用户中断程序执行")
        return 1
    except Exception as e:
        logger.error(f"程序执行过程中发生未预期的错误: {str(e)}")
        return 1
    finally:
        if db:
            db.close()
            logger.info("数据库连接已关闭")


def run_translation(generator: ChineseDescriptionGenerator, args) -> bool:
    """运行翻译任务"""
    try:
        logger.info("开始执行中文翻译任务")
        logger.info(f"配置参数: model={args.model}, limit={args.limit}")
        
        # 执行翻译
        result = generator.generate_chinese_descriptions(
            limit=args.limit,
            model_name=args.model,
            dry_run=args.dry_run
        )
        
        # 显示结果
        if result["success"]:
            logger.info("任务执行完成")
            logger.info(f"结果: {result['message']}")
            
            if not args.dry_run:
                logger.info(f"处理: {result['processed']} 个工具")
                logger.info(f"成功: {result['succeeded']} 个")
                logger.info(f"失败: {result['failed']} 个")
                
                if result["errors"]:
                    logger.warning("错误详情:")
                    for error in result["errors"]:
                        logger.warning(f"  - {error}")
            else:
                if "tools" in result:
                    logger.info("将要处理的工具:")
                    for tool in result["tools"]:
                        logger.info(f"  - {tool['tool_id']}: {tool['name']}")
            
            return True
        else:
            logger.error(f"任务执行失败: {result.get('error', '未知错误')}")
            return False
            
    except Exception as e:
        logger.error(f"执行翻译任务时发生错误: {str(e)}")
        return False


def show_status(generator: ChineseDescriptionGenerator):
    """显示翻译状态"""
    try:
        logger.info("获取翻译状态...")
        status = generator.get_translation_status()
        
        if status["success"]:
            print(f"\n=== 中文翻译状态统计 ===")
            print(f"英文翻译总数: {status['english_translations']}")
            print(f"中文翻译总数: {status['chinese_translations']}")
            print(f"待翻译数量: {status['need_translation']}")
            print(f"翻译完成率: {status['translation_rate']}%")
            print(f"Ollama服务状态: {'可用' if status['ollama_service_available'] else '不可用'}")
        else:
            logger.error(f"获取状态失败: {status.get('error', '未知错误')}")
            
    except Exception as e:
        logger.error(f"显示状态时发生错误: {str(e)}")


if __name__ == '__main__':
    print("脚本开始执行...")
    try:
        result = main()
        print(f"脚本执行完成，返回码: {result}")
        sys.exit(result)
    except Exception as e:
        print(f"脚本执行出错: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)