#!/bin/bash

# Aistak FastAPI 系统监控脚本
# 用于检查所有服务的运行状态和系统健康情况

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 状态图标
CHECK="✅"
CROSS="❌"
WARNING="⚠️"
INFO="ℹ️"
GEAR="⚙️"
CHART="📊"
CLOCK="🕐"

# 日志函数
log_info() {
    echo -e "${BLUE}${INFO}${NC} $1"
}

log_success() {
    echo -e "${GREEN}${CHECK}${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}${WARNING}${NC} $1"
}

log_error() {
    echo -e "${RED}${CROSS}${NC} $1"
}

log_stat() {
    echo -e "${CYAN}${CHART}${NC} $1"
}

# 检查服务状态
check_service_status() {
    local service_name=$1
    local check_command=$2
    
    if eval $check_command &> /dev/null; then
        log_success "$service_name: 运行正常"
        return 0
    else
        log_error "$service_name: 服务异常"
        return 1
    fi
}

# 检查端口状态
check_port_status() {
    local port=$1
    local service_name=$2
    
    if lsof -i :$port &> /dev/null; then
        log_success "$service_name (端口$port): 正在监听"
        return 0
    else
        log_error "$service_name (端口$port): 未监听"
        return 1
    fi
}

# 获取响应时间
get_response_time() {
    local url=$1
    local timeout=${2:-5}
    
    local response_time=$(curl -o /dev/null -s -w '%{time_total}' --max-time $timeout "$url" 2>/dev/null || echo "timeout")
    echo $response_time
}

# 格式化字节数
format_bytes() {
    local bytes=$1
    if [ $bytes -gt 1073741824 ]; then
        echo "$(echo "scale=2; $bytes/1073741824" | bc)GB"
    elif [ $bytes -gt 1048576 ]; then
        echo "$(echo "scale=2; $bytes/1048576" | bc)MB"
    elif [ $bytes -gt 1024 ]; then
        echo "$(echo "scale=2; $bytes/1024" | bc)KB"
    else
        echo "${bytes}B"
    fi
}

# 主监控函数
main() {
    clear
    echo "========================================"
    echo "    🚀 Aistak FastAPI 系统监控"
    echo "========================================"
    echo -e "${PURPLE}监控时间:${NC} $(date)"
    echo -e "${PURPLE}系统信息:${NC} $(uname -a)"
    echo

    # 1. 服务状态检查
    echo -e "${BLUE}${GEAR} 服务状态检查${NC}"
    echo "----------------------------------------"
    
    # FastAPI应用检查
    if check_service_status "FastAPI应用" "curl -s http://localhost:8000/api/v1/health/"; then
        local api_response_time=$(get_response_time "http://localhost:8000/api/v1/health/")
        if [ "$api_response_time" != "timeout" ]; then
            log_stat "  响应时间: ${api_response_time}s"
        fi
        log_stat "  API文档: http://localhost:8000/docs"
    fi
    
    # 数据库连接检查
    local db_status=$(curl -s http://localhost:8000/api/v1/health/database 2>/dev/null | jq -r '.status' 2>/dev/null || echo "unknown")
    if [ "$db_status" = "healthy" ]; then
        log_success "数据库连接: 正常"
        local db_info=$(curl -s http://localhost:8000/api/v1/health/database/info 2>/dev/null)
        if [ -n "$db_info" ]; then
            local active_conn=$(echo "$db_info" | jq -r '.active_connections' 2>/dev/null || echo "unknown")
            local pool_size=$(echo "$db_info" | jq -r '.pool_size' 2>/dev/null || echo "unknown")
            log_stat "  活跃连接: $active_conn/$pool_size"
        fi
    else
        log_error "数据库连接: 异常 ($db_status)"
    fi
    
    # Ollama服务检查
    if check_service_status "Ollama服务" "curl -s http://localhost:11434/api/tags"; then
        local model_count=$(curl -s http://localhost:11434/api/tags 2>/dev/null | jq '.models | length' 2>/dev/null || echo "unknown")
        log_stat "  可用模型: $model_count 个"
        
        # 列出前3个模型
        local models=$(curl -s http://localhost:11434/api/tags 2>/dev/null | jq -r '.models[0:3][].name' 2>/dev/null | tr '\n' ', ' | sed 's/,$//')
        if [ -n "$models" ]; then
            log_stat "  主要模型: $models"
        fi
    fi
    
    echo

    # 2. 进程状态检查
    echo -e "${BLUE}${GEAR} 进程状态检查${NC}"
    echo "----------------------------------------"
    
    # FastAPI进程
    local fastapi_pids=$(pgrep -f "uvicorn main:app" 2>/dev/null || true)
    if [ -n "$fastapi_pids" ]; then
        log_success "FastAPI进程: 运行中 (PID: $fastapi_pids)"
    else
        log_error "FastAPI进程: 未运行"
    fi
    
    # Ollama进程
    local ollama_pids=$(pgrep -f "ollama serve" 2>/dev/null || true)
    if [ -n "$ollama_pids" ]; then
        log_success "Ollama进程: 运行中 (PID: $ollama_pids)"
    else
        log_error "Ollama进程: 未运行"
    fi
    
    # AI生成任务
    local generation_pids=$(pgrep -f "generate_descriptions" 2>/dev/null || true)
    if [ -n "$generation_pids" ]; then
        log_warning "AI生成任务: 运行中 (PID: $generation_pids)"
    else
        log_info "AI生成任务: 无运行中任务"
    fi
    
    echo

    # 3. 端口监听检查
    echo -e "${BLUE}${GEAR} 端口监听检查${NC}"
    echo "----------------------------------------"
    check_port_status 8000 "FastAPI"
    check_port_status 11434 "Ollama"
    echo

    # 4. 系统资源使用情况
    echo -e "${BLUE}${CHART} 系统资源使用${NC}"
    echo "----------------------------------------"
    
    # CPU使用率（兼容macOS和Linux）
    local cpu_usage="unknown"
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        cpu_usage=$(top -l 1 -n 0 | grep "CPU usage" | awk '{print $3}' | sed 's/%//' 2>/dev/null || echo "unknown")
    else
        # Linux
        cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}' 2>/dev/null || echo "unknown")
    fi
    log_stat "CPU使用率: ${cpu_usage}%"

    # 内存使用情况（兼容macOS和Linux）
    local total_mem used_mem free_mem mem_percent
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        local vm_stat_output=$(vm_stat)
        local page_size=$(vm_stat | head -1 | awk '{print $8}' | sed 's/\.//')
        local pages_free=$(echo "$vm_stat_output" | grep "Pages free" | awk '{print $3}' | sed 's/\.//')
        local pages_active=$(echo "$vm_stat_output" | grep "Pages active" | awk '{print $3}' | sed 's/\.//')
        local pages_inactive=$(echo "$vm_stat_output" | grep "Pages inactive" | awk '{print $3}' | sed 's/\.//')
        local pages_wired=$(echo "$vm_stat_output" | grep "Pages wired down" | awk '{print $4}' | sed 's/\.//')

        total_mem=$(echo "($pages_free + $pages_active + $pages_inactive + $pages_wired) * $page_size" | bc 2>/dev/null || echo "0")
        used_mem=$(echo "($pages_active + $pages_inactive + $pages_wired) * $page_size" | bc 2>/dev/null || echo "0")
        free_mem=$(echo "$pages_free * $page_size" | bc 2>/dev/null || echo "0")
    else
        # Linux
        local memory_info=$(free -b)
        total_mem=$(echo "$memory_info" | awk 'NR==2{print $2}')
        used_mem=$(echo "$memory_info" | awk 'NR==2{print $3}')
        free_mem=$(echo "$memory_info" | awk 'NR==2{print $4}')
    fi

    if [ "$total_mem" -gt 0 ] 2>/dev/null; then
        mem_percent=$(echo "scale=1; $used_mem*100/$total_mem" | bc 2>/dev/null || echo "unknown")
    else
        mem_percent="unknown"
    fi
    
    log_stat "内存使用: $(format_bytes $used_mem)/$(format_bytes $total_mem) (${mem_percent}%)"
    log_stat "可用内存: $(format_bytes $free_mem)"
    
    # 磁盘使用情况
    local disk_usage=$(df -h / | awk 'NR==2{print $5}' | sed 's/%//')
    local disk_avail=$(df -h / | awk 'NR==2{print $4}')
    log_stat "磁盘使用: ${disk_usage}% (可用: $disk_avail)"
    
    # 系统负载
    local load_avg=$(uptime | awk -F'load average:' '{print $2}' | sed 's/^[ \t]*//')
    log_stat "系统负载: $load_avg"
    
    echo

    # 5. AI内容生成状态
    echo -e "${BLUE}${CHART} AI内容生成状态${NC}"
    echo "----------------------------------------"
    
    if command -v python &> /dev/null && [ -f "generate_descriptions.py" ]; then
        local generation_status=$(python generate_descriptions.py --status 2>/dev/null | grep -E "(总工具数|完成率)" | head -5)
        if [ -n "$generation_status" ]; then
            echo "$generation_status" | while read line; do
                log_stat "$line"
            done
        else
            log_warning "无法获取生成状态"
        fi
    else
        log_warning "生成脚本不可用"
    fi
    
    echo

    # 6. 日志文件状态
    echo -e "${BLUE}${INFO} 日志文件状态${NC}"
    echo "----------------------------------------"
    
    for log_file in app.log ollama.log generate_descriptions.log; do
        if [ -f "$log_file" ]; then
            local file_size mod_time
            if [[ "$OSTYPE" == "darwin"* ]]; then
                # macOS
                file_size=$(stat -f%z "$log_file" 2>/dev/null || echo "0")
                mod_time=$(stat -f%Sm "$log_file" 2>/dev/null || echo "unknown")
            else
                # Linux
                file_size=$(stat -c%s "$log_file" 2>/dev/null || echo "0")
                mod_time=$(stat -c%y "$log_file" 2>/dev/null | cut -d' ' -f1-2 || echo "unknown")
            fi
            log_stat "$log_file: $(format_bytes $file_size) (修改: $mod_time)"
        else
            log_info "$log_file: 不存在"
        fi
    done
    
    echo

    # 7. 网络连接状态
    echo -e "${BLUE}${INFO} 网络连接状态${NC}"
    echo "----------------------------------------"
    
    local established_conn=$(netstat -an 2>/dev/null | grep ESTABLISHED | wc -l | tr -d ' ')
    local listening_ports=$(netstat -tln 2>/dev/null | grep LISTEN | wc -l | tr -d ' ')
    
    log_stat "已建立连接: $established_conn"
    log_stat "监听端口数: $listening_ports"
    
    echo

    # 8. 快速操作提示
    echo -e "${BLUE}${GEAR} 快速操作命令${NC}"
    echo "----------------------------------------"
    echo "重启服务: ./start_services.sh"
    echo "停止服务: ./stop_services.sh"
    echo "查看日志: tail -f app.log"
    echo "生成描述: python generate_descriptions.py --limit 5"
    echo "健康检查: curl http://localhost:8000/api/v1/health/"
    
    echo
    echo "========================================"
    echo -e "${GREEN}${CLOCK} 监控完成 - $(date)${NC}"
    echo "========================================"
}

# 处理中断信号
trap 'echo -e "\n${YELLOW}监控被中断${NC}"; exit 0' INT TERM

# 检查是否需要持续监控
if [ "$1" = "--watch" ] || [ "$1" = "-w" ]; then
    echo "持续监控模式 (按Ctrl+C退出)"
    while true; do
        main
        sleep 30
    done
else
    main
fi
