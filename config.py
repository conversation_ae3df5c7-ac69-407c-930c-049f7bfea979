#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
流量数据处理系统配置文件
"""

import os
from dataclasses import dataclass
from typing import Optional


@dataclass
class DatabaseConfig:
    """数据库配置"""
    host: str = "127.0.0.1"
    port: int = 5432
    database: str = "aistak_db"
    user: str = "root"
    password: str = "fuwen<PERSON>"
    
    @classmethod
    def from_env(cls) -> 'DatabaseConfig':
        """从环境变量创建配置"""
        return cls(
            host=os.getenv('DB_HOST', '127.0.0.1'),
            port=int(os.getenv('DB_PORT', '5432')),
            database=os.getenv('DB_NAME', 'aistak_db'),
            user=os.getenv('DB_USER', 'root'),
            password=os.getenv('DB_PASSWORD', 'fuwenhao')
        )


@dataclass
class SpiderConfig:
    """爬虫配置"""
    base_url: str = "https://traffic.cv"
    request_timeout: int = 30
    request_delay: int = 2  # 请求间隔秒数
    max_retries: int = 3
    user_agent: str = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'


@dataclass
class LogConfig:
    """日志配置"""
    level: str = "INFO"
    format: str = '%(asctime)s - %(levelname)s - %(message)s'
    file_enabled: bool = True
    file_prefix: str = "traffic_processor"


# 默认配置实例
DEFAULT_DB_CONFIG = DatabaseConfig()
DEFAULT_SPIDER_CONFIG = SpiderConfig()
DEFAULT_LOG_CONFIG = LogConfig()

# 国家代码映射
COUNTRY_CODE_MAPPING = {
    'United States': 'USA',
    'China': 'CHN',
    'India': 'IND',
    'Korea, Republic of': 'KOR',
    'Brazil': 'BRA',
    'Japan': 'JPN',
    'Germany': 'DEU',
    'United Kingdom': 'GBR',
    'France': 'FRA',
    'Canada': 'CAN',
    'Australia': 'AUS',
    'Italy': 'ITA',
    'Spain': 'ESP',
    'Netherlands': 'NLD',
    'Russia': 'RUS',
    'Mexico': 'MEX',
    'Indonesia': 'IDN',
    'Turkey': 'TUR',
    'Saudi Arabia': 'SAU',
    'South Africa': 'ZAF'
}

# 流量来源类型映射
TRAFFIC_SOURCE_MAPPING = {
    'direct': '直接访问',
    'search': '搜索引擎',
    'referrals': '推荐网站',
    'social': '社交媒体',
    'paidReferrals': '付费推荐',
    'mail': '邮件营销'
}

# 数据验证规则
VALIDATION_RULES = {
    'domain': {
        'max_length': 255,
        'pattern': r'^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\.[a-zA-Z]{2,}$'
    },
    'tool_id': {
        'max_length': 50,
        'pattern': r'^[a-zA-Z0-9_-]+$'
    },
    'percentage': {
        'min_value': 0,
        'max_value': 100
    },
    'visits': {
        'min_value': 0,
        'max_value': 999999999999  # 12位数字
    }
}

# 数据转换配置
CONVERSION_CONFIG = {
    'visit_multipliers': {
        'K': 1_000,
        'M': 1_000_000,
        'B': 1_000_000_000
    },
    'date_formats': [
        '%Y-%m-%d',
        '%Y-%m-%d %H:%M:%S',
        '%Y/%m/%d',
        '%m/%d/%Y',
        '%d/%m/%Y'
    ]
}
