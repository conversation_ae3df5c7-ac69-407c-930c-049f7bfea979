#!/usr/bin/env python3
"""
域名价格信息解析独立脚本

这是一个独立的脚本，可以直接运行来解析域名的价格信息，无需启动FastAPI服务器。
它使用Ollama本地大模型来分析和解析网站的价格信息。

使用方法:
    python generate_price.py --help
    python generate_price.py --locale en --limit 10
    python generate_price.py --dry-run
    python generate_price.py --status
    python generate_price.py --domain example.com
"""

import sys
import argparse
import logging
import json
from pathlib import Path
from typing import Dict, Any, List, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.services.ollama_service import OllamaService
from app.services.content_extractor_service import ContentExtractorService
from app.models.tool import Tool, ToolTranslation

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('generate_price.log')
    ]
)
logger = logging.getLogger(__name__)


class DomainPriceAnalysisService:
    """域名价格分析服务类"""

    def __init__(self, db: Session):
        """
        初始化价格分析服务

        Args:
            db: 数据库会话
        """
        self.db = db
        self.ollama_service = None
        self.content_extractor = None

    def initialize_services(
        self,
        ip_address: Optional[str] = None,
        port: int = 11434
    ) -> bool:
        """
        初始化Ollama和内容提取服务

        Args:
            ip_address: Ollama服务器IP地址
            port: Ollama服务器端口

        Returns:
            bool: 是否初始化成功
        """
        try:
            # 初始化Ollama服务
            self.ollama_service = OllamaService(ip_address, port)

            # 初始化内容提取服务
            self.content_extractor = ContentExtractorService()

            logger.info(f"服务初始化成功，使用Ollama服务器: {self.ollama_service.ip_address}:{self.ollama_service.port}")
            return True

        except Exception as e:
            logger.error(f"服务初始化失败: {str(e)}")
            return False

    def analyze_domain_pricing(
        self,
        domain: str,
        model_name: str = 'gemma3:latest',
        max_tokens: int = 2048,
        temperature: float = 0.3,
        max_retries: int = 3,
        locale: str = 'en'
    ) -> Dict[str, Any]:
        """
        分析单个域名的价格信息

        Args:
            domain: 域名
            model_name: 使用的模型名称
            max_tokens: 最大令牌数
            temperature: 生成温度
            max_retries: 最大重试次数
            locale: 语言代码，影响分析结果的语言

        Returns:
            Dict[str, Any]: 分析结果
        """
        if not self.ollama_service or not self.content_extractor:
            return {
                "success": False,
                "error": "服务未初始化，请先调用 initialize_services()"
            }

        # 清理域名格式
        domain = domain.strip().lower()
        if domain.startswith(('http://', 'https://')):
            from urllib.parse import urlparse
            parsed = urlparse(domain)
            domain = parsed.netloc

        logger.info(f"开始分析域名价格: {domain}")

        # 构建URL尝试列表，优先尝试pricing页面
        pricing_urls = [
            f"https://{domain}/pricing",
            f"https://www.{domain}/pricing",
            f"https://{domain}/price",
            f"https://www.{domain}/price",
            f"https://{domain}/plans",
            f"https://www.{domain}/plans"
        ]
        
        main_urls = [
            f"https://{domain}",
            f"https://www.{domain}",
            f"http://{domain}",
            f"http://www.{domain}"
        ]

        website_data = None
        successful_url = None
        url_type = None

        # 首先尝试pricing相关页面
        logger.info(f"优先尝试价格相关页面...")
        for url in pricing_urls:
            try:
                logger.info(f"尝试提取价格页面内容: {url}")
                website_data = self.content_extractor.extract_website_content(
                    url=url,
                    max_chunks=5,  # 增加块数量以获取更多内容
                    chunk_size=3000  # 增加块大小
                )
                
                if website_data and website_data.get("chunks"):
                    successful_url = url
                    url_type = "pricing_page"
                    logger.info(f"✅ 成功从价格页面 {url} 提取到 {len(website_data['chunks'])} 个内容块")
                    break
                else:
                    logger.debug(f"从价格页面 {url} 未能提取到有效内容")
                    
            except Exception as e:
                logger.debug(f"从价格页面 {url} 提取内容失败: {str(e)}")
                continue

        # 如果pricing页面都失败，尝试主页面
        if not website_data or not website_data.get("chunks"):
            logger.info(f"价格页面未找到有效内容，尝试主页面...")
            for url in main_urls:
                try:
                    logger.info(f"尝试提取主页面内容: {url}")
                    website_data = self.content_extractor.extract_website_content(
                        url=url,
                        max_chunks=5,  # 增加块数量以获取更多内容
                        chunk_size=3000  # 增加块大小
                    )
                    
                    if website_data and website_data.get("chunks"):
                        successful_url = url
                        url_type = "main_page"
                        logger.info(f"✅ 成功从主页面 {url} 提取到 {len(website_data['chunks'])} 个内容块")
                        break
                    else:
                        logger.warning(f"从主页面 {url} 未能提取到有效内容")
                        
                except Exception as e:
                    logger.warning(f"从主页面 {url} 提取内容失败: {str(e)}")
                    continue

        if not website_data or not website_data.get("chunks"):
            all_tried_urls = pricing_urls + main_urls
            return {
                "success": False,
                "error": f"无法从域名 {domain} 的任何URL变体中提取内容。尝试的URL: {', '.join(all_tried_urls)}"
            }

        try:
            # 合并所有内容块，包括标题和描述
            content_parts = []
            
            if website_data.get("title"):
                content_parts.append(f"网站标题: {website_data['title']}")
            
            if website_data.get("meta_description"):
                content_parts.append(f"网站描述: {website_data['meta_description']}")
            
            content_parts.extend(website_data["chunks"])
            content = "\n\n".join(content_parts)

            # 构建价格分析提示词
            prompt = self._build_price_analysis_prompt(domain, content, url_type, locale)

            # 调用Ollama进行价格分析，带重试机制
            logger.info(f"正在使用模型 {model_name} 分析价格信息")

            # 根据locale确定系统提示词语言
            if locale and locale.startswith('zh'):
                system_prompt = """你是一个专业的价格信息分析专家。请仔细分析提供的网站内容，提取和解析所有价格相关信息。

重要要求：
1. 必须返回有效的JSON格式
2. 如果找不到明确价格信息，在notes字段中说明
3. 识别所有定价方案、免费试用、订阅模式等
4. 提取具体的价格数字和货币单位

请严格按照以下JSON格式返回结果：
{
    "pricing_model": "订阅制/一次性付费/免费增值/按使用量计费/未知",
    "plans": [],
    "free_trial": {"available": false, "duration": "", "limitations": ""},
    "special_offers": [],
    "currency": "",
    "notes": ""
}"""
            else:
                system_prompt = """You are a professional pricing information analysis expert. Please carefully analyze the provided website content and extract all pricing-related information.

Important requirements:
1. Must return valid JSON format
2. If no clear pricing information is found, explain in the notes field
3. Identify all pricing plans, free trials, subscription models, etc.
4. Extract specific price numbers and currency units
5. All content must be in English

Please return results strictly in the following JSON format:
{
    "pricing_model": "subscription/one-time/freemium/usage-based/unknown",
    "plans": [],
    "free_trial": {"available": false, "duration": "", "limitations": ""},
    "special_offers": [],
    "currency": "",
    "notes": ""
}"""

            analysis_result = None
            last_error = None

            for attempt in range(max_retries):
                try:
                    analysis_result = self.ollama_service.call_ollama(
                        prompt=prompt,
                        model=model_name,
                        system_prompt=system_prompt,
                        temperature=temperature,
                        max_tokens=max_tokens
                    )

                    # 检查返回结果是否包含错误
                    if isinstance(analysis_result, dict) and "error" in analysis_result:
                        last_error = analysis_result['error']
                        logger.warning(f"Ollama调用失败 (尝试 {attempt + 1}/{max_retries}): {last_error}")
                        if attempt < max_retries - 1:
                            import time
                            time.sleep(2 ** attempt)  # 指数退避
                            continue
                    else:
                        break

                except Exception as e:
                    last_error = str(e)
                    logger.warning(f"Ollama调用异常 (尝试 {attempt + 1}/{max_retries}): {last_error}")
                    if attempt < max_retries - 1:
                        import time
                        time.sleep(2 ** attempt)
                        continue

            if isinstance(analysis_result, dict) and "error" in analysis_result:
                return {
                    "success": False,
                    "error": f"Ollama分析失败 (重试{max_retries}次后): {last_error}"
                }

            if not analysis_result:
                return {
                    "success": False,
                    "error": f"Ollama未返回结果 (重试{max_retries}次后): {last_error}"
                }

            # 尝试解析JSON结果
            parsed_result = self._parse_analysis_result(analysis_result)
            
            return {
                "success": True,
                "domain": domain,
                "url": successful_url,
                "url_type": url_type,  # 添加URL类型信息
                "content_summary": {
                    "title": website_data.get("title", ""),
                    "description": website_data.get("meta_description", ""),
                    "content_blocks": len(website_data.get("chunks", [])),
                    "total_content_length": len(content),
                    "source_type": "Dedicated pricing page" if url_type == "pricing_page" else "Main page"
                },
                "pricing_analysis": parsed_result,
                "raw_analysis": analysis_result if isinstance(analysis_result, str) else str(analysis_result)
            }

        except Exception as e:
            logger.error(f"分析域名价格时发生错误: {str(e)}")
            return {
                "success": False,
                "error": f"处理过程中发生错误: {str(e)}"
            }

    def batch_analyze_tools_pricing(
        self,
        locale: str = 'en',
        model_name: str = 'gemma3:latest',
        limit: int = 10,
        dry_run: bool = False,
        delay_between_requests: float = 1.0,
        update_database: bool = False
    ) -> Dict[str, Any]:
        """
        批量分析工具的价格信息

        Args:
            locale: 语言代码
            model_name: 使用的模型名称
            limit: 处理的工具数量上限
            dry_run: 是否为测试模式
            delay_between_requests: 请求之间的延迟时间（秒）
            update_database: 是否更新数据库中的pricing_details字段

        Returns:
            Dict[str, Any]: 处理结果统计
        """
        if not self.ollama_service or not self.content_extractor:
            return {
                "success": False,
                "error": "服务未初始化，请先调用 initialize_services()"
            }

        try:
            # 查询需要分析价格的工具 - 优先处理pricing_details为空的记录
            if update_database:
                # 查询pricing_details为空的工具
                tools_query = self.db.query(Tool).join(ToolTranslation).filter(
                    ToolTranslation.locale == locale,
                    Tool.url.isnot(None),
                    Tool.url != "",
                    ToolTranslation.pricing_details.is_(None)
                ).limit(limit)
                logger.info(f"查询pricing_details为空的工具记录 (locale={locale})")
            else:
                # 普通查询
                tools_query = self.db.query(Tool).join(ToolTranslation).filter(
                    ToolTranslation.locale == locale,
                    Tool.url.isnot(None),
                    Tool.url != ""
                ).limit(limit)

            tools = tools_query.all()

            if not tools:
                message = f"没有找到需要分析价格的工具 (语言: {locale})"
                if update_database:
                    message += " - 所有记录的pricing_details字段都已有数据"
                return {
                    "success": True,
                    "message": message,
                    "processed": 0,
                    "succeeded": 0,
                    "failed": 0
                }

            if dry_run:
                tool_list = []
                for tool in tools:
                    translation = next((t for t in tool.translations if t.locale == locale), None)
                    tool_list.append({
                        "tool_id": tool.tool_id,
                        "name": translation.name if translation else "未知",
                        "url": tool.url,
                        "domain": self._extract_domain_from_url(tool.url)
                    })

                return {
                    "success": True,
                    "message": f"找到 {len(tools)} 个工具需要分析价格",
                    "tools": tool_list
                }

            # 执行批量分析
            processed = 0
            succeeded = 0
            failed = 0
            skipped = 0
            errors = []
            results = []
            
            total_tools = len(tools)
            logger.info(f"开始批量分析 {total_tools} 个工具的价格信息")

            for i, tool in enumerate(tools, 1):
                try:
                    translation = next((t for t in tool.translations if t.locale == locale), None)
                    tool_name = translation.name if translation else "未知工具"

                    logger.info(f"[{i}/{total_tools}] 正在分析工具: {tool_name}")
                    logger.info(f"工具URL: {tool.url}")

                    # 从URL提取域名
                    domain = self._extract_domain_from_url(tool.url)

                    if not domain:
                        logger.warning(f"无法从URL提取域名: {tool.url}")
                        failed += 1
                        errors.append(f"{tool_name}: 无法从URL '{tool.url}' 提取有效域名")
                        processed += 1
                        continue

                    # 检查域名是否有效
                    if not self._is_valid_domain(domain):
                        logger.warning(f"域名格式无效: {domain}")
                        failed += 1
                        errors.append(f"{tool_name}: 域名格式无效 '{domain}'")
                        processed += 1
                        continue

                    logger.info(f"提取的域名: {domain}")

                    # 分析价格
                    result = self.analyze_domain_pricing(
                        domain=domain,
                        model_name=model_name,
                        locale=locale
                    )

                    if result["success"]:
                        succeeded += 1
                        analysis_summary = self._create_analysis_summary(result["pricing_analysis"])
                        
                        # 如果启用数据库更新，保存价格信息到数据库
                        if update_database:
                            try:
                                self._update_pricing_details(tool.tool_id, locale, result["pricing_analysis"])
                                logger.info(f"💾 [{i}/{total_tools}] 已更新数据库: {tool_name}")
                            except Exception as db_error:
                                logger.error(f"❌ [{i}/{total_tools}] 数据库更新失败: {tool_name} - {str(db_error)}")
                        
                        results.append({
                            "tool_id": tool.tool_id,
                            "tool_name": tool_name,
                            "url": tool.url,
                            "domain": domain,
                            "pricing_analysis": result["pricing_analysis"],
                            "content_summary": result.get("content_summary", {}),
                            "analysis_summary": analysis_summary
                        })
                        logger.info(f"✅ [{i}/{total_tools}] 成功分析: {tool_name} - {analysis_summary}")
                    else:
                        failed += 1
                        error_msg = result.get('error', '未知错误')
                        errors.append(f"{tool_name}: {error_msg}")
                        logger.error(f"❌ [{i}/{total_tools}] 分析失败: {tool_name} - {error_msg}")

                    processed += 1

                    # 添加延迟以避免过于频繁的请求
                    if delay_between_requests > 0 and i < total_tools:
                        import time
                        time.sleep(delay_between_requests)

                except KeyboardInterrupt:
                    logger.info("用户中断批量分析")
                    break
                except Exception as e:
                    failed += 1
                    error_msg = f"处理异常: {str(e)}"
                    errors.append(f"{tool_name if 'tool_name' in locals() else '未知工具'}: {error_msg}")
                    logger.error(f"❌ [{i}/{total_tools}] 处理工具时发生错误: {error_msg}")
                    processed += 1

            # 生成详细的结果报告
            success_rate = (succeeded / processed * 100) if processed > 0 else 0
            
            return {
                "success": True,
                "message": f"批量价格分析完成",
                "summary": {
                    "total_tools": total_tools,
                    "processed": processed,
                    "succeeded": succeeded,
                    "failed": failed,
                    "skipped": skipped,
                    "success_rate": f"{success_rate:.1f}%"
                },
                "errors": errors,
                "results": results,
                "model_used": model_name,
                "locale": locale
            }

        except Exception as e:
            logger.error(f"批量分析价格时发生错误: {str(e)}")
            return {
                "success": False,
                "error": f"批量分析失败: {str(e)}"
            }

    def _extract_domain_from_url(self, url: str) -> str:
        """从URL中提取域名"""
        try:
            from urllib.parse import urlparse
            if not url:
                return ""
            
            # 确保URL有协议前缀
            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url
            
            parsed_url = urlparse(url)
            domain = parsed_url.netloc.lower()
            
            # 移除www前缀
            if domain.startswith('www.'):
                domain = domain[4:]
            
            return domain
        except Exception as e:
            logger.warning(f"提取域名失败: {url} - {str(e)}")
            return ""

    def _is_valid_domain(self, domain: str) -> bool:
        """检查域名是否有效"""
        import re
        if not domain:
            return False
        
        # 基本的域名格式检查
        domain_pattern = r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$'
        return bool(re.match(domain_pattern, domain)) and '.' in domain

    def _create_analysis_summary(self, analysis: Dict[str, Any]) -> str:
        """创建分析结果摘要"""
        if not analysis:
            return "无分析结果"
        
        pricing_model = analysis.get("pricing_model", "未知")
        plans = analysis.get("plans", [])
        free_trial = analysis.get("free_trial", {})
        
        summary_parts = [f"模式: {pricing_model}"]
        
        if plans:
            plan_count = len(plans)
            free_plans = sum(1 for plan in plans if plan.get("is_free", False))
            if free_plans > 0:
                summary_parts.append(f"方案: {plan_count}个({free_plans}个免费)")
            else:
                summary_parts.append(f"方案: {plan_count}个")
        
        if free_trial.get("available", False):
            duration = free_trial.get("duration", "")
            summary_parts.append(f"试用: {duration}" if duration else "试用: 有")
        
        return ", ".join(summary_parts)

    def _build_price_analysis_prompt(self, domain: str, content: str, url_type: str = "main_page", locale: str = "en") -> str:
        """构建价格分析提示词"""
        # 限制内容长度，但保留更多内容用于分析
        max_content_length = 12000
        if len(content) > max_content_length:
            # 优先保留包含价格关键词的内容
            price_keywords = ['price', 'pricing', 'plan', 'subscription', 'free', 'trial', 'cost', '$', '€', '£', 'USD', 'month', 'year', 'annual', 'billing', 'payment']
            
            # 分割内容为段落
            paragraphs = content.split('\n\n')
            important_paragraphs = []
            other_paragraphs = []
            
            for para in paragraphs:
                if any(keyword.lower() in para.lower() for keyword in price_keywords):
                    important_paragraphs.append(para)
                else:
                    other_paragraphs.append(para)
            
            # 优先包含重要段落
            selected_content = '\n\n'.join(important_paragraphs)
            remaining_length = max_content_length - len(selected_content)
            
            if remaining_length > 0:
                additional_content = '\n\n'.join(other_paragraphs)
                if len(additional_content) <= remaining_length:
                    selected_content += '\n\n' + additional_content
                else:
                    selected_content += '\n\n' + additional_content[:remaining_length] + '...'
            
            content = selected_content

        # 根据locale和URL类型调整分析策略
        if locale.startswith('zh'):
            # 中文提示词
            content_source_note = ""
            if url_type == "pricing_page":
                content_source_note = "注意：以下内容来自专门的价格页面，应该包含详细的定价信息。"
            else:
                content_source_note = "注意：以下内容来自网站主页，价格信息可能不够详细，请仔细寻找。"

            prompt = f"""请分析以下网站内容，提取和解析所有价格相关信息。

网站域名: {domain}
{content_source_note}

网站内容:
{content}

分析任务：
请仔细分析上述内容，寻找并提取以下价格信息：

1. **定价模式识别**: 
   - 免费增值模式 (有免费版本 + 付费升级)
   - 订阅制 (月付/年付)
   - 一次性付费
   - 按使用量计费
   - 完全免费

2. **定价方案详情**:
   - 方案名称 (如: Free, Basic, Pro, Enterprise)
   - 具体价格和货币单位
   - 计费周期 (月付/年付/一次性)
   - 每个方案包含的主要功能
   - 是否为免费方案

3. **免费试用信息**:
   - 是否提供免费试用
   - 试用期长度
   - 试用期间的功能限制

4. **特殊优惠**:
   - 折扣信息
   - 促销活动
   - 优惠码

5. **其他重要信息**:
   - 价格更新时间
   - 退款政策
   - 支持的支付方式

请严格按照以下JSON格式返回分析结果，不要添加任何其他文本：

{{
    "pricing_model": "免费增值/订阅制/一次性付费/按使用量计费/完全免费/未知",
    "plans": [
        {{
            "name": "方案名称",
            "price": "具体价格数字",
            "currency": "货币单位",
            "billing_cycle": "月付/年付/一次性/按使用量",
            "features": ["主要功能1", "主要功能2"],
            "is_free": true/false,
            "popular": true/false
        }}
    ],
    "free_trial": {{
        "available": true/false,
        "duration": "试用期长度",
        "limitations": "试用限制说明"
    }},
    "special_offers": [
        "优惠信息1",
        "优惠信息2"
    ],
    "currency": "主要货币单位",
    "last_updated": "价格更新时间（如果有提及）",
    "notes": "其他重要说明或如果无法找到价格信息的说明"
}}

注意：如果网站内容中没有找到明确的价格信息，请在notes字段中详细说明，并将pricing_model设为"未知"。"""
        else:
            # 英文提示词
            content_source_note = ""
            if url_type == "pricing_page":
                content_source_note = "Note: The following content is from a dedicated pricing page and should contain detailed pricing information."
            else:
                content_source_note = "Note: The following content is from the website homepage, pricing information may not be detailed, please search carefully."

            prompt = f"""Please analyze the following website content and extract all pricing-related information.

Website domain: {domain}
{content_source_note}

Website content:
{content}

Analysis tasks:
Please carefully analyze the above content and extract the following pricing information:

1. **Pricing Model Identification**: 
   - Freemium model (free version + paid upgrades)
   - Subscription model (monthly/yearly)
   - One-time payment
   - Usage-based pricing
   - Completely free

2. **Pricing Plan Details**:
   - Plan names (e.g., Free, Basic, Pro, Enterprise)
   - Specific prices and currency units
   - Billing cycles (monthly/yearly/one-time)
   - Main features included in each plan
   - Whether it's a free plan

3. **Free Trial Information**:
   - Whether free trial is available
   - Trial period length
   - Feature limitations during trial

4. **Special Offers**:
   - Discount information
   - Promotional activities
   - Promo codes

5. **Other Important Information**:
   - Price update time
   - Refund policy
   - Supported payment methods

Please return analysis results strictly in the following JSON format, do not add any other text:

{{
    "pricing_model": "freemium/subscription/one-time/usage-based/free/unknown",
    "plans": [
        {{
            "name": "Plan name",
            "price": "Specific price number",
            "currency": "Currency unit",
            "billing_cycle": "monthly/yearly/one-time/usage-based",
            "features": ["Main feature 1", "Main feature 2"],
            "is_free": true/false,
            "popular": true/false
        }}
    ],
    "free_trial": {{
        "available": true/false,
        "duration": "Trial period length",
        "limitations": "Trial limitation description"
    }},
    "special_offers": [
        "Offer information 1",
        "Offer information 2"
    ],
    "currency": "Main currency unit",
    "last_updated": "Price update time (if mentioned)",
    "notes": "Other important notes or explanation if no clear pricing information is found"
}}

Note: If no clear pricing information is found in the website content, please explain in detail in the notes field and set pricing_model to "unknown"."""

        return prompt

    def _parse_analysis_result(self, result: str) -> Dict[str, Any]:
        """解析分析结果"""
        if not result:
            return {
                "pricing_model": "unknown",
                "plans": [],
                "free_trial": {"available": False, "duration": "", "limitations": ""},
                "special_offers": [],
                "currency": "",
                "notes": "AI model returned no analysis result"
            }

        # 如果结果已经是字典，直接返回
        if isinstance(result, dict):
            return result

        try:
            # 尝试直接解析JSON
            parsed = json.loads(result)
            return self._validate_and_fix_result(parsed)
        except json.JSONDecodeError:
            # 如果不是有效JSON，尝试多种方法提取JSON部分
            import re
            
            # 方法1: 寻找完整的JSON对象
            json_patterns = [
                r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}',  # 简单嵌套JSON
                r'\{.*?\}',  # 最短匹配
                r'\{.*\}',   # 最长匹配
            ]
            
            for pattern in json_patterns:
                json_matches = re.findall(pattern, result, re.DOTALL)
                for match in json_matches:
                    try:
                        parsed = json.loads(match)
                        if isinstance(parsed, dict) and 'pricing_model' in parsed:
                            return self._validate_and_fix_result(parsed)
                    except json.JSONDecodeError:
                        continue
            
            # 方法2: 尝试修复常见的JSON格式问题
            cleaned_result = self._clean_json_string(result)
            if cleaned_result:
                try:
                    parsed = json.loads(cleaned_result)
                    return self._validate_and_fix_result(parsed)
                except json.JSONDecodeError:
                    pass
            
            # 方法3: 如果都失败了，尝试从文本中提取关键信息
            extracted_info = self._extract_info_from_text(result)
            if extracted_info:
                return extracted_info

            # 最后的备选方案：返回原始文本
            return {
                "pricing_model": "unknown",
                "plans": [],
                "free_trial": {"available": False, "duration": "", "limitations": ""},
                "special_offers": [],
                "currency": "",
                "notes": f"Unable to parse AI analysis result, original content: {result[:500]}..."
            }

    def _clean_json_string(self, json_str: str) -> str:
        """清理JSON字符串中的常见问题"""
        import re
        
        # 移除JSON前后的多余文本
        json_str = json_str.strip()
        
        # 寻找JSON开始和结束位置
        start_idx = json_str.find('{')
        end_idx = json_str.rfind('}')
        
        if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
            json_str = json_str[start_idx:end_idx + 1]
        
        # 修复常见的JSON格式问题
        json_str = re.sub(r',\s*}', '}', json_str)  # 移除对象末尾多余的逗号
        json_str = re.sub(r',\s*]', ']', json_str)  # 移除数组末尾多余的逗号
        json_str = re.sub(r':\s*,', ': null,', json_str)  # 修复空值
        
        return json_str

    def _extract_info_from_text(self, text: str) -> Dict[str, Any]:
        """从文本中提取价格信息"""
        import re
        
        result = {
            "pricing_model": "unknown",
            "plans": [],
            "free_trial": {"available": False, "duration": "", "limitations": ""},
            "special_offers": [],
            "currency": "",
            "notes": "Information extracted from text"
        }
        
        # 提取价格信息
        price_patterns = [
            r'\$(\d+(?:\.\d{2})?)',  # 美元
            r'€(\d+(?:\.\d{2})?)',   # 欧元
            r'£(\d+(?:\.\d{2})?)',   # 英镑
            r'(\d+(?:\.\d{2})?)\s*(?:USD|EUR|GBP)',  # 带货币代码的价格
        ]
        
        prices_found = []
        for pattern in price_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            prices_found.extend(matches)
        
        if prices_found:
            result["notes"] += f" Found prices: {', '.join(prices_found)}"
        
        # 检查是否提到免费
        if re.search(r'\bfree\b|\b免费\b', text, re.IGNORECASE):
            result["pricing_model"] = "freemium"
            result["plans"].append({
                "name": "Free",
                "price": "0",
                "currency": "",
                "billing_cycle": "free",
                "features": [],
                "is_free": True,
                "popular": False
            })
        
        # 检查订阅模式
        if re.search(r'\bmonth\b|\bmonthly\b|\byear\b|\byearly\b|\bannual\b|\bsubscription\b', text, re.IGNORECASE):
            if result["pricing_model"] == "unknown":
                result["pricing_model"] = "subscription"
        
        return result

    def _validate_and_fix_result(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """验证并修复解析结果"""
        # 确保必需字段存在
        required_fields = {
            "pricing_model": "unknown",
            "plans": [],
            "free_trial": {"available": False, "duration": "", "limitations": ""},
            "special_offers": [],
            "currency": "",
            "notes": ""
        }
        
        for field, default_value in required_fields.items():
            if field not in result:
                result[field] = default_value
        
        # 验证plans字段
        if not isinstance(result["plans"], list):
            result["plans"] = []
        
        # 验证每个plan的结构
        validated_plans = []
        for plan in result["plans"]:
            if isinstance(plan, dict):
                validated_plan = {
                    "name": plan.get("name", "Unknown Plan"),
                    "price": str(plan.get("price", "")),
                    "currency": plan.get("currency", ""),
                    "billing_cycle": plan.get("billing_cycle", ""),
                    "features": plan.get("features", []) if isinstance(plan.get("features"), list) else [],
                    "is_free": bool(plan.get("is_free", False)),
                    "popular": bool(plan.get("popular", False))
                }
                validated_plans.append(validated_plan)
        
        result["plans"] = validated_plans
        
        # 验证free_trial字段
        if not isinstance(result["free_trial"], dict):
            result["free_trial"] = {"available": False, "duration": "", "limitations": ""}
        
        # 验证special_offers字段
        if not isinstance(result["special_offers"], list):
            result["special_offers"] = []
        
        return result

    def _update_pricing_details(self, tool_id: str, locale: str, pricing_analysis: Dict[str, Any]) -> bool:
        """
        更新数据库中的pricing_details字段

        Args:
            tool_id: 工具ID
            locale: 语言代码
            pricing_analysis: 价格分析结果

        Returns:
            bool: 是否更新成功
        """
        try:
            # 查找对应的ToolTranslation记录
            translation = self.db.query(ToolTranslation).filter(
                ToolTranslation.tool_id == tool_id,
                ToolTranslation.locale == locale
            ).first()

            if not translation:
                logger.error(f"未找到工具翻译记录: tool_id={tool_id}, locale={locale}")
                return False

            # 将价格分析结果转换为JSON字符串
            pricing_json = json.dumps(pricing_analysis, ensure_ascii=False, indent=2)
            
            # 更新pricing_details字段
            translation.pricing_details = pricing_json
            
            # 提交更改
            self.db.commit()
            
            logger.info(f"✅ 成功更新pricing_details: tool_id={tool_id}, locale={locale}")
            return True

        except Exception as e:
            logger.error(f"❌ 更新pricing_details失败: tool_id={tool_id}, locale={locale}, error={str(e)}")
            self.db.rollback()
            return False

    def test_single_pricing_update(self, locale: str = 'en', model_name: str = 'gemma3:latest') -> Dict[str, Any]:
        """
        测试单个记录的价格更新功能

        Args:
            locale: 语言代码
            model_name: 使用的模型名称

        Returns:
            Dict[str, Any]: 测试结果
        """
        try:
            # 查找一个pricing_details为空的记录
            tool_with_translation = self.db.query(Tool, ToolTranslation).join(ToolTranslation).filter(
                ToolTranslation.locale == locale,
                Tool.url.isnot(None),
                Tool.url != "",
                ToolTranslation.pricing_details.is_(None)
            ).first()

            if not tool_with_translation:
                return {
                    "success": False,
                    "message": f"没有找到pricing_details为空的记录 (locale={locale})"
                }

            tool, translation = tool_with_translation
            
            logger.info(f"🧪 测试记录: tool_id={tool.tool_id}, name={translation.name}, url={tool.url}")
            print(f"\n🧪 测试单个记录的价格更新")
            print(f"工具ID: {tool.tool_id}")
            print(f"工具名称: {translation.name}")
            print(f"工具URL: {tool.url}")
            print(f"当前pricing_details: {translation.pricing_details}")

            # 提取域名
            domain = self._extract_domain_from_url(tool.url)
            if not domain:
                return {
                    "success": False,
                    "message": f"无法从URL提取域名: {tool.url}"
                }

            print(f"提取的域名: {domain}")

            # 分析价格
            result = self.analyze_domain_pricing(
                domain=domain,
                model_name=model_name,
                locale=locale
            )

            if result["success"]:
                # 更新数据库
                update_success = self._update_pricing_details(
                    tool.tool_id, 
                    locale, 
                    result["pricing_analysis"]
                )

                if update_success:
                    print(f"✅ 成功更新数据库记录")
                    print(f"价格分析摘要: {self._create_analysis_summary(result['pricing_analysis'])}")
                    
                    # 验证更新结果
                    updated_translation = self.db.query(ToolTranslation).filter(
                        ToolTranslation.tool_id == tool.tool_id,
                        ToolTranslation.locale == locale
                    ).first()
                    
                    if updated_translation and updated_translation.pricing_details:
                        print(f"✅ 数据库验证成功，pricing_details已更新")
                        print(f"存储的数据长度: {len(updated_translation.pricing_details)} 字符")
                    else:
                        print(f"❌ 数据库验证失败，pricing_details仍为空")

                    return {
                        "success": True,
                        "tool_id": tool.tool_id,
                        "tool_name": translation.name,
                        "domain": domain,
                        "pricing_analysis": result["pricing_analysis"],
                        "database_updated": update_success
                    }
                else:
                    return {
                        "success": False,
                        "message": "价格分析成功但数据库更新失败"
                    }
            else:
                return {
                    "success": False,
                    "message": f"价格分析失败: {result.get('error', '未知错误')}"
                }

        except Exception as e:
            logger.error(f"测试单个记录更新时发生错误: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    def get_available_models(self) -> List[Dict[str, Any]]:
        """
        获取可用的Ollama模型列表

        Returns:
            List[Dict[str, Any]]: 可用模型列表
        """
        if not self.ollama_service:
            return []

        return self.ollama_service.list_available_models()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='使用Ollama本地大模型分析域名价格信息',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 分析单个域名
  python generate_price.py --domain example.com

  # 批量分析工具价格
  python generate_price.py --locale en --limit 5

  # 批量分析并更新数据库
  python generate_price.py --locale en --limit 5 --update-db

  # 测试单个记录的数据库更新
  python generate_price.py --test-db-update --locale en

  # 干运行模式 - 仅显示将要处理的工具
  python generate_price.py --dry-run

  # 显示当前状态
  python generate_price.py --status

  # 列出可用模型
  python generate_price.py --list-models
        """
    )

    # 基本参数
    parser.add_argument(
        '--domain',
        type=str,
        help='要分析的单个域名'
    )

    parser.add_argument(
        '--locale',
        type=str,
        default='en',
        help='语言代码，默认为英语(en)'
    )

    parser.add_argument(
        '--model',
        type=str,
        default='gemma3:latest',
        help='使用的Ollama模型名称，默认为gemma3:latest'
    )

    parser.add_argument(
        '--limit',
        type=int,
        default=10,
        help='批量处理时的工具数量上限'
    )

    parser.add_argument(
        '--ip-address',
        type=str,
        help='Ollama服务器IP地址，默认自动寻找可用服务器'
    )

    parser.add_argument(
        '--port',
        type=int,
        default=11434,
        help='Ollama服务器端口，默认11434'
    )

    # 操作模式
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='仅显示将要处理的工具，不实际分析'
    )

    parser.add_argument(
        '--status',
        action='store_true',
        help='显示当前服务状态'
    )

    parser.add_argument(
        '--list-models',
        action='store_true',
        help='列出可用的Ollama模型'
    )

    parser.add_argument(
        '--verbose',
        action='store_true',
        help='启用详细日志输出'
    )

    parser.add_argument(
        '--output',
        type=str,
        help='输出结果到JSON文件'
    )

    parser.add_argument(
        '--test',
        action='store_true',
        help='运行测试模式，分析几个知名网站验证功能'
    )

    parser.add_argument(
        '--update-db',
        action='store_true',
        help='更新数据库中的pricing_details字段'
    )

    parser.add_argument(
        '--test-db-update',
        action='store_true',
        help='测试单个记录的数据库更新功能'
    )

    args = parser.parse_args()

    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.debug("启用详细日志模式")

    # 创建数据库会话
    db = None
    service = None

    try:
        # 建立数据库连接
        logger.info("建立数据库连接...")
        db = SessionLocal()

        # 创建服务实例
        service = DomainPriceAnalysisService(db)

        # 初始化Ollama服务
        logger.info("初始化Ollama服务...")
        success = service.initialize_services(args.ip_address, args.port)
        if not success:
            logger.error("无法连接到Ollama服务，请检查服务是否启动")
            return 1

        # 根据参数执行相应操作
        if args.status:
            show_status(service)
        elif args.list_models:
            list_models(service)
        elif args.test_db_update:
            # 测试数据库更新功能
            result = test_database_update(service, args)
            if args.output:
                save_result_to_file(result, args.output)
        elif args.test:
            # 运行测试模式
            result = run_test_mode(service, args)
            if args.output:
                save_result_to_file(result, args.output)
        elif args.domain:
            # 分析单个域名
            result = analyze_single_domain(service, args)
            if args.output:
                save_result_to_file(result, args.output)
        else:
            # 批量分析
            result = run_batch_analysis(service, args)
            if args.output:
                save_result_to_file(result, args.output)

        logger.info("程序执行完成")
        return 0

    except KeyboardInterrupt:
        logger.info("用户中断程序执行")
        return 1
    except Exception as e:
        logger.error(f"程序执行过程中发生未预期的错误: {str(e)}")
        return 1
    finally:
        if db:
            db.close()
            logger.info("数据库连接已关闭")


def analyze_single_domain(service: DomainPriceAnalysisService, args) -> Dict[str, Any]:
    """分析单个域名"""
    try:
        logger.info(f"开始分析域名: {args.domain}")
        print(f"\n🔍 正在分析域名: {args.domain}")
        print(f"使用模型: {args.model}")

        result = service.analyze_domain_pricing(
            domain=args.domain,
            model_name=args.model,
            locale=args.locale
        )

        if result["success"]:
            logger.info("✅ 域名价格分析完成")
            
            # 显示基本信息
            print(f"\n=== 域名价格分析结果 ===")
            print(f"域名: {result['domain']}")
            print(f"访问URL: {result['url']}")
            
            # 显示内容摘要
            content_summary = result.get("content_summary", {})
            if content_summary:
                print(f"\n--- 网站内容摘要 ---")
                print(f"内容来源: {content_summary.get('source_type', '未知')}")
                if content_summary.get("title"):
                    print(f"标题: {content_summary['title']}")
                if content_summary.get("description"):
                    print(f"描述: {content_summary['description'][:200]}...")
                print(f"内容块数: {content_summary.get('content_blocks', 0)}")
                print(f"总内容长度: {content_summary.get('total_content_length', 0)} 字符")

            # 显示价格分析结果
            pricing = result["pricing_analysis"]
            print(f"\n--- 价格分析结果 ---")
            print(f"定价模式: {pricing.get('pricing_model', '未知')}")
            
            # 显示定价方案
            plans = pricing.get("plans", [])
            if plans:
                print(f"\n定价方案 ({len(plans)} 个):")
                for i, plan in enumerate(plans, 1):
                    print(f"  {i}. {plan.get('name', '未知方案')}")
                    price = plan.get('price', '')
                    currency = plan.get('currency', '')
                    billing = plan.get('billing_cycle', '')
                    
                    if price and price != '0':
                        price_str = f"{currency}{price}" if currency else price
                        if billing:
                            price_str += f" / {billing}"
                        print(f"     价格: {price_str}")
                    elif plan.get('is_free', False):
                        print(f"     价格: 免费")
                    
                    features = plan.get('features', [])
                    if features:
                        print(f"     功能: {', '.join(features[:3])}")
                        if len(features) > 3:
                            print(f"           ... 还有 {len(features) - 3} 个功能")
                    
                    if plan.get('popular', False):
                        print(f"     🌟 推荐方案")
                    print()
            else:
                print("未找到具体的定价方案")

            # 显示免费试用信息
            free_trial = pricing.get("free_trial", {})
            if free_trial.get("available", False):
                print(f"免费试用: 是")
                if free_trial.get("duration"):
                    print(f"试用期: {free_trial['duration']}")
                if free_trial.get("limitations"):
                    print(f"试用限制: {free_trial['limitations']}")
            else:
                print(f"免费试用: 否")

            # 显示特殊优惠
            offers = pricing.get("special_offers", [])
            if offers:
                print(f"\n特殊优惠:")
                for offer in offers:
                    print(f"  • {offer}")

            # 显示备注
            notes = pricing.get("notes", "")
            if notes:
                print(f"\n备注: {notes}")

            # 如果需要详细信息，显示完整JSON
            if args.verbose:
                print(f"\n--- 完整分析结果 (JSON) ---")
                print(json.dumps(pricing, indent=2, ensure_ascii=False))

        else:
            logger.error(f"❌ 域名价格分析失败: {result.get('error', '未知错误')}")
            print(f"\n❌ 分析失败: {result.get('error', '未知错误')}")

        return result

    except Exception as e:
        logger.error(f"分析单个域名时发生错误: {str(e)}")
        print(f"\n❌ 分析过程中发生错误: {str(e)}")
        return {"success": False, "error": str(e)}


def run_batch_analysis(service: DomainPriceAnalysisService, args) -> Dict[str, Any]:
    """运行批量分析"""
    try:
        logger.info("开始执行批量价格分析任务")
        logger.info(f"配置参数: locale={args.locale}, model={args.model}, limit={args.limit}")

        result = service.batch_analyze_tools_pricing(
            locale=args.locale,
            model_name=args.model,
            limit=args.limit,
            dry_run=args.dry_run,
            delay_between_requests=1.0,  # 添加请求间延迟
            update_database=args.update_db  # 是否更新数据库
        )

        # 显示结果
        if result["success"]:
            logger.info("任务执行完成")
            
            if args.dry_run:
                # 干运行模式的结果显示
                if "tools" in result:
                    print(f"\n=== 将要分析的工具列表 ===")
                    print(f"总数: {len(result['tools'])} 个工具")
                    print(f"语言: {args.locale}")
                    print(f"模型: {args.model}")
                    print("\n工具详情:")
                    for i, tool in enumerate(result["tools"], 1):
                        print(f"{i:2d}. {tool['name']}")
                        print(f"     URL: {tool['url']}")
                        print(f"     域名: {tool['domain']}")
                        print()
                else:
                    print(result["message"])
            else:
                # 实际分析结果显示
                summary = result.get("summary", {})
                print(f"\n=== 批量价格分析完成 ===")
                print(f"总工具数: {summary.get('total_tools', 0)}")
                print(f"已处理: {summary.get('processed', 0)}")
                print(f"成功: {summary.get('succeeded', 0)}")
                print(f"失败: {summary.get('failed', 0)}")
                print(f"成功率: {summary.get('success_rate', '0%')}")
                print(f"使用模型: {result.get('model_used', args.model)}")
                if args.update_db:
                    print(f"数据库更新: 已启用")

                # 显示错误详情
                if result.get("errors"):
                    print(f"\n=== 错误详情 ({len(result['errors'])} 个) ===")
                    for i, error in enumerate(result["errors"][:10], 1):  # 最多显示10个错误
                        print(f"{i:2d}. {error}")
                    if len(result["errors"]) > 10:
                        print(f"... 还有 {len(result['errors']) - 10} 个错误")

                # 显示成功分析的结果摘要
                if result.get("results"):
                    print(f"\n=== 成功分析结果摘要 ===")
                    for i, item in enumerate(result["results"][:10], 1):  # 最多显示10个结果
                        print(f"{i:2d}. {item['tool_name']}")
                        print(f"     域名: {item['domain']}")
                        print(f"     摘要: {item.get('analysis_summary', '无摘要')}")
                        
                        # 显示定价方案数量
                        pricing = item.get('pricing_analysis', {})
                        if isinstance(pricing, dict):
                            plans = pricing.get('plans', [])
                            if plans:
                                plan_names = [plan.get('name', '未知') for plan in plans[:3]]
                                print(f"     方案: {', '.join(plan_names)}")
                                if len(plans) > 3:
                                    print(f"           ... 还有 {len(plans) - 3} 个方案")
                        print()
                    
                    if len(result["results"]) > 10:
                        print(f"... 还有 {len(result['results']) - 10} 个成功结果")

            return result
        else:
            logger.error(f"任务执行失败: {result.get('error', '未知错误')}")
            print(f"\n❌ 批量分析失败: {result.get('error', '未知错误')}")
            return result

    except Exception as e:
        logger.error(f"执行批量分析任务时发生错误: {str(e)}")
        print(f"\n❌ 执行过程中发生错误: {str(e)}")
        return {"success": False, "error": str(e)}


def show_status(service: DomainPriceAnalysisService):
    """显示服务状态"""
    try:
        logger.info("检查服务状态...")

        print(f"\n=== 域名价格分析服务状态 ===")

        if service.ollama_service:
            print(f"Ollama服务: 可用 ({service.ollama_service.ip_address}:{service.ollama_service.port})")

            # 检查模型
            models = service.get_available_models()
            print(f"可用模型数量: {len(models)}")
        else:
            print("Ollama服务: 不可用")

        if service.content_extractor:
            print("内容提取服务: 可用")
        else:
            print("内容提取服务: 不可用")

    except Exception as e:
        logger.error(f"显示状态时发生错误: {str(e)}")


def list_models(service: DomainPriceAnalysisService):
    """列出可用模型"""
    try:
        logger.info("获取可用模型列表...")
        models = service.get_available_models()

        if models:
            print("\n=== 可用的Ollama模型 ===")
            for model in models:
                name = model.get('name', 'Unknown')
                size = model.get('size', 'Unknown size')
                print(f"  - {name} ({size})")
        else:
            logger.warning("没有找到可用模型或Ollama服务不可用")

    except Exception as e:
        logger.error(f"列出模型时发生错误: {str(e)}")


def run_test_mode(service: DomainPriceAnalysisService, args) -> Dict[str, Any]:
    """运行测试模式"""
    test_domains = [
        "openai.com",
        "github.com", 
        "notion.so",
        "figma.com",
        "stripe.com"
    ]
    
    print(f"\n🧪 测试模式 - 分析 {len(test_domains)} 个知名网站")
    print(f"使用模型: {args.model}")
    
    results = []
    successful = 0
    failed = 0
    
    for i, domain in enumerate(test_domains, 1):
        print(f"\n[{i}/{len(test_domains)}] 测试域名: {domain}")
        
        try:
            result = service.analyze_domain_pricing(
                domain=domain,
                model_name=args.model,
                locale='en'  # 测试模式默认使用英文
            )
            
            if result["success"]:
                successful += 1
                pricing = result["pricing_analysis"]
                print(f"✅ 成功 - 模式: {pricing.get('pricing_model', '未知')}, 方案数: {len(pricing.get('plans', []))}")
                
                # 显示简要信息
                plans = pricing.get('plans', [])
                if plans:
                    plan_summary = []
                    for plan in plans[:3]:
                        name = plan.get('name', '未知')
                        if plan.get('is_free', False):
                            plan_summary.append(f"{name}(免费)")
                        else:
                            price = plan.get('price', '')
                            currency = plan.get('currency', '')
                            if price and price != '0':
                                plan_summary.append(f"{name}({currency}{price})")
                            else:
                                plan_summary.append(name)
                    print(f"    方案: {', '.join(plan_summary)}")
            else:
                failed += 1
                print(f"❌ 失败 - {result.get('error', '未知错误')}")
            
            results.append({
                "domain": domain,
                "success": result["success"],
                "result": result
            })
            
        except Exception as e:
            failed += 1
            print(f"❌ 异常 - {str(e)}")
            results.append({
                "domain": domain,
                "success": False,
                "error": str(e)
            })
    
    print(f"\n=== 测试结果汇总 ===")
    print(f"总测试数: {len(test_domains)}")
    print(f"成功: {successful}")
    print(f"失败: {failed}")
    print(f"成功率: {successful/len(test_domains)*100:.1f}%")
    
    return {
        "success": True,
        "test_results": results,
        "summary": {
            "total": len(test_domains),
            "successful": successful,
            "failed": failed,
            "success_rate": f"{successful/len(test_domains)*100:.1f}%"
        }
    }


def test_database_update(service: DomainPriceAnalysisService, args) -> Dict[str, Any]:
    """测试数据库更新功能"""
    try:
        print(f"\n🧪 测试数据库更新功能")
        print(f"语言: {args.locale}")
        print(f"模型: {args.model}")
        
        result = service.test_single_pricing_update(
            locale=args.locale,
            model_name=args.model
        )
        
        if result["success"]:
            print(f"\n✅ 测试成功完成")
        else:
            print(f"\n❌ 测试失败: {result.get('message', '未知错误')}")
        
        return result
        
    except Exception as e:
        logger.error(f"测试数据库更新功能时发生错误: {str(e)}")
        print(f"\n❌ 测试过程中发生错误: {str(e)}")
        return {"success": False, "error": str(e)}


def save_result_to_file(result: Dict[str, Any], filename: str):
    """保存结果到文件"""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        logger.info(f"结果已保存到文件: {filename}")
        print(f"📁 结果已保存到: {filename}")
    except Exception as e:
        logger.error(f"保存结果到文件时发生错误: {str(e)}")
        print(f"❌ 保存文件失败: {str(e)}")


if __name__ == '__main__':
    sys.exit(main())