#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
流量数据处理系统使用示例
演示如何使用TrafficDataManager进行数据采集和查询
"""

import asyncio
import json
from datetime import datetime
from traffic_data_manager import TrafficDataManager, DatabaseConfig


async def example_single_domain():
    """示例1: 处理单个域名"""
    print("="*60)
    print("示例1: 处理单个域名")
    print("="*60)
    
    # 创建数据库配置
    db_config = DatabaseConfig(
        host="localhost",
        port=5432,
        database="aistak_db",
        user="postgres",
        password="password"
    )
    
    # 创建管理器实例
    manager = TrafficDataManager(db_config)
    
    try:
        # 初始化数据库连接
        await manager.init_db_pool()
        
        # 处理域名
        domain = "klingai.com"
        result = await manager.process_domain_traffic(domain)
        
        print(f"处理结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        if result.get('success'):
            # 获取流量摘要
            tool_id = result.get('tool_id')
            summary = await manager.get_traffic_summary(tool_id)
            print(f"\n流量摘要: {json.dumps(summary, indent=2, ensure_ascii=False, default=str)}")
        
    except Exception as e:
        print(f"处理失败: {e}")
    finally:
        await manager.close_db_pool()


async def example_batch_domains():
    """示例2: 批量处理多个域名"""
    print("="*60)
    print("示例2: 批量处理多个域名")
    print("="*60)
    
    db_config = DatabaseConfig()
    manager = TrafficDataManager(db_config)
    
    try:
        await manager.init_db_pool()
        
        # 要处理的域名列表
        domains = [
            "chatgpt.com",
            "claude.ai",
            "midjourney.com"
        ]
        
        # 批量处理
        results = await manager.batch_process_domains(domains)
        
        print("批量处理结果:")
        for result in results:
            domain = result.get('domain')
            success = result.get('success', False)
            status = "✅ 成功" if success else "❌ 失败"
            print(f"  {domain}: {status}")
            
            if success:
                print(f"    数据点: {result.get('data_points', 0)}")
                print(f"    响应时间: {result.get('response_time_ms', 0)}ms")
        
    except Exception as e:
        print(f"批量处理失败: {e}")
    finally:
        await manager.close_db_pool()


async def example_query_data():
    """示例3: 查询已存储的数据"""
    print("="*60)
    print("示例3: 查询已存储的数据")
    print("="*60)
    
    db_config = DatabaseConfig()
    manager = TrafficDataManager(db_config)
    
    try:
        await manager.init_db_pool()
        
        # 查询特定工具的流量摘要
        tool_id = "klingai_com"  # 假设这个tool_id存在
        summary = await manager.get_traffic_summary(tool_id)
        
        if summary.get('overview'):
            overview = summary['overview']
            print(f"网站概览:")
            print(f"  域名: {overview.get('domain')}")
            print(f"  全球排名: #{overview.get('global_rank')}")
            print(f"  总访问量: {overview.get('total_visits_raw')}")
            print(f"  跳出率: {overview.get('bounce_rate')}%")
        
        if summary.get('trends'):
            print(f"\n访问量趋势:")
            for trend in summary['trends'][:3]:
                print(f"  {trend.get('period')}: {trend.get('visits_raw')}")
        
        if summary.get('sources'):
            print(f"\n流量来源:")
            for source in summary['sources']:
                print(f"  {source.get('source_type')}: {source.get('traffic_percent_raw')}")
        
    except Exception as e:
        print(f"查询数据失败: {e}")
    finally:
        await manager.close_db_pool()


async def example_custom_query():
    """示例4: 自定义数据库查询"""
    print("="*60)
    print("示例4: 自定义数据库查询")
    print("="*60)
    
    db_config = DatabaseConfig()
    manager = TrafficDataManager(db_config)
    
    try:
        await manager.init_db_pool()
        
        async with manager.db_pool.acquire() as conn:
            # 查询访问量最高的前10个网站
            top_sites = await conn.fetch("""
                SELECT tool_id, domain, total_visits, total_visits_raw, global_rank
                FROM traffic_site_overview 
                WHERE total_visits > 0
                ORDER BY total_visits DESC 
                LIMIT 10
            """)
            
            print("访问量最高的网站:")
            for i, site in enumerate(top_sites, 1):
                print(f"  {i}. {site['domain']} - {site['total_visits_raw']} (排名: #{site['global_rank']})")
            
            # 查询最近的数据提取日志
            recent_logs = await conn.fetch("""
                SELECT domain, extraction_status, data_points_extracted, response_time_ms, extracted_at
                FROM traffic_extraction_logs 
                ORDER BY extracted_at DESC 
                LIMIT 5
            """)
            
            print(f"\n最近的提取日志:")
            for log in recent_logs:
                status = "✅" if log['extraction_status'] == 'success' else "❌"
                print(f"  {status} {log['domain']} - {log['data_points_extracted']} 数据点 ({log['response_time_ms']}ms)")
        
    except Exception as e:
        print(f"自定义查询失败: {e}")
    finally:
        await manager.close_db_pool()


async def example_data_validation():
    """示例5: 数据验证和转换"""
    print("="*60)
    print("示例5: 数据验证和转换")
    print("="*60)
    
    manager = TrafficDataManager(DatabaseConfig())
    
    # 测试数据转换函数
    test_cases = [
        ("15.41M", "访问量转换"),
        ("1.2K", "访问量转换"),
        ("500", "访问量转换"),
        ("00:06:56", "时长转换"),
        ("34.31%", "百分比转换"),
        ("-5.20%", "百分比转换"),
        ("2024-03-26", "日期转换")
    ]
    
    print("数据转换测试:")
    for test_value, test_type in test_cases:
        if "访问量" in test_type:
            result = manager.convert_visits_to_number(test_value)
            print(f"  {test_value} -> {result:,} ({test_type})")
        elif "时长" in test_type:
            result = manager.convert_duration_to_seconds(test_value)
            print(f"  {test_value} -> {result} 秒 ({test_type})")
        elif "百分比" in test_type:
            result = manager.extract_percentage(test_value)
            print(f"  {test_value} -> {result} ({test_type})")
        elif "日期" in test_type:
            result = manager.parse_date(test_value)
            print(f"  {test_value} -> {result} ({test_type})")


async def main():
    """主函数 - 运行所有示例"""
    print("🚀 流量数据处理系统使用示例")
    print(f"运行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 运行示例（根据需要启用/禁用）
        await example_data_validation()
        
        # 注意: 以下示例需要数据库连接，请确保数据库配置正确
        # await example_single_domain()
        # await example_batch_domains()
        # await example_query_data()
        # await example_custom_query()
        
    except Exception as e:
        print(f"示例运行失败: {e}")
    
    print("\n✅ 示例运行完成")


if __name__ == "__main__":
    asyncio.run(main())
