#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
流量数据系统安装和部署脚本
"""

import os
import sys
import subprocess
import asyncio
import asyncpg
from pathlib import Path


def print_step(step_num, description):
    """打印步骤信息"""
    print(f"\n{'='*60}")
    print(f"步骤 {step_num}: {description}")
    print('='*60)


def run_command(command, description=""):
    """运行命令并处理错误"""
    try:
        print(f"执行: {command}")
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 命令执行失败: {e}")
        if e.stderr:
            print(f"错误信息: {e.stderr}")
        return False


def check_python_version():
    """检查Python版本"""
    print_step(1, "检查Python版本")
    
    version = sys.version_info
    print(f"当前Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("❌ 需要Python 3.7或更高版本")
        return False
    
    print("✅ Python版本符合要求")
    return True


def install_dependencies():
    """安装Python依赖"""
    print_step(2, "安装Python依赖")
    
    dependencies = [
        "asyncpg>=0.28.0",
        "requests>=2.28.0", 
        "beautifulsoup4>=4.11.0",
        "lxml>=4.9.0"
    ]
    
    print("安装依赖包:")
    for dep in dependencies:
        print(f"  - {dep}")
    
    command = f"{sys.executable} -m pip install " + " ".join(dependencies)
    
    if run_command(command):
        print("✅ 依赖安装成功")
        return True
    else:
        print("❌ 依赖安装失败")
        return False


def check_postgresql():
    """检查PostgreSQL连接"""
    print_step(3, "检查PostgreSQL连接")
    
    # 尝试连接数据库
    try:
        # 这里使用默认配置，实际使用时应该从配置文件读取
        print("尝试连接数据库...")
        print("使用默认配置: localhost:5432/aistak_db")
        print("如需修改，请编辑 config.py 文件")
        
        # 注意：这里只是检查，不实际连接
        print("⚠️ 请确保PostgreSQL服务正在运行")
        print("⚠️ 请确保数据库 'aistak_db' 已创建")
        print("⚠️ 请确保用户有适当的权限")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接检查失败: {e}")
        return False


def create_database_tables():
    """创建数据库表"""
    print_step(4, "创建数据库表")
    
    sql_file = "create_traffic_tables.sql"
    
    if not os.path.exists(sql_file):
        print(f"❌ SQL文件不存在: {sql_file}")
        return False
    
    print(f"找到SQL文件: {sql_file}")
    print("请手动执行以下命令创建数据库表:")
    print(f"  psql -d aistak_db -f {sql_file}")
    print("或者使用其他数据库管理工具执行SQL文件")
    
    return True


def create_directories():
    """创建必要的目录"""
    print_step(5, "创建目录结构")
    
    directories = [
        "logs",
        "data", 
        "json",
        "results"
    ]
    
    for directory in directories:
        try:
            Path(directory).mkdir(exist_ok=True)
            print(f"✅ 创建目录: {directory}")
        except Exception as e:
            print(f"❌ 创建目录失败 {directory}: {e}")
            return False
    
    return True


def create_config_file():
    """创建配置文件"""
    print_step(6, "配置文件检查")
    
    config_file = "config.py"
    
    if os.path.exists(config_file):
        print(f"✅ 配置文件已存在: {config_file}")
    else:
        print(f"❌ 配置文件不存在: {config_file}")
        return False
    
    print("请根据实际情况修改数据库配置:")
    print("  - 数据库主机地址")
    print("  - 数据库端口")
    print("  - 数据库名称")
    print("  - 用户名和密码")
    
    return True


def run_tests():
    """运行测试"""
    print_step(7, "运行系统测试")
    
    test_file = "test_system.py"
    
    if not os.path.exists(test_file):
        print(f"❌ 测试文件不存在: {test_file}")
        return False
    
    print("运行系统测试...")
    command = f"{sys.executable} {test_file}"
    
    if run_command(command):
        print("✅ 测试运行完成")
        return True
    else:
        print("❌ 测试运行失败")
        return False


def show_usage_examples():
    """显示使用示例"""
    print_step(8, "使用示例")
    
    examples = [
        ("处理单个域名", "python main_traffic_processor.py --domain klingai.com"),
        ("批量处理域名", "python main_traffic_processor.py --domains chatgpt.com claude.ai"),
        ("从文件处理", "python main_traffic_processor.py --file sample_domains.txt"),
        ("保存结果", "python main_traffic_processor.py --domain klingai.com --output result.json"),
        ("运行示例", "python example_usage.py"),
        ("系统测试", "python test_system.py")
    ]
    
    print("常用命令:")
    for description, command in examples:
        print(f"  {description}:")
        print(f"    {command}")
        print()


def main():
    """主安装函数"""
    print("🚀 流量数据系统安装程序")
    print("=" * 60)
    
    success_steps = 0
    total_steps = 8
    
    # 执行安装步骤
    steps = [
        check_python_version,
        install_dependencies,
        check_postgresql,
        create_database_tables,
        create_directories,
        create_config_file,
        # run_tests,  # 可选步骤
        show_usage_examples
    ]
    
    for step_func in steps:
        try:
            if step_func():
                success_steps += 1
            else:
                print(f"⚠️ 步骤失败，但继续执行...")
        except Exception as e:
            print(f"❌ 步骤执行异常: {e}")
    
    # 显示安装结果
    print("\n" + "=" * 60)
    print("安装完成")
    print("=" * 60)
    
    print(f"成功步骤: {success_steps}/{len(steps)}")
    
    if success_steps >= len(steps) - 1:  # 允许一个步骤失败
        print("🎉 系统安装基本完成!")
        print("\n下一步:")
        print("1. 确保PostgreSQL服务运行")
        print("2. 创建数据库表: psql -d aistak_db -f create_traffic_tables.sql")
        print("3. 修改config.py中的数据库配置")
        print("4. 运行测试: python test_system.py")
        print("5. 开始使用: python main_traffic_processor.py --domain example.com")
    else:
        print("⚠️ 安装过程中遇到问题，请检查错误信息")
        print("请参考 TRAFFIC_SYSTEM_README.md 文档进行手动配置")
    
    print("\n📚 文档和帮助:")
    print("  - 系统文档: TRAFFIC_SYSTEM_README.md")
    print("  - 使用示例: example_usage.py")
    print("  - 系统测试: test_system.py")
    print("  - 配置文件: config.py")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⚠️ 安装被用户中断")
    except Exception as e:
        print(f"\n💥 安装过程异常: {e}")
        sys.exit(1)
