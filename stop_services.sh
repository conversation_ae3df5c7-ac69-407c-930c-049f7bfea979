#!/bin/bash

# Aistak FastAPI 服务停止脚本
# 用于安全停止所有相关服务

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 停止进程
stop_process() {
    local process_name=$1
    local signal=${2:-TERM}
    
    local pids=$(pgrep -f "$process_name" 2>/dev/null || true)
    
    if [ -n "$pids" ]; then
        log_info "停止 $process_name 进程..."
        echo "$pids" | xargs kill -$signal 2>/dev/null || true
        
        # 等待进程停止
        sleep 3
        
        # 检查是否还在运行
        local remaining_pids=$(pgrep -f "$process_name" 2>/dev/null || true)
        if [ -n "$remaining_pids" ]; then
            log_warning "$process_name 进程未完全停止，使用KILL信号"
            echo "$remaining_pids" | xargs kill -KILL 2>/dev/null || true
            sleep 2
        fi
        
        # 最终检查
        if ! pgrep -f "$process_name" > /dev/null 2>&1; then
            log_success "$process_name 进程已停止"
        else
            log_error "$process_name 进程停止失败"
        fi
    else
        log_info "$process_name 进程未运行"
    fi
}

# 停止systemd服务
stop_systemd_service() {
    local service_name=$1
    
    if command -v systemctl &> /dev/null; then
        if systemctl is-active --quiet $service_name 2>/dev/null; then
            log_info "停止systemd服务: $service_name"
            sudo systemctl stop $service_name
            log_success "$service_name 服务已停止"
        else
            log_info "systemd服务 $service_name 未运行"
        fi
    fi
}

# 主函数
main() {
    echo "========================================"
    echo "    Aistak FastAPI 服务停止脚本"
    echo "========================================"
    echo
    
    # 停止FastAPI应用
    log_info "停止FastAPI应用..."
    
    # 尝试停止systemd服务
    stop_systemd_service "aistak-fastapi"
    
    # 停止可能的手动启动进程
    stop_process "uvicorn main:app"
    stop_process "python.*main.py"
    
    # 停止AI内容生成任务
    log_info "停止AI内容生成任务..."
    stop_process "generate_descriptions"
    
    # 停止爬虫任务
    log_info "停止爬虫任务..."
    stop_process "crawler"
    
    # 停止Ollama服务
    log_info "停止Ollama服务..."
    
    # 尝试停止systemd服务
    stop_systemd_service "ollama"
    
    # 停止可能的手动启动进程
    stop_process "ollama serve"
    
    # 检查端口占用情况
    log_info "检查端口占用情况..."
    
    if lsof -i :8000 &> /dev/null; then
        log_warning "端口8000仍被占用"
        lsof -i :8000
    else
        log_success "端口8000已释放"
    fi
    
    if lsof -i :11434 &> /dev/null; then
        log_warning "端口11434仍被占用"
        lsof -i :11434
    else
        log_success "端口11434已释放"
    fi
    
    # 清理临时文件（可选）
    read -p "是否清理日志文件？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "清理日志文件..."
        rm -f *.log
        log_success "日志文件已清理"
    fi
    
    echo
    echo "========================================"
    echo "           停止完成"
    echo "========================================"
    
    # 最终状态检查
    log_info "最终状态检查..."
    
    if ! pgrep -f "uvicorn main:app" > /dev/null && ! pgrep -f "python.*main.py" > /dev/null; then
        log_success "FastAPI应用: 已停止"
    else
        log_error "FastAPI应用: 仍在运行"
    fi
    
    if ! pgrep -f "ollama serve" > /dev/null; then
        log_success "Ollama服务: 已停止"
    else
        log_error "Ollama服务: 仍在运行"
    fi
    
    if ! pgrep -f "generate_descriptions" > /dev/null; then
        log_success "AI生成任务: 已停止"
    else
        log_error "AI生成任务: 仍在运行"
    fi
    
    echo
    log_success "所有服务已停止！"
    echo
    echo "重新启动服务: ./start_services.sh"
    echo "查看系统状态: ./system_monitor.sh"
}

# 处理中断信号
trap 'log_warning "停止过程被中断"; exit 1' INT TERM

# 确认操作
echo "即将停止所有Aistak FastAPI相关服务"
read -p "确认继续？(y/N): " -n 1 -r
echo

if [[ $REPLY =~ ^[Yy]$ ]]; then
    main "$@"
else
    log_info "操作已取消"
    exit 0
fi
