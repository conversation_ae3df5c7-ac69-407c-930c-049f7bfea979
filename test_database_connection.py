#!/usr/bin/env python3
"""
数据库连接测试脚本

用于测试数据库连接配置是否正确，包括超时设置和连接池配置
"""

import os
import sys
import time
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from sqlalchemy import text
from sqlalchemy.exc import OperationalError, DisconnectionError
from app.core.database import engine, SessionLocal
from app.core.database_config import (
    create_robust_database_session,
    execute_with_retry,
    DEFAULT_TIMEOUT_CONFIG
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s'
)
logger = logging.getLogger(__name__)


def test_basic_connection():
    """测试基本数据库连接"""
    logger.info("=== 测试基本数据库连接 ===")
    
    try:
        with engine.connect() as conn:
            result = conn.execute(text("SELECT version()"))
            version = result.fetchone()[0]
            logger.info(f"✅ 数据库连接成功")
            logger.info(f"数据库版本: {version}")
            return True
            
    except Exception as e:
        logger.error(f"❌ 数据库连接失败: {str(e)}")
        return False


def test_session_connection():
    """测试会话连接"""
    logger.info("=== 测试会话连接 ===")
    
    try:
        db = SessionLocal()
        result = db.execute(text("SELECT current_database(), current_user"))
        db_name, user = result.fetchone()
        logger.info(f"✅ 会话连接成功")
        logger.info(f"数据库: {db_name}, 用户: {user}")
        db.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ 会话连接失败: {str(e)}")
        return False


def test_robust_connection():
    """测试健壮连接"""
    logger.info("=== 测试健壮连接 ===")
    
    try:
        db = create_robust_database_session()
        if db:
            result = db.execute(text("SELECT 'robust connection test'"))
            test_result = result.fetchone()[0]
            logger.info(f"✅ 健壮连接成功: {test_result}")
            db.close()
            return True
        else:
            logger.error("❌ 健壮连接返回None")
            return False
            
    except Exception as e:
        logger.error(f"❌ 健壮连接失败: {str(e)}")
        return False


def test_timeout_settings():
    """测试超时设置"""
    logger.info("=== 测试超时设置 ===")
    
    try:
        db = SessionLocal()
        
        # 检查当前超时设置
        timeout_queries = [
            ("statement_timeout", "SHOW statement_timeout"),
            ("idle_in_transaction_session_timeout", "SHOW idle_in_transaction_session_timeout"),
        ]
        
        for setting_name, query in timeout_queries:
            result = db.execute(text(query))
            value = result.fetchone()[0]
            logger.info(f"✅ {setting_name}: {value}")
        
        db.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ 超时设置检查失败: {str(e)}")
        return False


def test_long_running_query():
    """测试长时间运行的查询"""
    logger.info("=== 测试长时间运行的查询 ===")
    
    try:
        db = SessionLocal()
        
        # 执行一个需要几秒钟的查询
        logger.info("执行长时间查询 (pg_sleep(10))...")
        start_time = time.time()
        
        result = db.execute(text("SELECT pg_sleep(10), 'long query completed'"))
        message = result.fetchone()[1]
        
        end_time = time.time()
        duration = end_time - start_time
        
        logger.info(f"✅ 长时间查询成功: {message}")
        logger.info(f"执行时间: {duration:.2f} 秒")
        
        db.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ 长时间查询失败: {str(e)}")
        return False


def test_connection_pool():
    """测试连接池"""
    logger.info("=== 测试连接池 ===")
    
    try:
        connections = []
        
        # 创建多个连接
        for i in range(5):
            db = SessionLocal()
            result = db.execute(text(f"SELECT {i+1} as connection_id"))
            conn_id = result.fetchone()[0]
            logger.info(f"连接 {i+1}: ID={conn_id}")
            connections.append(db)
        
        # 关闭所有连接
        for db in connections:
            db.close()
        
        logger.info("✅ 连接池测试成功")
        return True
        
    except Exception as e:
        logger.error(f"❌ 连接池测试失败: {str(e)}")
        return False


def test_retry_mechanism():
    """测试重试机制"""
    logger.info("=== 测试重试机制 ===")
    
    try:
        db = create_robust_database_session()
        
        def test_operation():
            return db.execute(text("SELECT 'retry test successful'")).fetchone()[0]
        
        result = execute_with_retry(db, test_operation)
        logger.info(f"✅ 重试机制测试成功: {result}")
        
        db.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ 重试机制测试失败: {str(e)}")
        return False


def test_transaction_handling():
    """测试事务处理"""
    logger.info("=== 测试事务处理 ===")
    
    try:
        db = SessionLocal()
        
        # 开始事务
        db.begin()
        
        # 执行一些操作
        db.execute(text("SELECT 1"))
        logger.info("事务中执行查询成功")
        
        # 提交事务
        db.commit()
        logger.info("✅ 事务提交成功")
        
        db.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ 事务处理失败: {str(e)}")
        try:
            db.rollback()
        except:
            pass
        return False


def main():
    """主函数"""
    logger.info("开始数据库连接测试...")
    logger.info(f"超时配置: {DEFAULT_TIMEOUT_CONFIG}")
    
    tests = [
        ("基本连接", test_basic_connection),
        ("会话连接", test_session_connection),
        ("健壮连接", test_robust_connection),
        ("超时设置", test_timeout_settings),
        ("连接池", test_connection_pool),
        ("重试机制", test_retry_mechanism),
        ("事务处理", test_transaction_handling),
    ]
    
    # 询问是否执行长时间查询测试
    print("\n是否执行长时间查询测试 (10秒)? [y/N]: ", end="")
    response = input().strip().lower()
    if response in ['y', 'yes']:
        tests.append(("长时间查询", test_long_running_query))
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            logger.error(f"测试 '{test_name}' 发生异常: {str(e)}")
            failed += 1
    
    logger.info(f"\n{'='*50}")
    logger.info("测试总结:")
    logger.info(f"✅ 通过: {passed}")
    logger.info(f"❌ 失败: {failed}")
    logger.info(f"总计: {passed + failed}")
    
    if failed == 0:
        logger.info("🎉 所有测试通过！数据库配置正常。")
        return 0
    else:
        logger.warning("⚠️ 部分测试失败，请检查数据库配置。")
        return 1


if __name__ == '__main__':
    sys.exit(main())
