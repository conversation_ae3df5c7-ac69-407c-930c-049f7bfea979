# Aistak_FastAPI 项目

这是一个基于FastAPI框架的Python Web应用项目。

## 项目结构

```
├── main.py              # 应用入口文件
├── requirements.txt     # 项目依赖
├── test_database.py     # 数据库连接测试脚本
├── app/                 # 应用核心代码
│   ├── core/           # 核心配置
│   │   ├── config.py   # 应用配置
│   │   └── database.py # 数据库配置
│   ├── models/         # 数据模型
│   │   └── user.py     # 用户模型
│   └── routers/        # 路由处理
│       ├── users.py    # 用户相关路由
│       └── health.py   # 健康检查路由
├── tests/              # 测试文件
│   ├── test_main.py    # 主要测试用例
│   └── test_database.py # 数据库测试用例
└── .env.example        # 环境变量示例
```

## 🚀 快速开始

### 一键启动（推荐）

```bash
# 一键启动所有服务（FastAPI + Ollama）
./start_services.sh

# 查看系统状态
./system_monitor.sh

# 快速测试价格分析功能
python generate_price.py --test

# 停止所有服务
./stop_services.sh
```

### 手动启动

#### 使用 uv (推荐)

1. 安装依赖：
```bash
uv sync --extra dev
```

2. 复制环境变量文件：
```bash
cp .env.example .env
```

3. 配置数据库连接：
编辑 `.env` 文件，设置数据库连接信息：
```
DATABASE_URL=postgresql://username:password@host:port/database_name?sslmode=prefer
```

4. 测试数据库连接：
```bash
make test-db
# 或
uv run python test_database.py
```

5. 启动应用：

#### 开发模式（前台运行）
```bash
# 使用 uv（推荐）
uv run uvicorn main:app --reload

# 或使用 Makefile
make init  # 初始化项目
make run   # 启动开发服务器
```

#### 生产模式（后台运行）
```bash
# 使用 nohup 后台运行
nohup uv run uvicorn main:app --host 0.0.0.0 --port 8000 > app.log 2>&1 &

# 使用 systemd 服务（推荐生产环境）
sudo systemctl start aistak-fastapi
sudo systemctl enable aistak-fastapi  # 开机自启

# 使用 PM2 进程管理器
pm2 start "uv run uvicorn main:app --host 0.0.0.0 --port 8000" --name aistak-fastapi
pm2 save  # 保存配置
pm2 startup  # 设置开机自启
```

#### 检测运行状态
```bash
# 检查进程是否运行
ps aux | grep uvicorn
pgrep -f "uvicorn main:app"

# 检查端口是否被占用
lsof -i :8000
netstat -tlnp | grep :8000

# 健康检查（应用启动后）
curl http://localhost:8000/
curl http://localhost:8000/api/v1/health/

# 检查应用日志
tail -f app.log  # 如果使用 nohup
pm2 logs aistak-fastapi  # 如果使用 PM2
journalctl -u aistak-fastapi -f  # 如果使用 systemd
```

#### 停止应用
```bash
# 如果使用 nohup
pkill -f "uvicorn main:app"

# 如果使用 PM2
pm2 stop aistak-fastapi
pm2 delete aistak-fastapi

# 如果使用 systemd
sudo systemctl stop aistak-fastapi
```

### 使用传统方式

1. 安装依赖：
```bash
pip install -r requirements.txt
```

2. 启动应用：
```bash
python main.py
```

## 📚 文档导航

### API文档
- **Swagger UI**: http://localhost:8000/docs (服务启动后访问)
- **ReDoc**: http://localhost:8000/redoc (服务启动后访问)

### 项目文档
- 📖 **[开发指南](docs/DEVELOPMENT_GUIDE.md)** - 完整的开发文档，包含架构、入口点、路由、数据库等
- 🏗️ **[架构概览](docs/ARCHITECTURE_OVERVIEW.md)** - 系统架构图和组件关系
- 🌐 **[API参考](docs/API_REFERENCE.md)** - 详细的API接口文档和使用示例
- 🚀 **[快速开始](docs/QUICK_START.md)** - 一分钟快速启动指南
- 🤖 **[AI内容生成](docs/DESCRIPTION_GENERATION_README.md)** - AI内容生成功能详细说明

### 管理脚本
- 🚀 **启动服务**: `./start_services.sh`
- 🛑 **停止服务**: `./stop_services.sh`
- 📊 **系统监控**: `./system_monitor.sh`

## 🕷️ 爬虫系统和AI内容生成

### 环境准备

#### 1. 安装和启动Ollama服务
```bash
# macOS
brew install ollama

# Linux
curl -fsSL https://ollama.com/install.sh | sh

# 启动Ollama服务（后台运行）
nohup ollama serve > ollama.log 2>&1 &

# 下载推荐模型
ollama pull gemma3:latest
ollama pull deepseek-r1:latest
ollama pull deepseek-r1:latest
```

#### 2. 检测Ollama服务状态
```bash
# 检查Ollama进程
ps aux | grep ollama
pgrep -f ollama

# 检查Ollama API
curl http://localhost:11434/api/tags

# 列出已下载的模型
ollama list

# 检查模型是否可用
curl -X POST http://localhost:11434/api/generate \
  -H "Content-Type: application/json" \
  -d '{"model": "gemma3:latest", "prompt": "Hello", "stream": false}'
```

### AI内容生成

#### 1. 后台批量生成
```bash
# 后台运行大批量生成任务
nohup python generate_descriptions.py --locale en --limit 100 --fields long_description > generation.log 2>&1 &

# 使用不同模型后台生成
nohup python generate_descriptions.py --model deepseek-r1:latest --limit 50 --fields usage_instructions,pricing_details > generation_advanced.log 2>&1 &

# 定时执行生成任务（使用cron）
# 编辑crontab
crontab -e

# 添加定时任务（每天凌晨2点执行）
0 2 * * * cd /path/to/aistak_fastapi && python generate_descriptions.py --limit 50 --fields long_description >> /var/log/aistak_generation.log 2>&1

# 每周日凌晨3点生成其他字段
0 3 * * 0 cd /path/to/aistak_fastapi && python generate_descriptions.py --limit 20 --fields usage_instructions,pricing_details >> /var/log/aistak_generation.log 2>&1
```

#### 2. 检测生成任务状态
```bash
# 检查生成进程
ps aux | grep generate_descriptions
pgrep -f generate_descriptions

# 查看生成日志
tail -f generation.log
tail -f generate_descriptions.log

# 检查生成进度和统计
python generate_descriptions.py --status

# 通过API检查状态
curl "http://localhost:8000/api/v1/description-generation/status?locale=en"

# 检查数据库中的完成情况
curl "http://localhost:8000/api/v1/description-generation/health"
```

#### 3. 监控和管理生成任务
```bash
# 创建监控脚本
cat > monitor_generation.sh << 'EOF'
#!/bin/bash
LOG_FILE="/var/log/aistak_generation_monitor.log"
echo "$(date): 开始检查生成任务状态" >> $LOG_FILE

# 检查Ollama服务
if ! curl -s http://localhost:11434/api/tags > /dev/null; then
    echo "$(date): Ollama服务异常，尝试重启" >> $LOG_FILE
    nohup ollama serve > ollama.log 2>&1 &
fi

# 检查生成进度
STATUS=$(python generate_descriptions.py --status 2>/dev/null | grep "完成率" | head -1)
echo "$(date): 生成状态 - $STATUS" >> $LOG_FILE

# 如果没有运行中的生成任务，启动新的
if ! pgrep -f generate_descriptions > /dev/null; then
    echo "$(date): 启动新的生成任务" >> $LOG_FILE
    nohup python generate_descriptions.py --limit 10 --fields long_description >> generation.log 2>&1 &
fi
EOF

chmod +x monitor_generation.sh

# 设置定时监控（每小时检查一次）
echo "0 * * * * /path/to/aistak_fastapi/monitor_generation.sh" | crontab -
```

### 爬虫系统

#### 1. 后台运行爬虫任务
```bash
# 通过API后台触发批量爬取
curl -X POST "http://localhost:8000/api/v1/crawler/batch-crawl" \
  -H "Content-Type: application/json" \
  -d '{
    "urls": ["https://tool1.com", "https://tool2.com"],
    "batch_size": 10
  }' > crawler_result.json &

# 如果有CLI爬虫工具
nohup python -m app.cli.crawler --batch --file urls.txt > crawler.log 2>&1 &
```

#### 2. 检测爬虫状态
```bash
# 检查爬虫进程
ps aux | grep crawler
curl "http://localhost:8000/api/v1/crawler/status"

# 查看爬虫日志
tail -f crawler.log
tail -f app.log | grep crawler

# 检查数据库中新增的工具数量
curl "http://localhost:8000/api/v1/health/database/info"
```

### 系统整体监控

#### 1. 创建系统监控脚本
```bash
cat > system_monitor.sh << 'EOF'
#!/bin/bash
echo "=== Aistak FastAPI 系统状态监控 ==="
echo "时间: $(date)"
echo

# 检查主应用
echo "1. FastAPI应用状态:"
if curl -s http://localhost:8000/ > /dev/null; then
    echo "   ✅ FastAPI应用运行正常"
else
    echo "   ❌ FastAPI应用异常"
fi

# 检查数据库
echo "2. 数据库连接状态:"
DB_STATUS=$(curl -s http://localhost:8000/api/v1/health/database | jq -r '.status' 2>/dev/null)
if [ "$DB_STATUS" = "healthy" ]; then
    echo "   ✅ 数据库连接正常"
else
    echo "   ❌ 数据库连接异常"
fi

# 检查Ollama服务
echo "3. Ollama服务状态:"
if curl -s http://localhost:11434/api/tags > /dev/null; then
    echo "   ✅ Ollama服务运行正常"
    MODEL_COUNT=$(curl -s http://localhost:11434/api/tags | jq '.models | length' 2>/dev/null)
    echo "   📊 可用模型数量: $MODEL_COUNT"
else
    echo "   ❌ Ollama服务异常"
fi

# 检查生成任务
echo "4. AI内容生成状态:"
if pgrep -f generate_descriptions > /dev/null; then
    echo "   🔄 内容生成任务运行中"
else
    echo "   ⏸️  无运行中的内容生成任务"
fi

# 检查价格分析任务
echo "5. 价格分析状态:"
if pgrep -f generate_price > /dev/null; then
    echo "   🔄 价格分析任务运行中"
else
    echo "   ⏸️  无运行中的价格分析任务"
fi

# 检查系统资源
echo "6. 系统资源使用:"
echo "   💾 内存使用: $(free -h | awk 'NR==2{printf "%.1f%%", $3*100/$2}')"
echo "   💿 磁盘使用: $(df -h / | awk 'NR==2{print $5}')"
echo "   🖥️  CPU负载: $(uptime | awk -F'load average:' '{print $2}')"

echo
echo "=== 监控完成 ==="
EOF

chmod +x system_monitor.sh

# 运行监控
./system_monitor.sh

# 设置定时监控（每30分钟）
echo "*/30 * * * * /path/to/aistak_fastapi/system_monitor.sh >> /var/log/aistak_monitor.log" | crontab -
```

#### 2. 日志管理
```bash
# 创建日志轮转配置
sudo tee /etc/logrotate.d/aistak-fastapi << 'EOF'
/path/to/aistak_fastapi/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 user user
    postrotate
        # 重启应用以重新打开日志文件（如果需要）
        # systemctl reload aistak-fastapi
    endscript
}
EOF

# 手动执行日志轮转测试
sudo logrotate -f /etc/logrotate.d/aistak-fastapi
```

## 生产环境部署

### 创建systemd服务

#### 1. 创建FastAPI服务配置
```bash
sudo tee /etc/systemd/system/aistak-fastapi.service << 'EOF'
[Unit]
Description=Aistak FastAPI Application
After=network.target postgresql.service

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory=/path/to/aistak_fastapi
Environment=PATH=/path/to/aistak_fastapi/.venv/bin
ExecStart=/path/to/aistak_fastapi/.venv/bin/uvicorn main:app --host 0.0.0.0 --port 8000
Restart=always
RestartSec=3
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF

# 启用和启动服务
sudo systemctl daemon-reload
sudo systemctl enable aistak-fastapi
sudo systemctl start aistak-fastapi
```

#### 2. 创建Ollama服务配置
```bash
sudo tee /etc/systemd/system/ollama.service << 'EOF'
[Unit]
Description=Ollama Service
After=network.target

[Service]
Type=simple
User=ollama
Group=ollama
ExecStart=/usr/local/bin/ollama serve
Restart=always
RestartSec=3
Environment=OLLAMA_HOST=0.0.0.0:11434
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF

# 创建ollama用户
sudo useradd -r -s /bin/false ollama

# 启用和启动服务
sudo systemctl daemon-reload
sudo systemctl enable ollama
sudo systemctl start ollama
```

#### 3. 创建AI内容生成定时服务
```bash
# 创建服务文件
sudo tee /etc/systemd/system/aistak-generation.service << 'EOF'
[Unit]
Description=Aistak AI Content Generation
After=network.target aistak-fastapi.service ollama.service

[Service]
Type=oneshot
User=www-data
Group=www-data
WorkingDirectory=/path/to/aistak_fastapi
Environment=PATH=/path/to/aistak_fastapi/.venv/bin
ExecStart=/path/to/aistak_fastapi/.venv/bin/python generate_descriptions.py --limit 20 --fields long_description
StandardOutput=journal
StandardError=journal
EOF

# 创建定时器文件
sudo tee /etc/systemd/system/aistak-generation.timer << 'EOF'
[Unit]
Description=Run Aistak AI Content Generation daily
Requires=aistak-generation.service

[Timer]
OnCalendar=daily
Persistent=true

[Install]
WantedBy=timers.target
EOF

# 启用定时器
sudo systemctl daemon-reload
sudo systemctl enable aistak-generation.timer
sudo systemctl start aistak-generation.timer
```

#### 4. 服务管理命令
```bash
# 查看服务状态
sudo systemctl status aistak-fastapi
sudo systemctl status ollama
sudo systemctl status aistak-generation.timer

# 查看服务日志
sudo journalctl -u aistak-fastapi -f
sudo journalctl -u ollama -f
sudo journalctl -u aistak-generation -f

# 重启服务
sudo systemctl restart aistak-fastapi
sudo systemctl restart ollama

# 停止服务
sudo systemctl stop aistak-fastapi
sudo systemctl stop ollama

# 查看定时器状态
sudo systemctl list-timers aistak-generation.timer
```

## 开发命令

```bash
# 运行测试
uv run pytest tests/
# 或
make test

# 代码格式化
uv run ruff format .
# 或
make format

# 代码检查
uv run ruff check .
# 或
make lint

# 修复代码问题
make fix

# 测试数据库连接
make test-db

# 运行数据库相关测试
make test-db-unit
```

## API端点

### 基础端点
- `GET /` - 根路径

### 健康检查端点
- `GET /api/v1/health/` - 基本健康检查
- `GET /api/v1/health/database` - 数据库连接健康检查
- `GET /api/v1/health/database/info` - 获取数据库详细信息

### 用户管理端点
- `GET /api/v1/users/` - 获取所有用户
- `POST /api/v1/users/` - 创建新用户
- `GET /api/v1/users/{user_id}` - 获取指定用户

### 爬虫系统端点
- `POST /api/v1/crawler/crawl` - 爬取单个工具
- `POST /api/v1/crawler/batch-crawl` - 批量爬取工具
- `GET /api/v1/crawler/status` - 查看爬虫状态

### AI内容生成端点
- `POST /api/v1/description-generation/generate` - 批量生成工具描述
- `GET /api/v1/description-generation/status` - 查看生成状态统计
- `GET /api/v1/description-generation/models` - 列出可用模型
- `GET /api/v1/description-generation/health` - 健康检查
- `POST /api/v1/description-generation/generate-single` - 为单个工具生成描述

### 调度器端点
- `GET /api/v1/scheduler/status` - 查看调度器状态
- `GET /api/v1/scheduler/jobs` - 查看所有定时任务
- `POST /api/v1/scheduler/jobs` - 创建新的定时任务
- `DELETE /api/v1/scheduler/jobs/{job_id}` - 删除定时任务

### 价格分析端点
- `POST /api/v1/pricing/analyze` - 分析单个域名价格
- `POST /api/v1/pricing/batch-analyze` - 批量分析价格
- `GET /api/v1/pricing/status` - 查看价格分析状态
- `GET /api/v1/pricing/statistics` - 获取价格分析统计信息

## 完整使用示例

### 1. 系统启动检查流程
```bash
# 1. 启动所有服务
sudo systemctl start aistak-fastapi
sudo systemctl start ollama

# 2. 等待服务启动（约10-30秒）
sleep 30

# 3. 检查服务状态
curl http://localhost:8000/api/v1/health/
curl http://localhost:11434/api/tags

# 4. 运行系统监控
./system_monitor.sh
```

### 2. 日常维护流程
```bash
# 每日维护脚本
cat > daily_maintenance.sh << 'EOF'
#!/bin/bash
echo "=== $(date): 开始日常维护 ==="

# 检查系统状态
./system_monitor.sh

# 检查生成进度
echo "当前生成进度:"
python generate_descriptions.py --status

# 清理旧日志（保留最近7天）
find . -name "*.log" -mtime +7 -delete

# 检查磁盘空间
df -h

echo "=== $(date): 日常维护完成 ==="
EOF

chmod +x daily_maintenance.sh

# 设置每日执行
echo "0 6 * * * /path/to/aistak_fastapi/daily_maintenance.sh >> /var/log/aistak_daily.log 2>&1" | crontab -
```

### 3. 紧急故障处理
```bash
# 创建故障恢复脚本
cat > emergency_recovery.sh << 'EOF'
#!/bin/bash
echo "=== 紧急故障恢复脚本 ==="

# 重启所有服务
echo "重启FastAPI服务..."
sudo systemctl restart aistak-fastapi
sleep 10

echo "重启Ollama服务..."
sudo systemctl restart ollama
sleep 10

# 检查服务状态
echo "检查服务状态..."
if curl -s http://localhost:8000/api/v1/health/ > /dev/null; then
    echo "✅ FastAPI服务恢复正常"
else
    echo "❌ FastAPI服务仍然异常"
fi

if curl -s http://localhost:11434/api/tags > /dev/null; then
    echo "✅ Ollama服务恢复正常"
else
    echo "❌ Ollama服务仍然异常"
fi

# 清理可能的僵尸进程
pkill -f "generate_descriptions"
pkill -f "uvicorn"

echo "=== 故障恢复完成 ==="
EOF

chmod +x emergency_recovery.sh
```

### 4. 性能监控和优化
```bash
# 创建性能监控脚本
cat > performance_monitor.sh << 'EOF'
#!/bin/bash
LOG_FILE="/var/log/aistak_performance.log"

echo "$(date): 性能监控开始" >> $LOG_FILE

# 检查API响应时间
RESPONSE_TIME=$(curl -o /dev/null -s -w '%{time_total}' http://localhost:8000/api/v1/health/)
echo "$(date): API响应时间: ${RESPONSE_TIME}s" >> $LOG_FILE

# 检查数据库连接数
DB_CONNECTIONS=$(curl -s http://localhost:8000/api/v1/health/database/info | jq -r '.active_connections' 2>/dev/null)
echo "$(date): 数据库活跃连接数: $DB_CONNECTIONS" >> $LOG_FILE

# 检查内存使用
MEMORY_USAGE=$(free | awk 'NR==2{printf "%.2f%%", $3*100/$2}')
echo "$(date): 内存使用率: $MEMORY_USAGE" >> $LOG_FILE

# 检查CPU使用
CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}')
echo "$(date): CPU使用率: ${CPU_USAGE}%" >> $LOG_FILE

# 如果响应时间过长，记录警告
if (( $(echo "$RESPONSE_TIME > 2.0" | bc -l) )); then
    echo "$(date): 警告 - API响应时间过长: ${RESPONSE_TIME}s" >> $LOG_FILE
fi

echo "$(date): 性能监控完成" >> $LOG_FILE
EOF

chmod +x performance_monitor.sh

# 每5分钟监控一次
echo "*/5 * * * * /path/to/aistak_fastapi/performance_monitor.sh" | crontab -
```

## 🛠️ 管理脚本

项目提供了三个核心管理脚本，简化日常操作：

### 1. 启动服务脚本 (`start_services.sh`)

**功能**: 一键启动所有相关服务
```bash
./start_services.sh
```

**执行流程**:
- ✅ 检查必要命令和环境
- ✅ 启动Ollama服务（如果未运行）
- ✅ 下载必要的AI模型
- ✅ 启动FastAPI应用
- ✅ 运行健康检查
- ✅ 显示服务状态和快速操作命令

**检测功能**:
- 自动检测服务是否已运行，避免重复启动
- 智能选择启动方式（systemd优先，后台进程备用）
- 等待服务完全启动后再进行下一步
- 提供详细的启动日志和状态反馈

### 2. 停止服务脚本 (`stop_services.sh`)

**功能**: 安全停止所有相关服务
```bash
./stop_services.sh
```

**执行流程**:
- ⚠️ 确认操作（防止误操作）
- 🛑 停止FastAPI应用
- 🛑 停止AI内容生成任务
- 🛑 停止爬虫任务
- 🛑 停止Ollama服务
- 🔍 检查端口释放情况
- 🧹 可选清理日志文件

**安全特性**:
- 优雅停止（TERM信号）+ 强制停止（KILL信号）
- 支持systemd服务和手动进程
- 检查端口释放状态
- 最终状态验证

### 3. 系统监控脚本 (`system_monitor.sh`)

**功能**: 全面的系统状态监控
```bash
# 单次监控
./system_monitor.sh

# 持续监控（每30秒刷新）
./system_monitor.sh --watch
```

**监控内容**:
- 🔍 **服务状态**: FastAPI、数据库、Ollama服务
- 📊 **性能指标**: API响应时间、数据库连接数
- 💻 **系统资源**: CPU、内存、磁盘使用情况
- 🔄 **进程状态**: 各服务进程运行情况
- 🌐 **网络状态**: 端口监听、连接数统计
- 🤖 **AI生成状态**: 各字段完成进度
- 📝 **日志状态**: 日志文件大小和修改时间

**特色功能**:
- 彩色输出，状态一目了然
- 实时响应时间测试
- 智能资源使用分析
- 快速操作命令提示

### 使用场景示例

#### 日常开发
```bash
# 开始工作
./start_services.sh

# 检查状态
./system_monitor.sh

# 生成内容
python generate_descriptions.py --limit 5

# 结束工作
./stop_services.sh
```

#### 生产部署
```bash
# 部署后启动
./start_services.sh

# 设置监控
./system_monitor.sh --watch &

# 或设置定时监控
echo "*/10 * * * * /path/to/aistak_fastapi/system_monitor.sh >> /var/log/aistak_monitor.log" | crontab -
```

#### 故障排查
```bash
# 查看详细状态
./system_monitor.sh

# 重启服务
./stop_services.sh
./start_services.sh

# 查看日志
tail -f app.log
tail -f ollama.log
```

#### 批量处理
```bash
# 启动服务
./start_services.sh

# 后台运行大批量任务
nohup python generate_descriptions.py --limit 100 --fields long_description > batch.log 2>&1 &

# 监控进度
watch -n 30 './system_monitor.sh | grep -A 10 "AI内容生成状态"'
```

## 数据库配置

项目使用 PostgreSQL 数据库，配置信息在 `.env` 文件中：

```env
DATABASE_URL=postgresql://username:password@host:port/database_name?sslmode=prefer
```

### 数据库功能
- 自动连接池管理
- 连接健康检查
- 启动时自动测试连接
- 详细的错误日志记录

### 测试数据库连接
```bash
# 使用独立测试脚本
python test_database.py

# 使用 Makefile
make test-db

# 运行单元测试
make test-db-unit
```



## 🏷️ 域名价格分析系统

### 环境准备

确保Ollama服务正在运行并已下载相关模型：
```bash
# 检查Ollama服务状态
curl http://localhost:11434/api/tags

# 下载推荐的价格分析模型
ollama pull gemma3:latest
ollama pull deepseek-r1:latest
ollama pull deepseek-r1:latest
```

### 基本使用

#### 1. 分析单个域名价格
```bash
# 分析单个域名（英文结果）
python generate_price.py --domain stripe.com --locale en

# 使用特定模型分析
python generate_price.py --domain notion.so --model deepseek-r1:latest --locale en

# 保存结果到文件
python generate_price.py --domain github.com --locale en --output pricing_result.json

# 启用详细日志
python generate_price.py --domain figma.com --locale en --verbose
```

#### 2. 批量分析工具价格
```bash
# 批量分析（默认10个工具）
python generate_price.py --locale en --limit 5

# 批量分析并更新数据库
python generate_price.py --locale en --limit 10 --update-db

# 干运行模式（仅显示将要处理的工具）
python generate_price.py --dry-run --locale en --limit 20

# 中文环境批量分析
python generate_price.py --locale zh --limit 5 --update-db
```

#### 3. 测试和状态检查
```bash
# 测试系统功能
python generate_price.py --test --model gemma3:latest

# 测试数据库更新功能
python generate_price.py --test-db-update --locale en

# 检查服务状态
python generate_price.py --status

# 列出可用的AI模型
python generate_price.py --list-models
```

### 高级功能

#### 1. 后台批量处理
```bash
# 后台运行大批量价格分析
nohup python generate_price.py --locale en --limit 50 --update-db > price_analysis.log 2>&1 &

# 使用不同模型后台分析
nohup python generate_price.py --model deepseek-r1:latest --locale en --limit 30 --update-db > price_analysis_advanced.log 2>&1 &

# 检查后台任务状态
ps aux | grep generate_price
tail -f price_analysis.log
```

#### 2. 定时价格更新
```bash
# 编辑crontab
crontab -e

# 添加定时任务（每天凌晨3点更新价格信息）
0 3 * * * cd /path/to/aistak_fastapi && python generate_price.py --locale en --limit 20 --update-db >> /var/log/aistak_pricing.log 2>&1

# 每周日凌晨4点进行大批量更新
0 4 * * 0 cd /path/to/aistak_fastapi && python generate_price.py --locale en --limit 100 --update-db >> /var/log/aistak_pricing.log 2>&1
```

#### 3. 监控价格分析任务
```bash
# 创建价格分析监控脚本
cat > monitor_pricing.sh << 'EOF'
#!/bin/bash
LOG_FILE="/var/log/aistak_pricing_monitor.log"
echo "$(date): 开始检查价格分析任务状态" >> $LOG_FILE

# 检查Ollama服务
if ! curl -s http://localhost:11434/api/tags > /dev/null; then
    echo "$(date): Ollama服务异常，尝试重启" >> $LOG_FILE
    nohup ollama serve > ollama.log 2>&1 &
fi

# 检查数据库中pricing_details为空的记录数量
EMPTY_COUNT=$(python -c "
from app.core.database import SessionLocal
from app.models.tool import ToolTranslation
db = SessionLocal()
count = db.query(ToolTranslation).filter(ToolTranslation.pricing_details.is_(None)).count()
print(count)
db.close()
")

echo "$(date): 待分析价格的工具数量: $EMPTY_COUNT" >> $LOG_FILE

# 如果有待处理的记录且没有运行中的分析任务，启动新的
if [ "$EMPTY_COUNT" -gt 0 ] && ! pgrep -f "generate_price.py" > /dev/null; then
    echo "$(date): 启动新的价格分析任务" >> $LOG_FILE
    nohup python generate_price.py --locale en --limit 10 --update-db >> price_analysis.log 2>&1 &
fi
EOF

chmod +x monitor_pricing.sh

# 设置定时监控（每2小时检查一次）
echo "0 */2 * * * /path/to/aistak_fastapi/monitor_pricing.sh" | crontab -
```

### 价格分析结果格式

生成的价格分析结果包含以下信息：
```json
{
  "success": true,
  "domain": "stripe.com",
  "url": "https://stripe.com/pricing",
  "url_type": "pricing_page",
  "content_summary": {
    "title": "Stripe Pricing",
    "source_type": "Dedicated pricing page",
    "content_blocks": 5,
    "total_content_length": 12000
  },
  "pricing_analysis": {
    "pricing_model": "usage-based",
    "plans": [
      {
        "name": "Standard",
        "price": "2.9",
        "currency": "USD",
        "billing_cycle": "per transaction",
        "features": ["Online payments", "Dashboard", "Fraud protection"],
        "is_free": false,
        "popular": true
      }
    ],
    "free_trial": {
      "available": false,
      "duration": "",
      "limitations": ""
    },
    "special_offers": ["Volume discounts available"],
    "currency": "USD",
    "notes": "Usage-based pricing with volume discounts"
  }
}
```

### 故障排查

#### 1. 常见问题解决
```bash
# 检查Ollama连接
python generate_price.py --status

# 测试单个域名分析
python generate_price.py --domain example.com --locale en --verbose

# 检查数据库连接
python test_database.py

# 查看详细错误日志
tail -f generate_price.log
```

#### 2. 性能优化
```bash
# 使用更快的模型
python generate_price.py --model gemma3:latest --locale en --limit 20

# 减少并发请求间隔
python generate_price.py --locale en --limit 10 --update-db

# 分批处理大量数据
for i in {1..5}; do
    python generate_price.py --locale en --limit 20 --update-db
    sleep 300  # 等待5分钟
done
```

#### 3. 数据库维护
```bash
# 检查pricing_details字段的完成情况
python -c "
from app.core.database import SessionLocal
from app.models.tool import ToolTranslation
from sqlalchemy import func

db = SessionLocal()
total = db.query(ToolTranslation).filter(ToolTranslation.locale == 'en').count()
completed = db.query(ToolTranslation).filter(
    ToolTranslation.locale == 'en',
    ToolTranslation.pricing_details.isnot(None)
).count()
print(f'总记录数: {total}')
print(f'已完成: {completed}')
print(f'完成率: {completed/total*100:.1f}%')
db.close()
"

# 清理无效的pricing_details数据
python -c "
from app.core.database import SessionLocal
from app.models.tool import ToolTranslation

db = SessionLocal()
# 清理空字符串或无效JSON的记录
invalid_records = db.query(ToolTranslation).filter(
    ToolTranslation.pricing_details == ''
).update({ToolTranslation.pricing_details: None})
db.commit()
print(f'清理了 {invalid_records} 条无效记录')
db.close()
"
```

## 定时任务处理

### 手动更新域名注册日期
```bash
python app/cli/manage_domain_registration.py update --batch-size 20 --max-tools 50
```

### 价格信息更新任务
```bash
# 手动触发价格信息更新
python generate_price.py --locale en --limit 50 --update-db

# 检查更新进度
python generate_price.py --status
```


## 中文描述翻译功能：
```
python generate_descriptions_zh.py --limit 10
```

## 中文分类翻译功能：
```
python generate_category_translations_zh.py --limit 3
```


