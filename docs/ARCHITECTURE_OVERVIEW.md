# 🏗️ Aistak FastAPI 架构概览

## 📊 系统架构图

```mermaid
graph TB
    subgraph "Client Layer"
        CLI[CLI Tools]
        API[REST API Clients]
        WEB[Web Interface]
    end
    
    subgraph "Application Layer"
        MAIN[main.py<br/>Entry Point]
        ROUTER[Routers<br/>API Endpoints]
        SERVICE[Services<br/>Business Logic]
    end
    
    subgraph "Core Layer"
        CONFIG[Config<br/>Settings]
        DB[Database<br/>SQLAlchemy]
        SCHEMA[Schemas<br/>Pydantic]
    end
    
    subgraph "External Services"
        PG[(PostgreSQL<br/>Database)]
        OLLAMA[Ollama<br/>AI Models]
        SCHEDULER[APScheduler<br/>Cron Jobs]
    end
    
    CLI --> MAIN
    API --> ROUTER
    WEB --> ROUTER
    
    MAIN --> ROUTER
    ROUTER --> SERVICE
    SERVICE --> DB
    SERVICE --> CONFIG
    SERVICE --> SCHEMA
    
    DB --> PG
    SERVICE --> OLLAMA
    SERVICE --> SCHEDULER
```

## 🔄 数据流程图

### AI内容生成流程
```mermaid
sequenceDiagram
    participant User
    participant API
    participant Service
    participant Extractor
    participant Ollama
    participant Database
    
    User->>API: POST /generate
    API->>Service: generate_descriptions()
    Service->>Database: query tools needing content
    Database-->>Service: return tool list
    
    loop For each tool
        Service->>Extractor: extract_website_content(url)
        Extractor-->>Service: website content
        Service->>Service: build_prompt()
        Service->>Ollama: call_ollama(prompt)
        Ollama-->>Service: generated content
        Service->>Database: update tool translation
    end
    
    Service-->>API: generation results
    API-->>User: response with statistics
```

### 爬虫数据处理流程
```mermaid
flowchart TD
    START([开始爬取]) --> CHECK{检查URL}
    CHECK -->|有效| CRAWL[爬取网站数据]
    CHECK -->|无效| ERROR[返回错误]
    
    CRAWL --> EXTRACT[提取工具信息]
    EXTRACT --> VALIDATE[数据验证]
    VALIDATE -->|通过| SAVE[保存到数据库]
    VALIDATE -->|失败| LOG[记录错误日志]
    
    SAVE --> TRANSLATE[创建翻译记录]
    TRANSLATE --> SCHEDULE[安排AI生成任务]
    SCHEDULE --> END([完成])
    
    ERROR --> END
    LOG --> END
```

## 🗂️ 目录结构详解

```
aistak_fastapi/
├── 🚀 main.py                          # 应用入口，FastAPI实例
├── 📋 requirements.txt                  # Python依赖包
├── 🔐 .env                             # 环境变量配置
├── 📁 app/                             # 主应用目录
│   ├── 🔧 core/                        # 核心配置模块
│   │   ├── ⚙️ config.py                # 应用配置类
│   │   └── 🗄️ database.py             # 数据库连接配置
│   ├── 📊 models/                      # SQLAlchemy数据模型
│   │   ├── 🛠️ tool.py                  # 工具相关模型
│   │   └── 👤 user.py                  # 用户模型
│   ├── 📋 schemas/                     # Pydantic数据验证
│   │   └── 🛠️ tool.py                  # 工具数据验证模式
│   ├── 🌐 routers/                     # FastAPI路由模块
│   │   ├── ❤️ health.py                # 健康检查端点
│   │   ├── 👥 users.py                 # 用户管理API
│   │   ├── 🕷️ crawler.py               # 爬虫管理API
│   │   ├── ⏰ scheduler.py             # 定时任务API
│   │   └── 🤖 description_generation.py # AI内容生成API
│   ├── 🔧 services/                    # 业务逻辑服务层
│   │   ├── 🗄️ database_service.py      # 数据库操作服务
│   │   ├── 🤖 ollama_service.py        # Ollama AI服务
│   │   ├── 📄 content_extractor_service.py # 网站内容提取
│   │   ├── ✍️ content_generation_service.py # AI内容生成
│   │   ├── 📝 description_generation_service.py # 描述生成主服务
│   │   ├── ⏰ scheduler_service.py     # 定时任务调度
│   │   └── 🛠️ toolify_service.py       # 工具相关服务
│   ├── 🔨 utils/                       # 工具函数模块
│   │   ├── 📊 data_analyzer.py         # 数据分析工具
│   │   └── 🌐 domain_utils.py          # 域名处理工具
│   ├── 🕷️ crawler/                     # 爬虫模块
│   └── 💻 cli/                         # 命令行工具
│       └── 🤖 generate_descriptions.py # 描述生成CLI
├── 📚 docs/                            # 项目文档
│   ├── 📖 DEVELOPMENT_GUIDE.md         # 开发指南
│   ├── 🏗️ ARCHITECTURE_OVERVIEW.md    # 架构概览
│   ├── 🚀 QUICK_START.md               # 快速开始
│   └── 🤖 DESCRIPTION_GENERATION_README.md # AI生成说明
├── 🧪 tests/                           # 测试文件
└── 📜 scripts/                         # 管理脚本
    ├── 🚀 start_services.sh            # 启动服务
    ├── 🛑 stop_services.sh             # 停止服务
    └── 📊 system_monitor.sh            # 系统监控
```

## 🔗 组件关系图

```mermaid
graph LR
    subgraph "Entry Point"
        MAIN[main.py]
    end
    
    subgraph "API Layer"
        HEALTH[health.py]
        USERS[users.py]
        CRAWLER[crawler.py]
        SCHEDULER[scheduler.py]
        GENERATION[description_generation.py]
    end
    
    subgraph "Service Layer"
        DB_SVC[database_service.py]
        OLLAMA_SVC[ollama_service.py]
        EXTRACT_SVC[content_extractor_service.py]
        GEN_SVC[content_generation_service.py]
        DESC_SVC[description_generation_service.py]
        SCHED_SVC[scheduler_service.py]
        TOOL_SVC[toolify_service.py]
    end
    
    subgraph "Data Layer"
        MODELS[models/]
        SCHEMAS[schemas/]
        DATABASE[(PostgreSQL)]
    end
    
    subgraph "External"
        OLLAMA_AI[Ollama AI]
        WEBSITES[Target Websites]
    end
    
    MAIN --> HEALTH
    MAIN --> USERS
    MAIN --> CRAWLER
    MAIN --> SCHEDULER
    MAIN --> GENERATION
    
    HEALTH --> DB_SVC
    USERS --> DB_SVC
    CRAWLER --> TOOL_SVC
    SCHEDULER --> SCHED_SVC
    GENERATION --> DESC_SVC
    
    DESC_SVC --> GEN_SVC
    DESC_SVC --> DB_SVC
    GEN_SVC --> OLLAMA_SVC
    GEN_SVC --> EXTRACT_SVC
    
    DB_SVC --> MODELS
    DB_SVC --> DATABASE
    
    OLLAMA_SVC --> OLLAMA_AI
    EXTRACT_SVC --> WEBSITES
    
    MODELS --> SCHEMAS
```

## 📋 核心组件说明

### 1. 入口点 (main.py)
- **职责**: 应用启动和生命周期管理
- **功能**: 
  - 创建FastAPI实例
  - 注册路由
  - 配置中间件
  - 启动/停止调度器
  - 数据库连接测试

### 2. 路由层 (routers/)
- **职责**: HTTP请求处理和响应
- **功能**:
  - 请求参数验证
  - 调用服务层
  - 响应格式化
  - 错误处理

### 3. 服务层 (services/)
- **职责**: 业务逻辑实现
- **功能**:
  - 数据处理
  - 外部服务调用
  - 业务规则执行
  - 事务管理

### 4. 数据层 (models/ + database.py)
- **职责**: 数据持久化
- **功能**:
  - ORM模型定义
  - 数据库连接管理
  - 查询优化
  - 事务控制

### 5. 验证层 (schemas/)
- **职责**: 数据验证和序列化
- **功能**:
  - 输入数据验证
  - 输出数据格式化
  - 类型检查
  - 文档生成

## 🔄 请求处理流程

### 典型API请求流程
```
1. 客户端发送HTTP请求
   ↓
2. FastAPI路由匹配
   ↓
3. 中间件处理（认证、日志等）
   ↓
4. 路由处理函数
   ↓
5. 参数验证（Pydantic）
   ↓
6. 服务层业务逻辑
   ↓
7. 数据库操作（SQLAlchemy）
   ↓
8. 外部服务调用（如需要）
   ↓
9. 响应数据序列化
   ↓
10. HTTP响应返回
```

### AI内容生成请求流程
```
1. POST /api/v1/description-generation/generate
   ↓
2. description_generation.py 路由处理
   ↓
3. DescriptionGenerationService.generate_descriptions()
   ↓
4. 查询需要生成内容的工具
   ↓
5. 对每个工具：
   a. ContentExtractorService 爬取网站
   b. 构建AI提示词
   c. OllamaService 调用AI模型
   d. 更新数据库记录
   ↓
6. 返回生成统计结果
```

## 🎯 设计原则

### 1. 分层架构
- **表现层**: 处理HTTP请求和响应
- **业务层**: 实现核心业务逻辑
- **数据层**: 管理数据持久化

### 2. 依赖注入
- 使用FastAPI的依赖注入系统
- 便于测试和模块解耦
- 支持依赖生命周期管理

### 3. 单一职责
- 每个模块专注特定功能
- 降低耦合度
- 提高可维护性

### 4. 配置外部化
- 环境变量管理配置
- 支持多环境部署
- 敏感信息保护

### 5. 异步处理
- 使用async/await处理IO密集型操作
- 提高并发性能
- 支持长时间运行任务

这个架构概览提供了系统的整体视图，帮助开发者快速理解项目结构和组件关系。
