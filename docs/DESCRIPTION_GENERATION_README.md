# 工具描述生成功能

这个功能为FastAPI项目添加了使用Ollama本地大模型自动生成工具描述的能力。该功能从Django项目完整迁移而来，保持了所有核心功能并进行了架构优化。

## 功能概述

系统可以自动为工具生成以下内容：
- **长描述** (long_description): 详细的工具介绍和功能说明
- **使用说明** (usage_instructions): 步骤化的使用指南
- **价格详情** (pricing_details): 详细的价格信息和计划对比
- **集成信息** (integration_info): 技术集成文档和API信息

## 架构设计

### 服务层架构
```
┌─────────────────────────────────────────────────────────────┐
│                    API Layer                                │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   REST API      │  │   CLI Script    │  │ Standalone   │ │
│  │   Endpoints     │  │                 │  │   Script     │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                  Service Layer                              │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │         DescriptionGenerationService                    │ │
│  │              (主要业务逻辑)                              │ │
│  └─────────────────────────────────────────────────────────┘ │
│           │                    │                    │       │
│  ┌────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ OllamaService  │  │ContentGeneration│  │ContentExtractor │ │
│  │   (AI调用)     │  │    Service      │  │    Service      │ │
│  │                │  │  (内容生成)     │  │  (网站爬取)     │ │
│  └────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                   Data Layer                                │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   PostgreSQL    │  │   Tool Models   │  │   Ollama     │ │
│  │   Database      │  │                 │  │   Server     │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 核心服务说明

1. **OllamaService**: 封装Ollama API调用，包括服务器发现、健康检查、模型调用等
2. **ContentExtractorService**: 网站内容爬取和智能提取，支持多种内容格式
3. **ContentGenerationService**: 内容生成的核心逻辑，协调AI调用和内容处理
4. **DescriptionGenerationService**: 主要业务服务，协调各组件完成完整的生成流程

## 使用方法

### 1. 命令行使用

#### 基本用法
```bash
# 生成10个工具的长描述
python generate_descriptions.py --locale en --limit 10

# 生成多个字段
python generate_descriptions.py --fields long_description,pricing_details --limit 5

# 干运行模式（仅显示将要处理的工具）
python generate_descriptions.py --dry-run

# 显示当前状态
python generate_descriptions.py --status

# 列出可用模型
python generate_descriptions.py --list-models
```

#### 高级用法
```bash
# 使用CLI模块（更多选项）
python -m app.cli.generate_descriptions --help

# 指定Ollama服务器
python generate_descriptions.py --ip-address ************* --port 11434

# 使用不同模型
python generate_descriptions.py --model gemma3:latest --limit 20

# 启用详细日志
python generate_descriptions.py --verbose --limit 5
```

### 2. REST API使用

启动FastAPI服务器后，可以通过HTTP API调用：

```bash
# 启动服务器
uvicorn main:app --host 0.0.0.0 --port 8000

# 批量生成描述
curl -X POST "http://localhost:8000/api/v1/description-generation/generate" \
  -H "Content-Type: application/json" \
  -d '{
    "locale": "en",
    "model_name": "llama3:latest",
    "fields": "long_description,pricing_details",
    "limit": 10,
    "dry_run": false
  }'

# 查看生成状态
curl "http://localhost:8000/api/v1/description-generation/status?locale=en"

# 列出可用模型
curl "http://localhost:8000/api/v1/description-generation/models"

# 健康检查
curl "http://localhost:8000/api/v1/description-generation/health"
```

### 3. Python代码调用

```python
from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.services.description_generation_service import DescriptionGenerationService

# 创建数据库会话
db = SessionLocal()

# 创建服务实例
service = DescriptionGenerationService(db)

# 初始化Ollama服务
success = service.initialize_services()

if success:
    # 执行生成
    result = service.generate_descriptions(
        locale='en',
        model_name='llama3:latest',
        fields='long_description',
        limit=10
    )
    
    print(f"处理结果: {result}")
else:
    print("服务初始化失败")

# 关闭数据库连接
db.close()
```

## 配置参数

### 主要参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `locale` | str | 'en' | 语言代码 |
| `model_name` | str | 'llama3:latest' | 主要使用的Ollama模型 |
| `fallback_model` | str | 'tinyllama:latest' | 备用模型 |
| `fields` | str | 'long_description,usage_instructions,pricing_details,integration_info' | 要生成的字段 |
| `limit` | int | 10 | 每次处理的工具数量上限 |
| `max_chunks` | int | 5 | 网站内容最大分块数 |
| `chunk_size` | int | 2000 | 每个文本块的字符数 |
| `max_tokens` | int | 2048 | AI生成的最大令牌数 |
| `temperature` | float | 0.7 | 生成文本的随机性 |
| `dry_run` | bool | False | 是否为测试模式 |

### 可生成的字段

- `long_description`: 详细描述，包含工具的主要功能、特点、目标用户等
- `usage_instructions`: 使用说明，包含步骤化的操作指南
- `pricing_details`: 价格信息，包含各种计划和定价详情
- `integration_info`: 集成信息，包含API文档、技术要求等

## 安装和配置

### 1. 安装依赖

```bash
# 安装Python依赖
pip install requests beautifulsoup4 sqlalchemy fastapi uvicorn

# 或者从requirements.txt安装
pip install -r requirements.txt
```

### 2. 配置Ollama服务

```bash
# 安装Ollama
# Mac
brew install ollama

# Linux
curl -fsSL https://ollama.com/install.sh | sh

# 启动Ollama服务
ollama serve

# 下载模型
ollama pull llama3:latest
ollama pull tinyllama:latest
```

### 3. 配置数据库

确保PostgreSQL数据库已配置并且包含工具数据表。

### 4. 验证安装

```bash
# 测试命令行工具
python generate_descriptions.py --status

# 测试API服务
python -c "
from app.services.description_generation_service import DescriptionGenerationService
from app.core.database import SessionLocal
db = SessionLocal()
service = DescriptionGenerationService(db)
print('服务初始化:', service.initialize_services())
db.close()
"
```

## 日志和监控

### 日志配置

系统会自动生成日志文件 `generate_descriptions.log`，包含：
- 处理进度信息
- 错误和警告信息
- API调用详情
- 数据库操作记录

### 监控指标

可以通过以下方式监控系统状态：
- 使用 `--status` 参数查看完成情况统计
- 通过API端点 `/description-generation/health` 检查服务健康状态
- 查看日志文件了解详细执行情况

## 故障排除

### 常见问题

1. **Ollama服务连接失败**
   ```bash
   # 检查Ollama服务状态
   curl http://localhost:11434/api/tags
   
   # 重启Ollama服务
   ollama serve
   ```

2. **数据库连接问题**
   ```bash
   # 检查数据库配置
   python -c "from app.core.database import test_database_connection; print(test_database_connection())"
   ```

3. **模型不存在**
   ```bash
   # 列出可用模型
   ollama list
   
   # 下载需要的模型
   ollama pull llama3:latest
   ```

4. **内存不足**
   - 减少 `limit` 参数值
   - 减少 `max_chunks` 和 `chunk_size`
   - 使用更小的模型如 `tinyllama:latest`

### 调试模式

启用详细日志进行调试：
```bash
python generate_descriptions.py --verbose --dry-run --limit 1
```

## 性能优化

### 建议配置

- **小规模测试**: `limit=5, max_chunks=3, chunk_size=1500`
- **常规使用**: `limit=10, max_chunks=5, chunk_size=2000`
- **大批量处理**: `limit=50, max_chunks=3, chunk_size=3000`

### 优化策略

1. **批量处理**: 使用适当的 `limit` 值，避免单次处理过多工具
2. **内容分块**: 合理设置 `max_chunks` 和 `chunk_size` 平衡质量和性能
3. **模型选择**: 根据需求选择合适的模型，大模型质量更好但速度较慢
4. **并发控制**: 系统内置了API限速保护，避免过度调用

## 扩展开发

### 添加新字段

1. 在数据库模型中添加新字段
2. 在 `ContentGenerationService._build_prompt()` 中添加新的提示词模板
3. 更新CLI和API参数验证

### 支持新模型

1. 在 `OllamaService` 中添加模型特定的配置
2. 根据需要调整提示词格式
3. 更新默认参数和文档

### 自定义内容提取

1. 继承 `ContentExtractorService` 类
2. 重写相关方法实现自定义逻辑
3. 在服务初始化时注入自定义实现

这个功能提供了完整的工具描述自动生成解决方案，支持多种使用方式和灵活的配置选项。
