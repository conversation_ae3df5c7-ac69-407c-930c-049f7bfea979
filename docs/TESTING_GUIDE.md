# 域名价格分析功能测试指南

## 🧪 测试环境准备

### 1. 确保 Ollama 服务运行
```bash
# 检查 Ollama 是否运行
curl http://localhost:11434/api/tags

# 如果没有运行，启动 Ollama
ollama serve

# 确保有可用的模型
ollama list
```

### 2. 确保数据库连接正常
```bash
# 检查数据库连接
python -c "from app.core.database import SessionLocal; db = SessionLocal(); print('数据库连接成功'); db.close()"
```

## 📋 正确的测试步骤

### 步骤 1: 运行单元测试
```bash
# 进入项目根目录
cd /Users/<USER>/fuwenhao/github/aistak_fastapi

# 运行单元测试
python tests/test_price_analysis.py
```

**预期结果**: 所有单元测试应该通过（OK）

### 步骤 2: 测试单个域名分析
```bash
# 测试 gumloop.com 定价页面
python generate_price.py --domain gumloop.com/pricing --model deepseek-r1:latest --verbose

# 测试其他域名
python generate_price.py --domain github.com --model gemma3:latest
python generate_price.py --domain openai.com/pricing --model deepseek-r1:latest
```

**预期结果**: 
- 成功提取网站内容
- 返回结构化的 JSON 价格分析
- 包含定价方案、价格、功能等信息

### 步骤 3: 测试批量分析（干运行）
```bash
# 先查看将要处理的工具
python generate_price.py --dry-run --limit 5 --locale en
```

**预期结果**: 显示数据库中的工具列表，不执行实际分析

### 步骤 4: 测试服务状态
```bash
# 检查服务状态
python generate_price.py --status

# 列出可用模型
python generate_price.py --list-models
```

**预期结果**: 
- 显示 Ollama 服务状态
- 显示可用模型列表
- 显示内容提取服务状态

### 步骤 5: 运行示例脚本
```bash
# 运行使用示例
python examples/price_analysis_example.py
```

**预期结果**: 
- 展示完整的使用流程
- 包含单域名分析和批量分析示例

## 🔍 常见问题排查

### 问题 1: "Ollama服务不可用"
**解决方案**:
```bash
# 检查 Ollama 进程
ps aux | grep ollama

# 重启 Ollama 服务
ollama serve

# 检查端口是否被占用
lsof -i :11434
```

### 问题 2: "数据库连接失败"
**解决方案**:
```bash
# 检查数据库配置
cat app/core/database.py

# 确保数据库文件存在
ls -la *.db

# 重新创建数据库表（如果需要）
python tests/create_tables.py
```

### 问题 3: "无法提取网站内容"
**解决方案**:
```bash
# 测试网络连接
curl -I https://gumloop.com/pricing

# 使用调试脚本查看提取的内容
python debug_content_extraction.py

# 检查防火墙和代理设置
```

### 问题 4: "模型不存在"
**解决方案**:
```bash
# 下载推荐模型
ollama pull deepseek-r1:latest
ollama pull gemma3:latest

# 检查模型列表
ollama list
```

## 📊 测试结果验证

### 成功的测试结果应该包含:

1. **单元测试**: 所有测试通过
2. **域名分析**: 返回完整的 JSON 结构
3. **价格信息**: 包含定价方案、价格、功能
4. **错误处理**: 优雅处理网络错误和解析错误

### JSON 输出格式验证:
```json
{
  "success": true,
  "domain": "example.com",
  "url": "https://example.com",
  "pricing_analysis": {
    "pricing_model": "订阅制",
    "plans": [...],
    "free_trial": {...},
    "special_offers": [...],
    "currency": "USD",
    "notes": "..."
  },
  "raw_analysis": "..."
}
```

## 🎯 具体测试命令

### 测试 gumloop.com 的完整流程:
```bash
# 1. 检查服务状态
python generate_price.py --status

# 2. 分析主页（应该显示信息不足）
python generate_price.py --domain gumloop.com --model deepseek-r1:latest

# 3. 分析定价页面（应该返回详细价格信息）
python generate_price.py --domain gumloop.com/pricing --model deepseek-r1:latest --output gumloop_result.json

# 4. 查看保存的结果
cat gumloop_result.json | jq .pricing_analysis
```

### 对比不同模型的结果:
```bash
# 使用 deepseek-r1:latest 模型
python generate_price.py --domain gumloop.com/pricing --model deepseek-r1:latest --output qwen_result.json

# 使用 gemma3:latest 模型
python generate_price.py --domain gumloop.com/pricing --model gemma3:latest --output gemma_result.json

# 对比结果
diff <(jq .pricing_analysis qwen_result.json) <(jq .pricing_analysis gemma_result.json)
```

## 🚨 如果测试失败

### 检查日志文件:
```bash
# 查看详细日志
tail -f generate_price.log

# 查看错误信息
grep ERROR generate_price.log
```

### 重新安装依赖:
```bash
# 重新安装 Python 依赖
pip install -r requirements.txt

# 检查关键依赖
python -c "import requests, beautifulsoup4, sqlalchemy; print('依赖正常')"
```

### 重置环境:
```bash
# 清理日志文件
rm -f generate_price.log

# 重启 Ollama 服务
pkill ollama
ollama serve &

# 重新测试
python generate_price.py --domain gumloop.com/pricing --model deepseek-r1:latest
```

## 📈 性能测试

### 测试处理时间:
```bash
# 记录处理时间
time python generate_price.py --domain gumloop.com/pricing --model deepseek-r1:latest
```

### 测试不同大小的网站:
```bash
# 小网站
python generate_price.py --domain example.com

# 中等网站  
python generate_price.py --domain github.com/pricing

# 大型网站
python generate_price.py --domain aws.amazon.com/pricing
```

## ✅ 测试通过标准

1. **单元测试**: 13/13 通过
2. **集成测试**: 成功分析真实网站
3. **错误处理**: 优雅处理各种异常情况
4. **输出格式**: 符合预期的 JSON 结构
5. **性能**: 单个域名分析在 60 秒内完成

如果所有测试都通过，说明系统工作正常！
