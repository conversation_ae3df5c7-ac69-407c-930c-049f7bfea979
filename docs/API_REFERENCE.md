# 🌐 Aistak FastAPI API 参考文档

## 📋 API 概览

Aistak FastAPI 提供了完整的 RESTful API，支持工具管理、AI内容生成、爬虫控制和系统监控等功能。

**基础URL**: `http://localhost:8000/api/v1`

**API文档**: 
- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

## 🔐 认证方式

目前API为开放访问，未来版本将支持：
- JWT Token认证
- API Key认证
- OAuth 2.0

## 📊 响应格式

### 成功响应
```json
{
    "success": true,
    "data": {...},
    "message": "操作成功"
}
```

### 错误响应
```json
{
    "success": false,
    "error": "错误类型",
    "detail": "详细错误信息",
    "status_code": 400
}
```

## 🏥 健康检查 API

### 基础健康检查
```http
GET /api/v1/health/
```

**响应示例**:
```json
{
    "status": "healthy",
    "message": "服务运行正常"
}
```

### 数据库健康检查
```http
GET /api/v1/health/database
```

**响应示例**:
```json
{
    "status": "healthy",
    "message": "数据库连接正常",
    "database_info": {
        "status": "connected",
        "database_name": "aistak_db",
        "user": "postgres",
        "version": "PostgreSQL 14.0"
    }
}
```

### 数据库详细信息
```http
GET /api/v1/health/database/info
```

## 🤖 AI内容生成 API

### 批量生成工具描述
```http
POST /api/v1/description-generation/generate
```

**请求体**:
```json
{
    "locale": "en",
    "model_name": "gemma3:latest",
    "fallback_model": "deepseek-r1:latest",
    "fields": "long_description,usage_instructions,pricing_details,integration_info",
    "limit": 10,
    "max_chunks": 5,
    "chunk_size": 2000,
    "max_tokens": 2048,
    "temperature": 0.7,
    "dry_run": false,
    "ip_address": null,
    "port": 11434
}
```

**响应示例**:
```json
{
    "success": true,
    "message": "批量处理完成: 处理 10 个，成功 8 个，失败 2 个",
    "processed": 10,
    "succeeded": 8,
    "failed": 2,
    "errors": [
        "工具 tool-1 生成失败: 网站无法访问",
        "工具 tool-2 生成失败: AI服务超时"
    ]
}
```

### 查看生成状态
```http
GET /api/v1/description-generation/status?locale=en
```

**响应示例**:
```json
{
    "success": true,
    "total_tools": 2528,
    "locale": "en",
    "field_statistics": {
        "long_description": {
            "completed": 2507,
            "pending": 21,
            "completion_rate": 99.17
        },
        "usage_instructions": {
            "completed": 0,
            "pending": 2528,
            "completion_rate": 0.0
        },
        "pricing_details": {
            "completed": 1,
            "pending": 2527,
            "completion_rate": 0.04
        },
        "integration_info": {
            "completed": 0,
            "pending": 2528,
            "completion_rate": 0.0
        }
    },
    "ollama_service_available": true
}
```

### 列出可用模型
```http
GET /api/v1/description-generation/models?ip_address=localhost&port=11434
```

**响应示例**:
```json
{
    "success": true,
    "models": [
        {
            "name": "gemma3:latest",
            "size": "4.8GB",
            "modified_at": "2024-01-15T10:30:00Z"
        },
        {
            "name": "deepseek-r1:latest",
            "size": "8.2GB",
            "modified_at": "2024-01-14T15:45:00Z"
        }
    ]
}
```

### AI服务健康检查
```http
GET /api/v1/description-generation/health?ip_address=localhost&port=11434
```

**响应示例**:
```json
{
    "status": "healthy",
    "database": "connected",
    "ollama_service": "connected: 127.0.0.1:11434",
    "timestamp": "2024-01-15T12:00:00Z"
}
```

### 单个工具生成
```http
POST /api/v1/description-generation/generate-single
```

**请求体**:
```json
{
    "tool_id": "example-tool",
    "field_type": "long_description",
    "model_name": "gemma3:latest",
    "ip_address": null,
    "port": 11434
}
```

## 🕷️ 爬虫管理 API

### 爬取单个工具
```http
POST /api/v1/crawler/crawl
```

**请求体**:
```json
{
    "url": "https://example-tool.com",
    "tool_id": "example-tool"
}
```

### 批量爬取工具
```http
POST /api/v1/crawler/batch-crawl
```

**请求体**:
```json
{
    "urls": [
        "https://tool1.com",
        "https://tool2.com",
        "https://tool3.com"
    ],
    "batch_size": 10
}
```

### 爬虫状态查询
```http
GET /api/v1/crawler/status
```

## ⏰ 调度器管理 API

### 查看调度器状态
```http
GET /api/v1/scheduler/status
```

**响应示例**:
```json
{
    "status": "running",
    "jobs_count": 3,
    "next_run_time": "2024-01-16T02:00:00Z"
}
```

### 查看所有任务
```http
GET /api/v1/scheduler/jobs
```

**响应示例**:
```json
{
    "jobs": [
        {
            "id": "daily_generation",
            "name": "每日内容生成",
            "next_run_time": "2024-01-16T02:00:00Z",
            "trigger": "cron"
        },
        {
            "id": "weekly_crawl",
            "name": "每周数据爬取",
            "next_run_time": "2024-01-21T03:00:00Z",
            "trigger": "cron"
        }
    ]
}
```

### 创建新任务
```http
POST /api/v1/scheduler/jobs
```

**请求体**:
```json
{
    "job_id": "custom_task",
    "func_name": "custom_function",
    "trigger_type": "cron",
    "trigger_args": {
        "hour": 4,
        "minute": 30
    }
}
```

### 删除任务
```http
DELETE /api/v1/scheduler/jobs/{job_id}
```

## 👥 用户管理 API

### 获取用户列表
```http
GET /api/v1/users/?page=1&size=10
```

**响应示例**:
```json
{
    "data": [
        {
            "id": 1,
            "username": "admin",
            "email": "<EMAIL>",
            "is_active": true,
            "created_at": "2024-01-01T00:00:00Z"
        }
    ],
    "pagination": {
        "page": 1,
        "size": 10,
        "total": 1,
        "pages": 1
    }
}
```

### 创建用户
```http
POST /api/v1/users/
```

**请求体**:
```json
{
    "username": "newuser",
    "email": "<EMAIL>",
    "password": "securepassword"
}
```

### 获取特定用户
```http
GET /api/v1/users/{user_id}
```

## 🛠️ 工具管理 API (扩展)

### 获取工具列表
```http
GET /api/v1/tools/?page=1&size=20&category=ai-writing&search=chatgpt
```

**查询参数**:
- `page`: 页码 (默认: 1)
- `size`: 每页数量 (默认: 20, 最大: 100)
- `category`: 分类筛选
- `search`: 搜索关键词
- `locale`: 语言 (默认: en)

**响应示例**:
```json
{
    "data": [
        {
            "tool_id": "chatgpt",
            "name": "ChatGPT",
            "description": "AI对话助手",
            "url": "https://chat.openai.com",
            "pricing_type": "freemium",
            "rating": 4.8,
            "categories": ["ai-writing", "chatbot"],
            "created_at": "2024-01-01T00:00:00Z"
        }
    ],
    "pagination": {
        "page": 1,
        "size": 20,
        "total": 2528,
        "pages": 127
    }
}
```

### 获取工具详情
```http
GET /api/v1/tools/{tool_id}?locale=en
```

### 创建工具
```http
POST /api/v1/tools/
```

**请求体**:
```json
{
    "tool_id": "new-tool",
    "url": "https://new-tool.com",
    "pricing_type": "free",
    "translations": {
        "en": {
            "name": "New Tool",
            "description": "A new AI tool"
        }
    },
    "categories": ["ai-productivity"]
}
```

## 📊 错误代码说明

| 状态码 | 错误类型 | 说明 |
|--------|----------|------|
| 400 | Bad Request | 请求参数错误 |
| 401 | Unauthorized | 未授权访问 |
| 403 | Forbidden | 权限不足 |
| 404 | Not Found | 资源不存在 |
| 422 | Validation Error | 数据验证失败 |
| 500 | Internal Server Error | 服务器内部错误 |
| 503 | Service Unavailable | 服务不可用 |

## 🔄 分页和排序

### 分页参数
- `page`: 页码，从1开始
- `size`: 每页数量，默认20，最大100

### 排序参数
- `sort`: 排序字段
- `order`: 排序方向 (asc/desc)

**示例**:
```http
GET /api/v1/tools/?page=2&size=50&sort=created_at&order=desc
```

## 🚀 使用示例

### Python 客户端示例
```python
import requests

# 基础配置
BASE_URL = "http://localhost:8000/api/v1"
headers = {"Content-Type": "application/json"}

# 健康检查
response = requests.get(f"{BASE_URL}/health/")
print(response.json())

# 生成工具描述
data = {
    "locale": "en",
    "limit": 5,
    "fields": "long_description",
    "dry_run": True
}
response = requests.post(f"{BASE_URL}/description-generation/generate", 
                        json=data, headers=headers)
print(response.json())

# 查看生成状态
response = requests.get(f"{BASE_URL}/description-generation/status?locale=en")
print(response.json())
```

### JavaScript 客户端示例
```javascript
const BASE_URL = 'http://localhost:8000/api/v1';

// 健康检查
fetch(`${BASE_URL}/health/`)
  .then(response => response.json())
  .then(data => console.log(data));

// 生成工具描述
const generateData = {
  locale: 'en',
  limit: 5,
  fields: 'long_description',
  dry_run: true
};

fetch(`${BASE_URL}/description-generation/generate`, {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify(generateData)
})
.then(response => response.json())
.then(data => console.log(data));
```

### cURL 示例
```bash
# 健康检查
curl -X GET "http://localhost:8000/api/v1/health/"

# 生成工具描述
curl -X POST "http://localhost:8000/api/v1/description-generation/generate" \
  -H "Content-Type: application/json" \
  -d '{
    "locale": "en",
    "limit": 5,
    "fields": "long_description",
    "dry_run": true
  }'

# 查看生成状态
curl -X GET "http://localhost:8000/api/v1/description-generation/status?locale=en"
```

## 📝 注意事项

1. **速率限制**: 目前无限制，生产环境建议添加
2. **数据大小**: 单次请求建议不超过10MB
3. **超时设置**: 长时间任务建议使用异步处理
4. **缓存策略**: 频繁查询的数据会被缓存
5. **版本控制**: API版本通过URL路径控制 (/api/v1/)

这个API参考文档提供了完整的接口说明和使用示例，帮助开发者快速集成和使用API服务。
