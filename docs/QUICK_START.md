# 🚀 Aistak FastAPI 快速启动指南

## 一分钟快速开始

```bash
# 1. 一键启动所有服务
./start_services.sh

# 2. 查看系统状态
./system_monitor.sh

# 3. 生成工具描述
python generate_descriptions.py --limit 5

# 4. 停止所有服务
./stop_services.sh
```

## 🔧 核心命令

### 服务管理
```bash
./start_services.sh     # 启动所有服务
./stop_services.sh      # 停止所有服务
./system_monitor.sh     # 系统监控
./system_monitor.sh -w  # 持续监控
```

### AI内容生成
```bash
# 基本用法
python generate_descriptions.py --limit 10

# 生成多个字段
python generate_descriptions.py --fields long_description,pricing_details --limit 5

# 查看状态
python generate_descriptions.py --status

# 干运行（查看将要处理的工具）
python generate_descriptions.py --dry-run --limit 20

# 使用不同模型
python generate_descriptions.py --model deepseek-r1:latest --limit 3
```

### 后台运行
```bash
# 后台生成大批量内容
nohup python generate_descriptions.py --limit 100 --fields long_description > generation.log 2>&1 &

# 查看后台任务
ps aux | grep generate_descriptions

# 查看生成日志
tail -f generation.log
```

## 🌐 API使用

### 启动API服务
```bash
./start_services.sh
# 或手动启动
uvicorn main:app --host 0.0.0.0 --port 8000
```

### 常用API端点
```bash
# 健康检查
curl http://localhost:8000/api/v1/health/

# 批量生成描述
curl -X POST "http://localhost:8000/api/v1/description-generation/generate" \
  -H "Content-Type: application/json" \
  -d '{"locale": "en", "limit": 10, "dry_run": false}'

# 查看生成状态
curl "http://localhost:8000/api/v1/description-generation/status?locale=en"

# 列出可用模型
curl "http://localhost:8000/api/v1/description-generation/models"
```

## 📊 监控和日志

### 实时监控
```bash
# 系统状态监控
./system_monitor.sh

# 持续监控（每30秒刷新）
./system_monitor.sh --watch

# 查看实时日志
tail -f app.log
tail -f generate_descriptions.log
```

### 性能检查
```bash
# API响应时间测试
curl -o /dev/null -s -w '%{time_total}' http://localhost:8000/api/v1/health/

# 检查进程状态
ps aux | grep -E "(uvicorn|ollama|generate_descriptions)"

# 检查端口监听
lsof -i :8000
lsof -i :11434
```

## 🛠️ 故障排除

### 常见问题
```bash
# 1. 服务启动失败
./stop_services.sh  # 先停止所有服务
./start_services.sh # 重新启动

# 2. Ollama连接问题
curl http://localhost:11434/api/tags  # 检查Ollama API
ollama list  # 查看可用模型
ollama serve  # 手动启动Ollama

# 3. 数据库连接问题
curl http://localhost:8000/api/v1/health/database  # 检查数据库状态

# 4. 端口被占用
lsof -i :8000  # 查看端口占用
pkill -f "uvicorn main:app"  # 强制停止FastAPI
```

### 紧急恢复
```bash
# 一键恢复脚本
./stop_services.sh
sleep 5
./start_services.sh
./system_monitor.sh
```

## 📈 生产环境部署

### 使用systemd服务
```bash
# 创建服务文件（需要root权限）
sudo cp systemd/*.service /etc/systemd/system/
sudo systemctl daemon-reload

# 启动服务
sudo systemctl start aistak-fastapi
sudo systemctl start ollama

# 设置开机自启
sudo systemctl enable aistak-fastapi
sudo systemctl enable ollama

# 查看服务状态
sudo systemctl status aistak-fastapi
sudo journalctl -u aistak-fastapi -f
```

### 定时任务设置
```bash
# 编辑crontab
crontab -e

# 添加定时生成任务（每天凌晨2点）
0 2 * * * cd /path/to/aistak_fastapi && python generate_descriptions.py --limit 50 >> /var/log/aistak_generation.log 2>&1

# 添加监控任务（每10分钟）
*/10 * * * * /path/to/aistak_fastapi/system_monitor.sh >> /var/log/aistak_monitor.log 2>&1
```

## 🎯 使用场景

### 开发调试
```bash
./start_services.sh
python generate_descriptions.py --dry-run --limit 3
python generate_descriptions.py --limit 1 --verbose
./system_monitor.sh
```

### 批量处理
```bash
./start_services.sh
nohup python generate_descriptions.py --limit 100 --fields long_description > batch.log 2>&1 &
watch -n 30 'python generate_descriptions.py --status'
```

### 定期维护
```bash
# 每日维护脚本
./system_monitor.sh
python generate_descriptions.py --status
python generate_descriptions.py --limit 20
find . -name "*.log" -mtime +7 -delete
```

## 📚 更多信息

- 完整文档: [README.md](README.md)
- AI生成详细说明: [docs/DESCRIPTION_GENERATION_README.md](docs/DESCRIPTION_GENERATION_README.md)
- API文档: http://localhost:8000/docs (服务启动后访问)

## 🆘 获取帮助

```bash
# 查看脚本帮助
./start_services.sh --help
python generate_descriptions.py --help

# 查看系统状态
./system_monitor.sh

# 检查日志
tail -f app.log
tail -f generate_descriptions.log
```

---

**提示**: 首次使用建议先运行 `./start_services.sh` 确保所有服务正常启动，然后使用 `./system_monitor.sh` 检查系统状态。
