# 爬虫功能文档

本文档描述了从Django项目迁移到FastAPI项目的完整爬虫功能。

## 功能概述

爬虫系统提供了完整的网站数据采集和入库功能，包括：

- **数据采集**: 从Toolify.ai网站采集工具信息
- **数据转换**: 将外部数据转换为内部数据库格式
- **数据存储**: 将采集的数据保存到PostgreSQL数据库
- **定时任务**: 支持定时自动采集数据
- **数据分析**: 提供数据统计和分析功能

## 项目结构

```
app/
├── crawler/                 # 爬虫模块
├── models/
│   └── tool.py             # 数据库模型
├── schemas/
│   └── tool.py             # API数据模型
├── services/
│   ├── toolify_service.py  # Toolify API服务
│   ├── database_service.py # 数据库操作服务
│   └── scheduler_service.py # 定时任务服务
├── utils/
│   ├── domain_utils.py     # 域名工具
│   └── data_analyzer.py    # 数据分析工具
└── routers/
    ├── crawler.py          # 爬虫API路由
    └── scheduler.py        # 定时任务API路由
```

## 数据库模型

### 核心表结构

1. **tools** - 工具主表
2. **tool_translations** - 工具翻译表
3. **categories** - 分类表
4. **category_translations** - 分类翻译表
5. **tool_categories** - 工具-分类关联表
6. **tags** - 标签表
7. **tag_translations** - 标签翻译表
8. **tool_tags** - 工具-标签关联表
9. **tool_features** - 工具特性表

## API 接口

### 爬虫接口

#### 1. 单页爬取
```
GET /api/v1/crawler/toolify?page=1&per_page=28&tool_type=1
```

#### 2. 批量爬取
```
POST /api/v1/crawler/toolify/batch
{
    "start_page": 1,
    "max_pages": 10,
    "per_page": 28,
    "tool_type": 1,
    "save_to_db": true
}
```

#### 3. 数据分析
```
POST /api/v1/crawler/toolify/analyze
{
    "data": {...}  // Toolify API返回的数据
}
```

#### 4. 爬虫状态
```
GET /api/v1/crawler/status
```

#### 5. 测试爬虫
```
POST /api/v1/crawler/test
```

#### 6. 后台爬取
```
POST /api/v1/crawler/background-crawl
{
    "start_page": 1,
    "max_pages": 5,
    "per_page": 28,
    "tool_type": 1,
    "save_to_db": true
}
```

### 定时任务接口

#### 1. 调度器状态
```
GET /api/v1/scheduler/status
```

#### 2. 任务列表
```
GET /api/v1/scheduler/jobs
```

#### 3. 启动调度器
```
POST /api/v1/scheduler/start
```

#### 4. 停止调度器
```
POST /api/v1/scheduler/stop
```

#### 5. 重启调度器
```
POST /api/v1/scheduler/restart
```

## 安装和配置

### 1. 安装依赖

```bash
# 使用uv安装依赖
uv add requests python-whois beautifulsoup4 lxml python-multipart alembic apscheduler

# 或使用pip
pip install -r requirements.txt
```

### 2. 配置数据库

在 `.env` 文件中配置数据库连接：

```env
DATABASE_URL=postgresql://username:password@localhost:5432/database_name
```

### 3. 创建数据库表

```bash
python create_tables.py create
```

### 4. 启动应用

```bash
uvicorn main:app --reload
```

## 使用指南

### 1. 测试功能

运行完整测试：
```bash
python test_crawler.py
```

运行特定测试：
```bash
python test_crawler.py db        # 测试数据库连接
python test_crawler.py api       # 测试API连接
python test_crawler.py batch     # 测试批量爬取
```

### 2. 手动爬取数据

使用API接口进行手动爬取：

```bash
# 爬取单页数据
curl "http://localhost:8000/api/v1/crawler/toolify?page=1&per_page=10"

# 批量爬取并保存到数据库
curl -X POST "http://localhost:8000/api/v1/crawler/toolify/batch" \
  -H "Content-Type: application/json" \
  -d '{
    "start_page": 1,
    "max_pages": 2,
    "per_page": 20,
    "tool_type": 1,
    "save_to_db": true
  }'
```

### 3. 定时任务

系统会自动启动定时任务：

- **每日12点**: 爬取2页工具数据
- **每周日2点**: 深度爬取10页工具数据

可以通过API管理定时任务：

```bash
# 查看任务状态
curl "http://localhost:8000/api/v1/scheduler/status"

# 查看所有任务
curl "http://localhost:8000/api/v1/scheduler/jobs"
```

## 数据流程

1. **数据采集**: 从Toolify.ai API获取原始数据
2. **数据转换**: 将原始数据转换为内部格式
3. **数据验证**: 验证数据完整性和格式
4. **数据存储**: 保存到PostgreSQL数据库
5. **关系处理**: 处理工具与分类、标签的关联关系

## 错误处理

系统包含完善的错误处理机制：

- **网络错误**: 自动重试机制，指数退避
- **数据错误**: 跳过无效数据，记录错误日志
- **数据库错误**: 事务回滚，保证数据一致性
- **限流处理**: 处理API限流，自动等待重试

## 监控和日志

- **详细日志**: 记录所有操作和错误信息
- **状态监控**: 提供实时状态查询接口
- **统计信息**: 提供数据统计和分析功能

## 性能优化

- **批量操作**: 支持批量数据处理
- **连接池**: 使用数据库连接池
- **异步处理**: 支持后台异步任务
- **缓存机制**: 避免重复数据处理

## 扩展性

系统设计具有良好的扩展性：

- **多数据源**: 易于添加新的数据源
- **插件化**: 支持自定义数据处理器
- **配置化**: 支持灵活的配置管理
- **微服务**: 可以独立部署和扩展

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库配置
   - 确认数据库服务运行状态

2. **API请求失败**
   - 检查网络连接
   - 确认目标网站可访问

3. **数据保存失败**
   - 检查数据格式
   - 查看错误日志

### 日志查看

应用日志包含详细的操作信息，可以通过日志排查问题：

```bash
# 查看应用日志
tail -f /var/log/fastapi/app.log

# 查看定时任务日志
tail -f /tmp/crawler_cron.log
```

## 维护建议

1. **定期备份**: 定期备份数据库
2. **监控磁盘**: 监控日志文件大小
3. **更新依赖**: 定期更新依赖包
4. **性能监控**: 监控API响应时间和数据库性能
