# 域名注册日期更新功能

## 📋 功能概述

本功能为AI工具管理平台添加了自动查询和更新工具域名注册日期的能力。系统会定期查询数据库中`tools`表里`domain_registration_date`字段为空的记录，通过WHOIS查询获取域名注册日期，并更新到数据库中。

## 🏗️ 架构设计

### 核心组件

1. **数据模型扩展** (`app/models/tool.py`)
   - 为`Tool`模型添加`domain_registration_date`字段
   - 类型：`DateTime(timezone=True)`，允许为空

2. **域名注册服务** (`app/services/domain_registration_service.py`)
   - 提供域名注册日期查询和更新功能
   - 支持批量处理和错误处理

3. **定时任务集成** (`app/services/scheduler_service.py`)
   - 每日凌晨1点自动执行域名注册日期更新
   - 集成到现有的APScheduler调度系统

4. **数据库服务扩展** (`app/services/database_service.py`)
   - 添加查询和更新域名注册日期的方法

## 🚀 使用方法

### 1. 数据库准备

首先确保数据库表包含`domain_registration_date`字段：

```bash
# 方法1: 使用CLI脚本添加字段
python app/cli/add_domain_registration_field.py

# 方法2: 重新创建表（会丢失数据，谨慎使用）
python tests/create_tables.py recreate
```

### 2. 启动定时任务

定时任务会在应用启动时自动启动：

```bash
# 启动应用
uvicorn main:app --reload
```

定时任务配置：
- **执行时间**: 每日凌晨1点
- **任务ID**: `daily_domain_registration_update`
- **任务名称**: 每日域名注册日期更新

### 3. 手动测试

运行测试脚本验证功能：

```bash
# 运行完整测试
python tests/test_domain_registration_service.py

# 测试单个功能
python -c "
from app.services.domain_registration_service import DomainRegistrationService
service = DomainRegistrationService()
result = service.batch_update_registration_dates(batch_size=5, max_tools=10)
print(result)
"
```

### 4. 监控任务状态

通过API监控定时任务状态：

```bash
# 查看调度器状态
curl http://localhost:8000/api/v1/scheduler/status

# 查看所有任务
curl http://localhost:8000/api/v1/scheduler/jobs
```

## 🔧 配置参数

### 批量更新参数

在`scheduler_service.py`中的`_daily_domain_registration_update_task`方法中可以调整：

```python
result = domain_service.batch_update_registration_dates(
    batch_size=20,  # 每批处理的工具数量
    max_tools=50    # 每天最多处理的工具数量
)
```

### WHOIS查询限制

为避免过于频繁的WHOIS查询，系统内置了以下限制：
- 批次间休息1秒
- 每天最多处理50个工具
- 每批处理20个工具

## 📊 功能特性

### 1. 智能域名提取
- 自动从URL中提取域名
- 支持各种URL格式（http/https、带路径等）
- 域名有效性验证

### 2. 可靠的WHOIS查询
- 使用`python-whois`库进行查询
- 处理各种WHOIS响应格式
- 错误处理和重试机制

### 3. 批量处理能力
- 支持大批量工具的处理
- 进度跟踪和日志记录
- 错误统计和报告

### 4. 数据库安全
- 事务处理确保数据一致性
- 自动回滚机制
- 连接池管理

## 📈 监控和日志

### 日志级别

系统提供详细的日志记录：

```
🔍 [域名服务] 查询没有注册日期的工具，限制数量: 50
📊 [域名服务] 找到 25 个需要更新注册日期的工具
🔄 [处理工具] 开始处理工具: tool_123 - https://example.com
🌐 [处理工具] 提取的域名: example.com
✅ [WHOIS查询] 域名 example.com 注册日期: 2020-01-15 10:30:00
💾 [数据库更新] 更新工具 tool_123 的注册日期: 2020-01-15 10:30:00
✅ [处理工具] 成功更新工具 tool_123 的注册日期: 2020-01-15 10:30:00
```

### 性能指标

每次批量更新后会输出统计信息：

```
🎉 每日域名注册日期更新任务完成:
  - 总工具数: 25
  - 已处理: 25
  - 成功: 20
  - 失败: 5
```

## 🛠️ 故障排查

### 常见问题

1. **WHOIS查询失败**
   - 检查网络连接
   - 某些域名可能有查询限制
   - 查看错误日志了解具体原因

2. **数据库字段不存在**
   ```bash
   # 手动添加字段
   python app/cli/add_domain_registration_field.py
   ```

3. **定时任务未执行**
   - 检查调度器状态
   - 查看应用日志
   - 确认任务是否正确添加

### 调试命令

```bash
# 检查数据库连接
python -c "from app.core.database import test_database_connection; print(test_database_connection())"

# 查看需要更新的工具数量
python -c "
from app.core.database import SessionLocal
from app.services.domain_registration_service import DomainRegistrationService
db = SessionLocal()
service = DomainRegistrationService(db)
tools = service.get_tools_without_registration_date(limit=100)
print(f'需要更新的工具数量: {len(tools)}')
db.close()
"

# 手动执行单次更新
python -c "
from app.services.domain_registration_service import DomainRegistrationService
service = DomainRegistrationService()
result = service.batch_update_registration_dates(batch_size=5, max_tools=5)
print(result)
"
```

## 🔒 安全考虑

1. **WHOIS查询频率限制**
   - 避免过于频繁的查询导致IP被封
   - 批次间添加延迟

2. **数据库事务**
   - 使用事务确保数据一致性
   - 异常时自动回滚

3. **错误处理**
   - 单个工具处理失败不影响其他工具
   - 详细的错误日志记录

## 📝 API接口

虽然主要通过定时任务运行，但也可以通过编程方式调用：

```python
from app.services.domain_registration_service import DomainRegistrationService

# 创建服务实例
service = DomainRegistrationService()

# 批量更新
result = service.batch_update_registration_dates(
    batch_size=10,
    max_tools=50
)

# 处理单个工具
from app.models.tool import Tool
tool = db.query(Tool).filter(Tool.tool_id == "example_tool").first()
success, message = service.process_single_tool(tool)
```

## 🔄 维护建议

1. **定期监控**
   - 检查定时任务执行状态
   - 监控成功率和错误日志

2. **性能优化**
   - 根据实际情况调整批次大小
   - 监控WHOIS查询响应时间

3. **数据清理**
   - 定期检查更新失败的工具
   - 手动处理特殊情况的域名

## 📚 相关文档

- [定时任务文档](./SCHEDULER_README.md)
- [数据库架构文档](./DATABASE_SCHEMA.md)
- [API参考文档](./API_REFERENCE.md)
