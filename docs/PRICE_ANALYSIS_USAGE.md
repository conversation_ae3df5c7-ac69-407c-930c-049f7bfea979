# 域名价格分析工具使用指南

## 概述

`generate_price.py` 是一个独立的域名价格信息分析工具，使用Ollama本地大模型来分析和解析网站的价格信息。

## 主要修复和改进

### 🔧 修复的问题

1. **网络请求健壮性** - 增加了多URL尝试机制和重试逻辑
2. **内容提取优化** - 改进了价格相关内容的识别和提取
3. **JSON解析增强** - 增加了多种JSON解析策略和错误恢复
4. **错误处理完善** - 提供了详细的错误信息和处理建议
5. **进度显示** - 添加了批量处理的进度显示和结果统计

### ✨ 新增功能

1. **测试模式** - 可以测试几个知名网站验证功能
2. **详细结果显示** - 提供了更友好的结果展示格式
3. **内容摘要** - 显示网站内容提取的统计信息
4. **智能重试** - 自动重试失败的请求，提高成功率

## 使用方法

### 1. 环境准备

确保已安装所需依赖：
```bash
pip install -r requirements.txt
```

确保Ollama服务正在运行：
```bash
ollama serve
```

### 2. 功能测试

运行测试脚本验证环境：
```bash
python test_price_analysis.py
```

### 3. 基本用法

#### 分析单个域名
```bash
# 分析单个域名
python generate_price.py --domain openai.com

# 使用特定模型
python generate_price.py --domain stripe.com --model llama3:latest

# 详细输出
python generate_price.py --domain notion.so --verbose
```

#### 批量分析工具
```bash
# 批量分析（默认10个工具）
python generate_price.py --locale en --limit 5

# 干运行模式（仅显示将要处理的工具）
python generate_price.py --dry-run --limit 10

# 指定语言和模型
python generate_price.py --locale zh --model gemma3:latest --limit 20
```

#### 测试模式
```bash
# 运行测试模式，分析几个知名网站
python generate_price.py --test

# 测试并保存结果
python generate_price.py --test --output test_results.json
```

#### 系统状态检查
```bash
# 检查服务状态
python generate_price.py --status

# 列出可用模型
python generate_price.py --list-models
```

### 4. 高级选项

```bash
# 指定Ollama服务器
python generate_price.py --domain example.com --ip-address ************* --port 11434

# 保存结果到文件
python generate_price.py --domain example.com --output result.json

# 启用详细日志
python generate_price.py --domain example.com --verbose
```

## 输出格式

### 单域名分析结果示例

```
=== 域名价格分析结果 ===
域名: openai.com
访问URL: https://openai.com

--- 网站内容摘要 ---
标题: OpenAI
描述: OpenAI is an AI research and deployment company...
内容块数: 5
总内容长度: 12450 字符

--- 价格分析结果 ---
定价模式: 免费增值

定价方案 (3 个):
  1. Free
     价格: 免费
     功能: Basic access, Limited usage

  2. Plus
     价格: $20 / 月付
     功能: Priority access, Faster response
     🌟 推荐方案

  3. Team
     价格: $25 / 月付
     功能: Team collaboration, Admin controls

免费试用: 否

备注: 价格可能根据使用量调整
```

### 批量分析结果示例

```
=== 批量价格分析完成 ===
总工具数: 10
已处理: 10
成功: 8
失败: 2
成功率: 80.0%
使用模型: gemma3:latest

=== 成功分析结果摘要 ===
  1. OpenAI
     域名: openai.com
     摘要: 模式: 免费增值, 方案: 3个(1个免费), 试用: 有

  2. GitHub
     域名: github.com
     摘要: 模式: 免费增值, 方案: 4个(1个免费)
     方案: Free, Pro, Team
```

## 故障排除

### 常见问题

1. **Ollama连接失败**
   ```
   错误: 无法连接到任何Ollama服务器
   解决: 确保Ollama服务正在运行: ollama serve
   ```

2. **数据库连接失败**
   ```
   错误: 数据库连接失败
   解决: 检查.env文件中的数据库配置
   ```

3. **网站内容提取失败**
   ```
   错误: 无法从域名提取内容
   解决: 检查网络连接，某些网站可能有反爬虫机制
   ```

4. **模型不存在**
   ```
   错误: 模型不可用
   解决: 使用 --list-models 查看可用模型，或下载所需模型
   ```

### 调试技巧

1. **启用详细日志**
   ```bash
   python generate_price.py --domain example.com --verbose
   ```

2. **检查服务状态**
   ```bash
   python generate_price.py --status
   ```

3. **运行测试模式**
   ```bash
   python generate_price.py --test
   ```

4. **查看日志文件**
   ```bash
   tail -f generate_price.log
   ```

## 性能优化建议

1. **批量处理时设置合理的限制**
   - 建议每次处理不超过50个工具
   - 使用 `--limit` 参数控制数量

2. **选择合适的模型**
   - 较小的模型响应更快但可能准确性较低
   - 较大的模型更准确但响应较慢

3. **网络优化**
   - 确保网络连接稳定
   - 考虑使用代理处理被屏蔽的网站

4. **资源监控**
   - 监控Ollama服务的资源使用情况
   - 必要时调整并发数量

## 扩展开发

如需扩展功能，可以修改以下部分：

1. **添加新的内容提取策略** - 修改 `ContentExtractorService`
2. **改进价格识别逻辑** - 修改 `_build_price_analysis_prompt`
3. **添加新的输出格式** - 修改结果显示函数
4. **集成其他AI模型** - 扩展 `OllamaService`

## 许可证

本工具遵循项目的整体许可证。