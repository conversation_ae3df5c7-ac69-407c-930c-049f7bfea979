# 🚀 Aistak FastAPI 开发指南

## 📋 项目概述

Aistak FastAPI 是一个基于 FastAPI 框架的 AI 工具管理平台，主要功能包括：
- 🕷️ 工具数据爬取和管理
- 🤖 AI 内容生成（使用 Ollama 本地大模型）
- ⏰ 定时任务调度
- 📊 数据分析和监控
- 🌐 RESTful API 服务

## 🏗️ 项目架构

### 整体架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    FastAPI Application                     │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   main.py       │  │   Routers       │  │   Services   │ │
│  │   (Entry Point) │  │   (API Layer)   │  │   (Business) │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    Core Components                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Database      │  │   Models        │  │   Schemas    │ │
│  │   (SQLAlchemy)  │  │   (ORM)         │  │   (Pydantic) │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                  External Services                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   PostgreSQL    │  │   Ollama AI     │  │   Scheduler  │ │
│  │   Database      │  │   Models        │  │   (APScheduler)│ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 目录结构
```
aistak_fastapi/
├── main.py                 # 🚀 应用入口点
├── requirements.txt        # 📦 依赖管理
├── .env                   # 🔐 环境变量配置
├── app/                   # 📁 主应用目录
│   ├── __init__.py
│   ├── core/              # 🔧 核心配置
│   │   ├── config.py      # ⚙️ 应用配置
│   │   └── database.py    # 🗄️ 数据库配置
│   ├── models/            # 📊 数据模型 (SQLAlchemy ORM)
│   │   ├── tool.py        # 🛠️ 工具相关模型
│   │   └── user.py        # 👤 用户模型
│   ├── schemas/           # 📋 数据验证 (Pydantic)
│   │   └── tool.py        # 🛠️ 工具数据验证
│   ├── routers/           # 🌐 API 路由
│   │   ├── health.py      # ❤️ 健康检查
│   │   ├── users.py       # 👥 用户管理
│   │   ├── crawler.py     # 🕷️ 爬虫管理
│   │   ├── scheduler.py   # ⏰ 定时任务
│   │   └── description_generation.py # 🤖 AI内容生成
│   ├── services/          # 🔧 业务逻辑服务
│   │   ├── database_service.py        # 🗄️ 数据库服务
│   │   ├── ollama_service.py          # 🤖 Ollama AI服务
│   │   ├── content_extractor_service.py # 📄 内容提取
│   │   ├── content_generation_service.py # ✍️ 内容生成
│   │   ├── description_generation_service.py # 📝 描述生成
│   │   ├── scheduler_service.py       # ⏰ 调度服务
│   │   └── toolify_service.py         # 🛠️ 工具服务
│   ├── utils/             # 🔨 工具函数
│   │   ├── data_analyzer.py # 📊 数据分析
│   │   └── domain_utils.py  # 🌐 域名工具
│   ├── crawler/           # 🕷️ 爬虫模块
│   └── cli/               # 💻 命令行工具
│       └── generate_descriptions.py # 🤖 描述生成CLI
├── docs/                  # 📚 文档
├── tests/                 # 🧪 测试文件
└── scripts/               # 📜 管理脚本
    ├── start_services.sh  # 🚀 启动服务
    ├── stop_services.sh   # 🛑 停止服务
    └── system_monitor.sh  # 📊 系统监控
```

## 🚪 应用入口点

### main.py - 应用启动入口

```python
# main.py
from contextlib import asynccontextmanager
from fastapi import FastAPI
from app.routers import users, health, crawler, scheduler, description_generation
from app.core.config import settings

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时处理
    logger.info("应用启动中...")
    
    # 测试数据库连接
    is_connected, message = test_database_connection()
    
    # 启动定时任务调度器
    scheduler = get_scheduler()
    scheduler.start()
    
    yield
    
    # 关闭时处理
    scheduler.stop()
    logger.info("应用正在关闭...")

# 创建FastAPI应用实例
app = FastAPI(
    title=settings.PROJECT_NAME,
    version=settings.VERSION,
    description="AI工具管理平台",
    lifespan=lifespan
)

# 注册路由
app.include_router(users.router, prefix="/api/v1")
app.include_router(health.router, prefix="/api/v1")
app.include_router(crawler.router, prefix="/api/v1")
app.include_router(scheduler.router, prefix="/api/v1")
app.include_router(description_generation.router, prefix="/api/v1")
```

### 启动流程
1. **环境初始化**: 加载配置文件和环境变量
2. **数据库连接**: 测试PostgreSQL数据库连接
3. **调度器启动**: 启动APScheduler定时任务调度器
4. **路由注册**: 注册所有API路由
5. **服务启动**: 启动FastAPI服务器

## 🗄️ 数据库架构

### 数据库配置 (app/core/database.py)

```python
from sqlalchemy import create_engine
from sqlalchemy.orm import declarative_base, sessionmaker

# 创建数据库引擎
engine = create_engine(
    settings.DATABASE_URL,
    pool_pre_ping=True,      # 连接池预检查
    pool_recycle=300,        # 连接回收时间
    echo=settings.DEBUG      # 调试模式显示SQL
)

# 会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# ORM基类
Base = declarative_base()

def get_db():
    """依赖注入：获取数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
```

### 核心数据模型

#### 1. 工具模型 (Tool)
```python
class Tool(Base):
    __tablename__ = 'tools'
    
    id = Column(Integer, primary_key=True)
    tool_id = Column(String(50), unique=True, nullable=False)
    url = Column(Text, nullable=False)
    icon_url = Column(Text)
    pricing_type = Column(String(20))
    is_premium = Column(Boolean, default=False)
    rating = Column(DECIMAL(3, 2))
    api_available = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关系
    translations = relationship("ToolTranslation", back_populates="tool")
    categories = relationship("Category", secondary=tool_categories)
```

#### 2. 工具翻译模型 (ToolTranslation)
```python
class ToolTranslation(Base):
    __tablename__ = 'tool_translations'
    
    id = Column(Integer, primary_key=True)
    tool_id = Column(String(50), ForeignKey('tools.tool_id'))
    locale = Column(String(10), nullable=False)  # 'en', 'zh', etc.
    name = Column(String(200), nullable=False)
    description = Column(Text)
    long_description = Column(Text)           # AI生成的详细描述
    usage_instructions = Column(Text)         # AI生成的使用说明
    pricing_details = Column(Text)            # AI生成的价格详情
    integration_info = Column(Text)           # AI生成的集成信息
    
    # 关系
    tool = relationship("Tool", back_populates="translations")
```

#### 3. 分类模型 (Category)
```python
class Category(Base):
    __tablename__ = 'categories'
    
    id = Column(Integer, primary_key=True)
    slug = Column(String(50), unique=True, nullable=False)
    icon_url = Column(Text)
    
    # 关系
    translations = relationship("CategoryTranslation")
    tools = relationship("Tool", secondary=tool_categories)
```

### 数据库操作模式

#### 依赖注入模式
```python
from fastapi import Depends
from app.core.database import get_db

@router.get("/tools/")
async def get_tools(db: Session = Depends(get_db)):
    """获取工具列表"""
    tools = db.query(Tool).all()
    return tools
```

#### 事务管理
```python
def create_tool_with_translation(db: Session, tool_data: dict):
    """创建工具及其翻译（事务）"""
    try:
        # 创建工具
        tool = Tool(**tool_data['tool'])
        db.add(tool)
        db.flush()  # 获取tool.id
        
        # 创建翻译
        translation = ToolTranslation(
            tool_id=tool.tool_id,
            **tool_data['translation']
        )
        db.add(translation)
        
        db.commit()
        return tool
    except Exception as e:
        db.rollback()
        raise e
```

## 🌐 API 路由架构

### 路由组织结构

#### 1. 健康检查路由 (health.py)
```python
router = APIRouter(prefix="/health", tags=["健康检查"])

@router.get("/")
async def health_check():
    """基本健康检查"""
    return {"status": "healthy"}

@router.get("/database")
async def database_health_check():
    """数据库健康检查"""
    is_connected, message = test_database_connection()
    if not is_connected:
        raise HTTPException(status_code=503, detail=message)
    return {"status": "healthy", "message": message}
```

#### 2. AI内容生成路由 (description_generation.py)
```python
router = APIRouter(prefix="/description-generation", tags=["AI内容生成"])

@router.post("/generate")
async def generate_descriptions(
    request: GenerationRequest,
    service: DescriptionGenerationService = Depends(get_description_service)
):
    """批量生成工具描述"""
    result = service.generate_descriptions(**request.dict())
    return result

@router.get("/status")
async def get_generation_status(locale: str = "en"):
    """获取生成状态统计"""
    return service.get_generation_status(locale)
```

#### 3. 爬虫管理路由 (crawler.py)
```python
router = APIRouter(prefix="/crawler", tags=["爬虫管理"])

@router.post("/crawl")
async def crawl_single_tool(request: CrawlRequest):
    """爬取单个工具"""
    return await crawler_service.crawl_tool(request.url)

@router.post("/batch-crawl")
async def batch_crawl_tools(request: BatchCrawlRequest):
    """批量爬取工具"""
    return await crawler_service.batch_crawl(request.urls)
```

### API 设计原则

#### 1. RESTful 设计
- `GET /api/v1/tools/` - 获取工具列表
- `POST /api/v1/tools/` - 创建新工具
- `GET /api/v1/tools/{tool_id}` - 获取特定工具
- `PUT /api/v1/tools/{tool_id}` - 更新工具
- `DELETE /api/v1/tools/{tool_id}` - 删除工具

#### 2. 统一响应格式
```python
# 成功响应
{
    "success": true,
    "data": {...},
    "message": "操作成功"
}

# 错误响应
{
    "success": false,
    "error": "错误信息",
    "detail": "详细错误描述"
}
```

#### 3. 分页和过滤
```python
@router.get("/tools/")
async def get_tools(
    page: int = Query(1, ge=1),
    size: int = Query(10, ge=1, le=100),
    category: Optional[str] = None,
    search: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """获取工具列表（支持分页和过滤）"""
    query = db.query(Tool)
    
    if category:
        query = query.filter(Tool.categories.any(Category.slug == category))
    
    if search:
        query = query.filter(Tool.translations.any(
            ToolTranslation.name.ilike(f"%{search}%")
        ))
    
    total = query.count()
    tools = query.offset((page - 1) * size).limit(size).all()
    
    return {
        "data": tools,
        "pagination": {
            "page": page,
            "size": size,
            "total": total,
            "pages": (total + size - 1) // size
        }
    }
```

## 🔧 服务层架构

### 服务层设计原则

服务层负责业务逻辑处理，位于路由层和数据层之间：

```
Router Layer (API) → Service Layer (Business Logic) → Model Layer (Data)
```

### 核心服务组件

#### 1. Ollama AI 服务 (ollama_service.py)
```python
class OllamaService:
    """Ollama AI模型服务"""

    def __init__(self, ip_address: str = None, port: int = 11434):
        self.ip_address = ip_address
        self.port = port
        self._find_available_server()

    def call_ollama(self, prompt: str, model: str = "gemma3:latest"):
        """调用Ollama API生成内容"""
        url = f"http://{self.ip_address}:{self.port}/api/generate"
        payload = {
            "model": model,
            "prompt": prompt,
            "stream": False
        }
        response = requests.post(url, json=payload)
        return response.json()

    def list_available_models(self):
        """获取可用模型列表"""
        url = f"http://{self.ip_address}:{self.port}/api/tags"
        response = requests.get(url)
        return response.json().get("models", [])
```

#### 2. 内容生成服务 (content_generation_service.py)
```python
class ContentGenerationService:
    """AI内容生成服务"""

    def __init__(self, db: Session, ollama_service: OllamaService):
        self.db = db
        self.ollama_service = ollama_service
        self.content_extractor = ContentExtractorService()

    def generate_tool_content(self, tool: Tool, field_type: str):
        """为工具生成指定字段内容"""
        # 1. 爬取网站内容
        website_data = self.content_extractor.extract_website_content(tool.url)

        # 2. 构建提示词
        prompt = self._build_prompt(field_type, tool, website_data)

        # 3. 调用AI生成内容
        content = self.ollama_service.call_ollama(prompt)

        return content

    def _build_prompt(self, field_type: str, tool: Tool, website_data: dict):
        """构建不同字段类型的提示词"""
        if field_type == 'long_description':
            return f"""
            Create a comprehensive description for the AI tool "{tool.name}".
            Website content: {website_data['content']}
            Focus on: features, capabilities, target audience, use cases.
            """
        elif field_type == 'pricing_details':
            return f"""
            Extract and organize pricing information for "{tool.name}".
            Website content: {website_data['content']}
            Include: pricing tiers, costs, free trial info.
            """
        # ... 其他字段类型
```

#### 3. 描述生成服务 (description_generation_service.py)
```python
class DescriptionGenerationService:
    """工具描述生成主服务"""

    def __init__(self, db: Session):
        self.db = db
        self.ollama_service = None
        self.content_generation_service = None

    def initialize_services(self, ip_address: str = None):
        """初始化依赖服务"""
        self.ollama_service = OllamaService(ip_address)
        self.content_generation_service = ContentGenerationService(
            self.db, self.ollama_service
        )

    def generate_descriptions(self, locale: str = 'en', limit: int = 10):
        """批量生成工具描述"""
        # 1. 查找需要生成描述的工具
        translations = self._find_tools_to_process(locale, limit)

        # 2. 处理每个工具
        results = []
        for translation in translations:
            try:
                # 生成内容
                content = self.content_generation_service.generate_tool_content(
                    translation.tool, 'long_description'
                )

                # 更新数据库
                translation.long_description = content
                self.db.commit()

                results.append({"tool_id": translation.tool_id, "status": "success"})
            except Exception as e:
                results.append({"tool_id": translation.tool_id, "status": "error", "error": str(e)})

        return {"processed": len(results), "results": results}
```

#### 4. 调度服务 (scheduler_service.py)
```python
from apscheduler.schedulers.asyncio import AsyncIOScheduler

class SchedulerService:
    """定时任务调度服务"""

    def __init__(self):
        self.scheduler = AsyncIOScheduler()

    def start(self):
        """启动调度器"""
        # 添加定时任务
        self.scheduler.add_job(
            func=self.daily_content_generation,
            trigger="cron",
            hour=2,  # 每天凌晨2点执行
            id="daily_generation"
        )

        self.scheduler.start()

    async def daily_content_generation(self):
        """每日内容生成任务"""
        from app.core.database import SessionLocal
        from app.services.description_generation_service import DescriptionGenerationService

        db = SessionLocal()
        try:
            service = DescriptionGenerationService(db)
            service.initialize_services()
            result = service.generate_descriptions(limit=50)
            logger.info(f"每日生成任务完成: {result}")
        finally:
            db.close()
```

### 服务层最佳实践

#### 1. 依赖注入
```python
# 在路由中注入服务
def get_description_service(db: Session = Depends(get_db)):
    return DescriptionGenerationService(db)

@router.post("/generate")
async def generate_descriptions(
    service: DescriptionGenerationService = Depends(get_description_service)
):
    return service.generate_descriptions()
```

#### 2. 错误处理
```python
class ServiceException(Exception):
    """服务层异常基类"""
    pass

class OllamaConnectionError(ServiceException):
    """Ollama连接异常"""
    pass

def handle_service_errors(func):
    """服务错误处理装饰器"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except OllamaConnectionError as e:
            logger.error(f"Ollama连接失败: {e}")
            raise HTTPException(status_code=503, detail="AI服务不可用")
        except Exception as e:
            logger.error(f"服务异常: {e}")
            raise HTTPException(status_code=500, detail="内部服务错误")
    return wrapper
```

#### 3. 配置管理
```python
# app/core/config.py
class Settings(BaseSettings):
    # 数据库配置
    DATABASE_URL: str

    # Ollama配置
    OLLAMA_HOST: str = "localhost"
    OLLAMA_PORT: int = 11434

    # AI生成配置
    DEFAULT_MODEL: str = "gemma3:latest"
    MAX_TOKENS: int = 2048
    TEMPERATURE: float = 0.7

    # 调度器配置
    SCHEDULER_TIMEZONE: str = "Asia/Shanghai"

    class Config:
        env_file = ".env"
```

## 🔄 完整开发流程

### 1. 新功能开发流程

#### 步骤1: 需求分析
- 明确功能需求和业务逻辑
- 设计API接口和数据结构
- 确定涉及的服务和模型

#### 步骤2: 数据模型设计
```python
# 1. 在 app/models/ 中定义新模型
class NewFeature(Base):
    __tablename__ = 'new_features'

    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    # ... 其他字段

# 2. 创建数据库迁移
# alembic revision --autogenerate -m "Add new feature model"
# alembic upgrade head
```

#### 步骤3: 数据验证模式
```python
# app/schemas/new_feature.py
from pydantic import BaseModel

class NewFeatureCreate(BaseModel):
    name: str
    description: Optional[str] = None

class NewFeatureResponse(BaseModel):
    id: int
    name: str
    created_at: datetime

    class Config:
        from_attributes = True
```

#### 步骤4: 服务层实现
```python
# app/services/new_feature_service.py
class NewFeatureService:
    def __init__(self, db: Session):
        self.db = db

    def create_feature(self, feature_data: NewFeatureCreate):
        feature = NewFeature(**feature_data.dict())
        self.db.add(feature)
        self.db.commit()
        self.db.refresh(feature)
        return feature
```

#### 步骤5: API路由实现
```python
# app/routers/new_feature.py
router = APIRouter(prefix="/new-features", tags=["新功能"])

@router.post("/", response_model=NewFeatureResponse)
async def create_feature(
    feature_data: NewFeatureCreate,
    db: Session = Depends(get_db)
):
    service = NewFeatureService(db)
    return service.create_feature(feature_data)
```

#### 步骤6: 注册路由
```python
# main.py
from app.routers import new_feature

app.include_router(new_feature.router, prefix="/api/v1")
```

#### 步骤7: 测试
```python
# tests/test_new_feature.py
def test_create_feature(client, db):
    response = client.post("/api/v1/new-features/", json={
        "name": "Test Feature",
        "description": "Test Description"
    })
    assert response.status_code == 200
    assert response.json()["name"] == "Test Feature"
```

### 2. AI内容生成开发流程

#### 添加新的内容生成字段

1. **更新数据模型**
```python
# app/models/tool.py - 在ToolTranslation中添加新字段
class ToolTranslation(Base):
    # ... 现有字段
    new_ai_field = Column(Text)  # 新的AI生成字段
```

2. **更新提示词模板**
```python
# app/services/content_generation_service.py
def _build_prompt(self, field_type: str, tool: Tool, website_data: dict):
    if field_type == 'new_ai_field':
        return f"""
        Generate {field_type} for the tool "{tool.name}".
        Website content: {website_data['content']}
        Requirements: ...
        """
```

3. **更新API和CLI**
```python
# 在相关的API端点和CLI工具中添加新字段支持
fields_to_generate = ['long_description', 'usage_instructions', 'new_ai_field']
```

### 3. 爬虫功能扩展流程

#### 添加新的数据源

1. **创建爬虫服务**
```python
# app/services/new_crawler_service.py
class NewCrawlerService:
    def crawl_new_source(self, url: str):
        # 实现新数据源的爬取逻辑
        pass
```

2. **添加API端点**
```python
# app/routers/crawler.py
@router.post("/crawl-new-source")
async def crawl_new_source(url: str):
    service = NewCrawlerService()
    return service.crawl_new_source(url)
```

3. **集成到调度器**
```python
# app/services/scheduler_service.py
self.scheduler.add_job(
    func=self.crawl_new_source_job,
    trigger="interval",
    hours=6,  # 每6小时执行一次
    id="new_source_crawl"
)
```

## 🧪 测试策略

### 1. 单元测试
```python
# tests/test_services/test_ollama_service.py
import pytest
from app.services.ollama_service import OllamaService

def test_ollama_service_initialization():
    service = OllamaService()
    assert service.ip_address is not None
    assert service.port == 11434

@pytest.mark.asyncio
async def test_call_ollama():
    service = OllamaService()
    result = service.call_ollama("Hello", "gemma3:latest")
    assert "response" in result
```

### 2. 集成测试
```python
# tests/test_integration/test_content_generation.py
def test_full_content_generation_flow(db_session):
    # 创建测试工具
    tool = Tool(tool_id="test-tool", url="https://example.com")
    db_session.add(tool)
    db_session.commit()

    # 测试内容生成
    service = DescriptionGenerationService(db_session)
    service.initialize_services()
    result = service.generate_descriptions(limit=1)

    assert result["processed"] == 1
```

### 3. API测试
```python
# tests/test_api/test_description_generation.py
def test_generate_descriptions_api(client):
    response = client.post("/api/v1/description-generation/generate", json={
        "locale": "en",
        "limit": 1,
        "dry_run": True
    })
    assert response.status_code == 200
    assert "processed" in response.json()
```

## 🚀 部署和运维

### 1. 开发环境启动
```bash
# 使用管理脚本
./start_services.sh

# 或手动启动
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### 2. 生产环境部署
```bash
# 使用systemd服务
sudo systemctl start aistak-fastapi
sudo systemctl enable aistak-fastapi

# 或使用Docker
docker-compose up -d
```

### 3. 监控和日志
```bash
# 系统监控
./system_monitor.sh

# 查看日志
tail -f app.log
journalctl -u aistak-fastapi -f
```

## 📚 开发最佳实践

### 1. 代码规范
- 使用类型提示 (Type Hints)
- 遵循PEP 8代码风格
- 编写详细的文档字符串
- 使用有意义的变量和函数名

### 2. 错误处理
- 使用自定义异常类
- 提供详细的错误信息
- 记录错误日志
- 优雅降级处理

### 3. 性能优化
- 使用数据库连接池
- 实现适当的缓存策略
- 异步处理长时间任务
- 监控和分析性能指标

### 4. 安全考虑
- 输入验证和清理
- SQL注入防护
- API访问控制
- 敏感信息加密

这个开发指南提供了完整的项目架构说明和开发流程，帮助开发者快速理解和参与项目开发。
