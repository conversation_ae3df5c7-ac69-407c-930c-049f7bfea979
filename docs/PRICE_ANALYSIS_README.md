# 域名价格信息解析功能

## 📋 功能概述

本功能为AI工具管理平台添加了自动分析和解析域名价格信息的能力。系统使用Ollama本地大模型来分析网站内容，提取和解析价格相关信息，包括定价方案、免费试用、订阅模式等。

## 🏗️ 架构设计

### 核心组件

1. **价格分析服务** (`generate_price.py`)
   - 主要的独立脚本，提供完整的价格分析功能
   - 支持单个域名分析和批量工具分析
   - 集成Ollama AI模型进行智能价格信息提取

2. **域名价格分析服务类** (`DomainPriceAnalysisService`)
   - 封装价格分析的核心逻辑
   - 提供网站内容提取和AI分析功能
   - 支持结构化的价格信息输出

3. **内容提取集成** (`ContentExtractorService`)
   - 复用现有的网站内容提取功能
   - 智能识别价格相关的页面元素
   - 支持多块内容的合并处理

## 🚀 功能特性

### 价格信息提取
- **定价方案识别**: 自动识别免费版、基础版、专业版等不同方案
- **价格解析**: 提取具体价格和计费周期（月付、年付等）
- **功能差异分析**: 分析不同价格方案包含的功能差异
- **免费试用检测**: 识别免费试用期和相关限制
- **特殊优惠发现**: 检测折扣、优惠码和促销信息
- **付费模式分类**: 识别订阅制、一次性付费、按使用量计费等模式

### AI分析能力
- **智能内容理解**: 使用Ollama模型理解网站内容语义
- **结构化输出**: 以JSON格式返回结构化的价格信息
- **多语言支持**: 支持中英文等多种语言的价格信息分析
- **容错处理**: 对于无法解析的内容提供降级处理

### 批量处理
- **工具批量分析**: 支持从数据库批量获取工具进行价格分析
- **进度跟踪**: 实时显示处理进度和成功/失败统计
- **错误处理**: 详细的错误日志和异常处理
- **干运行模式**: 支持预览将要处理的工具列表

## 📖 使用方法

### 1. 基本用法

```bash
# 分析单个域名
python generate_price.py --domain openai.com

# 批量分析工具价格（英文）
python generate_price.py --locale en --limit 10

# 批量分析工具价格（中文）
python generate_price.py --locale zh --limit 5

# 干运行模式 - 仅显示将要处理的工具
python generate_price.py --dry-run --limit 10
```

### 2. 高级选项

```bash
# 指定Ollama模型
python generate_price.py --domain example.com --model deepseek-r1:latest

# 指定Ollama服务器地址
python generate_price.py --domain example.com --ip-address ************* --port 11434

# 输出结果到JSON文件
python generate_price.py --domain example.com --output result.json

# 启用详细日志
python generate_price.py --domain example.com --verbose
```

### 3. 服务状态检查

```bash
# 显示服务状态
python generate_price.py --status

# 列出可用的Ollama模型
python generate_price.py --list-models
```

## 📊 输出格式

### JSON结构化输出

```json
{
  "pricing_model": "订阅制",
  "plans": [
    {
      "name": "免费版",
      "price": "$0",
      "billing_cycle": "永久免费",
      "features": ["基础功能", "有限使用"],
      "is_free": true
    },
    {
      "name": "专业版",
      "price": "$20",
      "billing_cycle": "月付",
      "features": ["高级功能", "无限使用", "优先支持"],
      "is_free": false
    }
  ],
  "free_trial": {
    "available": true,
    "duration": "14天",
    "limitations": "功能限制"
  },
  "special_offers": ["年付8折优惠"],
  "currency": "USD",
  "last_updated": "2024-01-01",
  "notes": "价格可能因地区而异"
}
```

### 批量分析结果

```json
{
  "success": true,
  "message": "批量价格分析完成",
  "processed": 10,
  "succeeded": 8,
  "failed": 2,
  "errors": ["工具A: 无法访问网站", "工具B: 解析失败"],
  "results": [
    {
      "tool_id": "tool_001",
      "tool_name": "AI工具A",
      "domain": "example.com",
      "pricing_analysis": { /* 价格分析结果 */ }
    }
  ]
}
```

## 🛠️ 配置要求

### 环境依赖
- Python 3.8+
- Ollama服务（本地或远程）
- 数据库连接（用于批量分析）

### 推荐模型
- `gemma3:latest` - 默认模型，平衡性能和准确性
- `deepseek-r1:latest` - 备用模型，中文支持较好
- `llama3:latest` - 可选模型，英文分析效果好

### 网络要求
- 能够访问目标网站
- Ollama服务可达
- 稳定的网络连接

## 📝 示例代码

### Python API使用

```python
from generate_price import DomainPriceAnalysisService
from app.core.database import SessionLocal

# 创建服务实例
db = SessionLocal()
service = DomainPriceAnalysisService(db)

# 初始化服务
service.initialize_services()

# 分析单个域名
result = service.analyze_domain_pricing("openai.com")
if result["success"]:
    print(result["pricing_analysis"])

# 批量分析
batch_result = service.batch_analyze_tools_pricing(
    locale='en',
    limit=5,
    dry_run=False
)
```

### 命令行使用

```bash
# 完整的分析流程
python generate_price.py --domain github.com --model gemma3:latest --output github_pricing.json --verbose
```

## 🔧 故障排除

### 常见问题

1. **Ollama服务不可用**
   ```bash
   # 检查Ollama是否运行
   curl http://localhost:11434/api/tags
   
   # 启动Ollama服务
   ollama serve
   ```

2. **模型不存在**
   ```bash
   # 下载推荐模型
   ollama pull gemma3:latest
   ollama pull deepseek-r1:latest
   ```

3. **网站无法访问**
   - 检查网络连接
   - 确认目标网站可访问
   - 检查防火墙设置

4. **解析结果不准确**
   - 尝试不同的模型
   - 调整temperature参数
   - 检查网站内容是否包含价格信息

### 日志分析

```bash
# 查看详细日志
tail -f generate_price.log

# 启用调试模式
python generate_price.py --domain example.com --verbose
```

## 🔄 集成建议

### 定时任务集成
可以将价格分析功能集成到现有的调度系统中：

```python
# 在scheduler_service.py中添加
def schedule_price_analysis():
    scheduler.add_job(
        func=run_price_analysis,
        trigger="cron",
        hour=2,  # 每日凌晨2点执行
        minute=0,
        id="price_analysis_job"
    )
```

### API接口扩展
可以为Web界面添加价格分析API：

```python
@router.post("/tools/{tool_id}/analyze-pricing")
async def analyze_tool_pricing(tool_id: str):
    # 调用价格分析服务
    pass
```

## 📈 性能优化

### 批量处理优化
- 合理设置limit参数避免超时
- 使用干运行模式预估处理时间
- 考虑分批处理大量工具

### 模型选择
- 根据语言选择合适的模型
- 平衡准确性和处理速度
- 考虑模型大小和内存使用

### 缓存策略
- 可以考虑缓存分析结果
- 定期更新价格信息
- 避免重复分析相同域名

## 🤝 贡献指南

欢迎提交改进建议和bug报告：

1. 提交Issue描述问题或建议
2. Fork项目并创建功能分支
3. 提交Pull Request

## 📄 许可证

本功能遵循项目的整体许可证协议。
