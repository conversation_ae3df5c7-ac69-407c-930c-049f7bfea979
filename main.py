from contextlib import asynccontextmanager
from fastapi import FastAPI
from app.routers import users, health, crawler, scheduler, description_generation
from app.core.config import settings
from app.core.database import test_database_connection
from app.services.scheduler_service import get_scheduler
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时的处理
    logger.info("应用启动中...")

    # 测试数据库连接
    is_connected, message = test_database_connection()
    if is_connected:
        logger.info(f"✅ {message}")
    else:
        logger.error(f"❌ {message}")
        logger.warning("应用将继续启动，但数据库功能可能不可用")

    # 启动定时任务调度器
    try:
        scheduler = get_scheduler()
        scheduler.start()
        logger.info("✅ 定时任务调度器已启动")
    except Exception as e:
        logger.error(f"❌ 启动定时任务调度器失败: {str(e)}")

    yield

    # 关闭时的处理
    logger.info("应用正在关闭...")

    # 停止定时任务调度器
    try:
        scheduler = get_scheduler()
        scheduler.stop()
        logger.info("✅ 定时任务调度器已停止")
    except Exception as e:
        logger.error(f"❌ 停止定时任务调度器失败: {str(e)}")

app = FastAPI(
    title=settings.PROJECT_NAME,
    version=settings.VERSION,
    description="FastAPI项目",
    lifespan=lifespan
)

# 包含路由
app.include_router(users.router, prefix="/api/v1")
app.include_router(health.router, prefix="/api/v1")
app.include_router(crawler.router, prefix="/api/v1")
app.include_router(scheduler.router, prefix="/api/v1")
app.include_router(description_generation.router, prefix="/api/v1")

@app.get("/")
async def root():
    return {"message": "欢迎使用FastAPI!"}



if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)