#!/usr/bin/env python3
"""
改进的工具描述生成脚本 - 具有更好的数据库超时处理

这个版本包含了以下改进：
1. 更好的数据库连接管理和超时处理
2. 重试机制和错误恢复
3. 批量处理优化
4. 连接健康检查
5. 更详细的日志记录

使用方法:
    python generate_descriptions_robust.py --help
    python generate_descriptions_robust.py --locale en --limit 10
    python generate_descriptions_robust.py --dry-run
"""

import os
import sys
import argparse
import logging
import time
from pathlib import Path
from typing import Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from sqlalchemy.orm import Session
from sqlalchemy.exc import OperationalError, DisconnectionError
from app.core.database import SessionLocal
from app.core.database_config import (
    create_robust_database_session, 
    execute_with_retry,
    DEFAULT_TIMEOUT_CONFIG,
    DEFAULT_OPERATION_CONFIG
)
from app.services.description_generation_service import DescriptionGenerationService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('generate_descriptions_robust.log')
    ]
)
logger = logging.getLogger(__name__)


class RobustDescriptionGenerator:
    """健壮的描述生成器"""
    
    def __init__(self):
        self.db: Optional[Session] = None
        self.service: Optional[DescriptionGenerationService] = None
        self.last_connection_check = 0
        
    def initialize_database(self) -> bool:
        """初始化数据库连接"""
        try:
            logger.info("初始化数据库连接...")
            self.db = create_robust_database_session()
            
            if self.db is None:
                logger.error("无法建立数据库连接")
                return False
                
            logger.info("数据库连接初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"数据库初始化失败: {str(e)}")
            return False
    
    def initialize_services(self, ip_address: Optional[str] = None, port: int = 11434) -> bool:
        """初始化服务"""
        try:
            if not self.db:
                logger.error("数据库未初始化")
                return False
                
            # 创建服务实例
            self.service = DescriptionGenerationService(self.db)
            
            # 初始化Ollama服务
            logger.info("初始化Ollama服务...")
            success = self.service.initialize_services(ip_address, port)
            if not success:
                logger.error("无法连接到Ollama服务，请检查服务是否启动")
                return False
                
            logger.info("服务初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"服务初始化失败: {str(e)}")
            return False
    
    def check_connection_health(self) -> bool:
        """检查连接健康状态"""
        current_time = time.time()
        
        # 每分钟检查一次连接
        if current_time - self.last_connection_check < DEFAULT_OPERATION_CONFIG.connection_refresh_interval:
            return True
            
        try:
            if self.db:
                from sqlalchemy import text
                self.db.execute(text("SELECT 1"))
                self.last_connection_check = current_time
                return True
        except Exception as e:
            logger.warning(f"连接健康检查失败: {str(e)}")
            return False
            
        return False
    
    def refresh_connection_if_needed(self) -> bool:
        """如果需要则刷新连接"""
        if not self.check_connection_health():
            logger.info("尝试刷新数据库连接...")
            try:
                if self.db:
                    self.db.close()
                
                self.db = create_robust_database_session()
                if self.db and self.service:
                    self.service.db = self.db
                    logger.info("数据库连接刷新成功")
                    return True
                    
            except Exception as e:
                logger.error(f"连接刷新失败: {str(e)}")
                return False
                
        return True
    
    def run_generation_with_retry(self, args) -> bool:
        """运行生成任务，包含重试机制"""
        try:
            logger.info("开始执行工具描述生成任务")
            logger.info(f"配置参数: locale={args.locale}, model={args.model}, fields={args.fields}, limit={args.limit}")
            
            def generation_operation():
                # 刷新连接
                if not self.refresh_connection_if_needed():
                    raise Exception("无法刷新数据库连接")
                
                # 执行生成
                return self.service.generate_descriptions(
                    locale=args.locale,
                    model_name=args.model,
                    fields=args.fields,
                    limit=args.limit,
                    dry_run=args.dry_run
                )
            
            # 使用重试机制执行
            result = execute_with_retry(self.db, generation_operation)
            
            # 显示结果
            if result and result["success"]:
                logger.info("任务执行完成")
                logger.info(f"结果: {result['message']}")
                
                if not args.dry_run:
                    logger.info(f"处理: {result['processed']} 个工具")
                    logger.info(f"成功: {result['succeeded']} 个")
                    logger.info(f"失败: {result['failed']} 个")
                    
                    if result["errors"]:
                        logger.warning("错误详情:")
                        for error in result["errors"]:
                            logger.warning(f"  - {error}")
                else:
                    if "tools" in result:
                        logger.info("将要处理的工具:")
                        for tool in result["tools"]:
                            logger.info(f"  - {tool['tool_id']}: {tool['name']}")
                
                return True
            else:
                error_msg = result.get('error', '未知错误') if result else '操作返回空结果'
                logger.error(f"任务执行失败: {error_msg}")
                return False
                
        except Exception as e:
            logger.error(f"执行生成任务时发生错误: {str(e)}")
            return False
    
    def show_status(self, locale: str = 'en'):
        """显示生成状态"""
        try:
            if not self.refresh_connection_if_needed():
                logger.error("无法刷新数据库连接")
                return
                
            logger.info("获取生成状态...")
            status = self.service.get_generation_status(locale)
            
            if status["success"]:
                print(f"\n=== 生成状态统计 (语言: {status['locale']}) ===")
                print(f"总工具数: {status['total_tools']}")
                print(f"Ollama服务状态: {'可用' if status['ollama_service_available'] else '不可用'}")
                print("")
                
                print("字段完成情况:")
                for field, stats in status["field_statistics"].items():
                    print(f"  {field}:")
                    print(f"    已完成: {stats['completed']}")
                    print(f"    待处理: {stats['pending']}")
                    print(f"    完成率: {stats['completion_rate']}%")
                    print("")
            else:
                logger.error(f"获取状态失败: {status.get('error', '未知错误')}")
                
        except Exception as e:
            logger.error(f"显示状态时发生错误: {str(e)}")
    
    def list_models(self):
        """列出可用模型"""
        try:
            if not self.refresh_connection_if_needed():
                logger.error("无法刷新数据库连接")
                return
                
            logger.info("获取可用模型列表...")
            models = self.service.get_available_models()
            
            if models:
                print("\n=== 可用的Ollama模型 ===")
                for model in models:
                    name = model.get('name', 'Unknown')
                    size = model.get('size', 'Unknown size')
                    print(f"  - {name} ({size})")
            else:
                logger.warning("没有找到可用模型或Ollama服务不可用")
                
        except Exception as e:
            logger.error(f"列出模型时发生错误: {str(e)}")
    
    def cleanup(self):
        """清理资源"""
        if self.db:
            try:
                self.db.close()
                logger.info("数据库连接已关闭")
            except Exception as e:
                logger.warning(f"关闭数据库连接时出错: {str(e)}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='使用Ollama本地大模型为工具自动生成详细描述 (改进版)',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 基本用法 - 生成10个工具的长描述
  python generate_descriptions_robust.py --locale en --limit 10
  
  # 干运行模式 - 仅显示将要处理的工具
  python generate_descriptions_robust.py --dry-run
  
  # 生成多个字段
  python generate_descriptions_robust.py --fields long_description,pricing_details --limit 5
  
  # 显示当前状态
  python generate_descriptions_robust.py --status
        """
    )
    
    # 基本参数
    parser.add_argument('--locale', type=str, default='en', help='语言代码，默认为英语(en)')
    parser.add_argument('--model', type=str, default='gemma3:latest', help='使用的Ollama模型名称')
    parser.add_argument('--limit', type=int, default=10, help='每次处理的工具数量上限')
    parser.add_argument('--fields', type=str, default='long_description', 
                       help='要生成的字段，用逗号分隔')
    parser.add_argument('--ip-address', type=str, help='Ollama服务器IP地址')
    parser.add_argument('--port', type=int, default=11434, help='Ollama服务器端口')
    
    # 操作模式
    parser.add_argument('--dry-run', action='store_true', help='仅显示将要处理的工具')
    parser.add_argument('--status', action='store_true', help='显示当前生成状态统计')
    parser.add_argument('--list-models', action='store_true', help='列出可用的Ollama模型')
    parser.add_argument('--verbose', action='store_true', help='启用详细日志输出')
    
    args = parser.parse_args()
    
    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.debug("启用详细日志模式")
    
    # 创建生成器实例
    generator = RobustDescriptionGenerator()
    
    try:
        # 初始化数据库
        if not generator.initialize_database():
            return 1
        
        # 初始化服务
        if not generator.initialize_services(args.ip_address, args.port):
            return 1
        
        # 根据参数执行相应操作
        if args.status:
            generator.show_status(args.locale)
        elif args.list_models:
            generator.list_models()
        else:
            # 执行生成任务
            success = generator.run_generation_with_retry(args)
            if not success:
                return 1
        
        logger.info("程序执行完成")
        return 0
        
    except KeyboardInterrupt:
        logger.info("用户中断程序执行")
        return 1
    except Exception as e:
        logger.error(f"程序执行过程中发生未预期的错误: {str(e)}")
        return 1
    finally:
        generator.cleanup()


if __name__ == '__main__':
    sys.exit(main())
