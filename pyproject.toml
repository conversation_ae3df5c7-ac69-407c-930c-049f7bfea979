[project]
name = "fastapi-project"
version = "1.0.0"
description = "FastAPI项目"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "fastapi>=0.104.1",
    "uvicorn[standard]>=0.24.0",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    "sqlalchemy>=2.0.41",
    "psycopg2-binary>=2.9.10",
    "requests>=2.32.4",
    "python-whois>=0.9.5",
    "beautifulsoup4>=4.13.4",
    "lxml>=6.0.0",
    "python-multipart>=0.0.20",
    "alembic>=1.16.4",
    "apscheduler>=3.11.0",
    "tenacity==8.2.3",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.3",
    "httpx>=0.25.2",
    "pytest-asyncio>=0.21.1",
    "ruff>=0.1.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["app"]



[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
asyncio_mode = "auto"

[tool.ruff]
line-length = 88
target-version = "py312"

[tool.ruff.lint]
select = ["E", "F", "I", "N", "W"]
ignore = ["E501"]

[tool.ruff.format]
quote-style = "double"
indent-style = "space"
