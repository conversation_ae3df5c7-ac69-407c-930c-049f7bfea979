"""
AI工具流量数据分析系统

这是一个完整的AI工具流量数据采集、处理和分析系统，包含以下核心功能：

1. 流量数据采集 - 从Traffic.cv等数据源获取网站流量数据
2. 数据处理和存储 - 智能解析和结构化存储流量数据
3. 域名管理 - 从工具URL中提取和管理域名信息
4. 工具关联 - 解决多工具对应同域名的关联问题
5. 数据分析 - 提供丰富的流量数据分析功能

主要模块：
- core: 核心功能模块
- scripts: 可执行脚本
- docs: 文档
- sql: 数据库脚本
- logs: 日志文件

版本: 1.0.0
作者: AI工具平台团队
创建时间: 2025-07-23
"""

__version__ = "1.0.0"
__author__ = "AI工具平台团队"

# 导入核心模块
from .core.traffic_data_manager import TrafficDataManager, DatabaseConfig
from .core.traffic_cv_spider import TrafficCVSpider
from .core.tools_traffic_sync import ToolsTrafficSyncer

__all__ = [
    'TrafficDataManager',
    'DatabaseConfig', 
    'TrafficCVSpider',
    'ToolsTrafficSyncer'
]
