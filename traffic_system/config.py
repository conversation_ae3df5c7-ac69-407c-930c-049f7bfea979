"""
流量系统配置文件
"""

import os
from dataclasses import dataclass
from typing import Optional


@dataclass
class DatabaseConfig:
    """数据库配置"""
    host: str = "127.0.0.1"
    port: int = 5432
    database: str = "aistak_db"
    user: str = "root"
    password: str = "fuwenhao"
    
    @classmethod
    def from_env(cls) -> 'DatabaseConfig':
        """从环境变量创建配置"""
        return cls(
            host=os.getenv('DB_HOST', '127.0.0.1'),
            port=int(os.getenv('DB_PORT', '5432')),
            database=os.getenv('DB_NAME', 'aistak_db'),
            user=os.getenv('DB_USER', 'root'),
            password=os.getenv('DB_PASSWORD', 'fuwenhao')
        )


@dataclass
class TrafficConfig:
    """流量数据采集配置"""
    # 请求配置
    request_timeout: int = 30
    request_delay: int = 3
    max_retries: int = 3
    
    # 数据源配置
    traffic_cv_base_url: str = "https://traffic.cv"
    user_agent: str = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36"
    
    # 批量处理配置
    batch_size: int = 100
    max_concurrent: int = 5
    
    @classmethod
    def from_env(cls) -> 'TrafficConfig':
        """从环境变量创建配置"""
        return cls(
            request_timeout=int(os.getenv('TRAFFIC_REQUEST_TIMEOUT', '30')),
            request_delay=int(os.getenv('TRAFFIC_REQUEST_DELAY', '3')),
            max_retries=int(os.getenv('TRAFFIC_MAX_RETRIES', '3')),
            batch_size=int(os.getenv('TRAFFIC_BATCH_SIZE', '100')),
            max_concurrent=int(os.getenv('TRAFFIC_MAX_CONCURRENT', '5'))
        )


@dataclass
class LogConfig:
    """日志配置"""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file_path: Optional[str] = None
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5
    
    @classmethod
    def from_env(cls) -> 'LogConfig':
        """从环境变量创建配置"""
        return cls(
            level=os.getenv('LOG_LEVEL', 'INFO'),
            file_path=os.getenv('LOG_FILE_PATH'),
            max_file_size=int(os.getenv('LOG_MAX_FILE_SIZE', str(10 * 1024 * 1024))),
            backup_count=int(os.getenv('LOG_BACKUP_COUNT', '5'))
        )


# 默认配置实例
DEFAULT_DB_CONFIG = DatabaseConfig()
DEFAULT_TRAFFIC_CONFIG = TrafficConfig()
DEFAULT_LOG_CONFIG = LogConfig()
