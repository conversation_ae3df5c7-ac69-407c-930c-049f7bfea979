# AI工具流量数据分析系统

## 🎯 系统概述

这是一个完整的AI工具流量数据采集、处理和分析系统，专门为AI工具平台设计，提供全面的流量数据洞察和分析能力。

### 核心功能
- **流量数据采集**: 从Traffic.cv等数据源自动获取网站流量数据
- **智能数据处理**: 解析和结构化存储流量数据到PostgreSQL数据库
- **域名管理**: 从工具URL中智能提取和管理域名信息
- **工具关联**: 解决多工具对应同域名的关联问题
- **数据分析**: 提供丰富的流量数据分析和统计功能

### 系统特点
- ✅ **完全自动化**: 支持批量处理和定时任务
- ✅ **智能解析**: 处理各种复杂的HTML和数据格式
- ✅ **数据一致性**: 确保流量数据与工具的准确关联
- ✅ **高可扩展性**: 模块化设计，易于扩展新功能
- ✅ **完善监控**: 详细的日志记录和错误处理

## 📁 项目结构

```
traffic_system/
├── __init__.py                 # 包初始化文件
├── config.py                   # 系统配置
├── run.py                      # 统一启动脚本
├── requirements.txt            # 依赖包列表
├── README.md                   # 项目文档
│
├── core/                       # 核心功能模块
│   ├── __init__.py
│   ├── traffic_data_manager.py # 流量数据管理器
│   ├── traffic_cv_spider.py    # Traffic.cv数据采集器
│   └── tools_traffic_sync.py   # 工具流量同步器
│
├── scripts/                    # 可执行脚本
│   ├── main_traffic_processor.py    # 主流量处理脚本
│   ├── sync_tools_traffic.py        # 工具流量同步脚本
│   ├── update_tools_domain.py       # 域名更新脚本
│   ├── init_domain_field.py         # 域名字段初始化脚本
│   ├── fix_missing_domains.py       # 缺失域名修复脚本
│   ├── fix_whois_data.py            # WHOIS数据修复脚本
│   └── debug_extraction.py          # 数据提取调试脚本
│
├── sql/                        # 数据库脚本
│   ├── create_traffic_tables.sql    # 创建流量表
│   ├── add_domain_field.sql         # 添加域名字段
│   └── fix_whois_table.sql          # 修复WHOIS表
│
├── docs/                       # 详细文档
│   ├── TRAFFIC_SYSTEM_README.md     # 流量系统详细说明
│   ├── DOMAIN_EXTRACTION_README.md  # 域名提取系统说明
│   ├── DOMAIN_CONFLICT_RESOLUTION.md # 域名冲突解决策略
│   └── TOOLS_TRAFFIC_SYNC_README.md # 工具流量同步说明
│
└── logs/                       # 日志文件
    ├── tools_traffic_sync_20250723.log
    └── traffic_processor_20250723.log
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装依赖
cd traffic_system
pip install -r requirements.txt

# 配置数据库连接（可选，使用环境变量）
export DB_HOST=127.0.0.1
export DB_PORT=5432
export DB_NAME=aistak_db
export DB_USER=root
export DB_PASSWORD=fuwenhao
```

### 2. 数据库初始化

```bash
# 创建流量数据表
psql -d aistak_db -f sql/create_traffic_tables.sql

# 初始化域名字段
python run.py domain --init
```

### 3. 域名数据准备

```bash
# 更新工具域名信息
python run.py domain --update

# 查看域名统计
python run.py domain --stats
```

### 4. 流量数据同步

```bash
# 查看同步统计
python run.py sync --stats

# 同步指定域名
python run.py sync --domains chatgpt.com github.com

# 批量同步（小规模测试）
python run.py sync --all --limit 10

# 全量同步
python run.py sync --all
```

## 📊 核心功能详解

### 1. 流量数据采集

**TrafficCVSpider** 负责从Traffic.cv获取流量数据：

```python
from traffic_system.core import TrafficCVSpider

spider = TrafficCVSpider()
data = await spider.get_traffic_data("chatgpt.com")
```

**支持的数据类型**:
- 网站概览数据（访问量、跳出率、页面停留时间等）
- 月度趋势数据（最近3个月的访问量变化）
- 流量来源分析（直接访问、搜索引擎、社交媒体等）
- 地区分布数据（主要访问国家和地区）
- 关键词分析（热门搜索关键词和流量）
- WHOIS信息（域名注册信息）

### 2. 数据管理

**TrafficDataManager** 提供完整的数据管理功能：

```python
from traffic_system.core import TrafficDataManager, DatabaseConfig

config = DatabaseConfig()
manager = TrafficDataManager(config)
await manager.process_domain_traffic("example.com")
```

**核心功能**:
- 自动数据解析和清洗
- 结构化数据存储
- 数据去重和更新
- 错误处理和重试机制

### 3. 工具流量同步

**ToolsTrafficSyncer** 解决多工具对应同域名的问题：

```python
from traffic_system.core import ToolsTrafficSyncer

syncer = ToolsTrafficSyncer(config)
await syncer.sync_all_domains(limit=100)
```

**解决方案**:
- 智能主工具选择算法
- 域名-工具映射表管理
- 数据一致性保证
- 批量同步处理

## 🛠️ 使用指南

### 命令行工具

系统提供统一的命令行接口 `run.py`：

```bash
# 流量数据处理
python run.py traffic --domain chatgpt.com
python run.py traffic --batch --limit 10

# 域名管理
python run.py domain --init
python run.py domain --update --dry-run
python run.py domain --stats

# 工具流量同步
python run.py sync --stats
python run.py sync --mapping
python run.py sync --domains chatgpt.com github.com
python run.py sync --all --limit 10 --force

# 数据修复
python run.py fix --whois
python run.py fix --domains
```

### 直接脚本调用

也可以直接调用具体脚本：

```bash
# 流量数据处理
cd scripts
python main_traffic_processor.py --domain chatgpt.com

# 工具流量同步
python sync_tools_traffic.py --sync-all --limit 10

# 域名更新
python update_tools_domain.py --dry-run
```

## 📈 数据库设计

### 核心数据表

1. **traffic_site_overview** - 网站概览数据
2. **traffic_monthly_trends** - 月度趋势数据
3. **traffic_source_analysis** - 流量来源分析
4. **traffic_region_distribution** - 地区分布数据
5. **traffic_keyword_analysis** - 关键词分析
6. **traffic_domain_whois** - WHOIS信息
7. **traffic_extraction_logs** - 提取日志
8. **domain_tool_mapping** - 域名工具映射

### 数据关联

```sql
-- 通过域名关联流量数据
SELECT t.tool_id, t.domain, tso.total_visits, tso.bounce_rate
FROM tools t
JOIN traffic_site_overview tso ON t.domain = tso.domain;

-- 通过映射表查询工具关联
SELECT dtm.primary_tool_id, dtm.related_tool_ids, tso.*
FROM domain_tool_mapping dtm
JOIN traffic_site_overview tso ON dtm.domain = tso.domain;
```

## 🔧 配置管理

### 数据库配置

```python
from traffic_system.config import DatabaseConfig

# 默认配置
config = DatabaseConfig()

# 从环境变量
config = DatabaseConfig.from_env()

# 自定义配置
config = DatabaseConfig(
    host="localhost",
    port=5432,
    database="my_db",
    user="user",
    password="password"
)
```

### 流量采集配置

```python
from traffic_system.config import TrafficConfig

config = TrafficConfig(
    request_timeout=30,
    request_delay=3,
    batch_size=100,
    max_concurrent=5
)
```

## 📝 开发指南

### 添加新的数据源

1. 继承 `TrafficCVSpider` 类
2. 实现数据提取方法
3. 更新数据管理器
4. 添加相应的测试

### 扩展数据分析功能

1. 在 `TrafficDataManager` 中添加新方法
2. 创建相应的数据库表
3. 更新文档和测试

### 自定义同步策略

1. 修改 `ToolsTrafficSyncer` 的选择算法
2. 更新映射关系管理
3. 测试数据一致性

## 🧪 测试

```bash
# 运行所有测试
pytest

# 运行特定测试
pytest tests/test_traffic_data_manager.py

# 生成覆盖率报告
pytest --cov=traffic_system --cov-report=html
```

## 📊 监控和日志

### 日志配置

```python
from traffic_system.config import LogConfig

log_config = LogConfig(
    level="INFO",
    file_path="logs/traffic_system.log",
    max_file_size=10*1024*1024,  # 10MB
    backup_count=5
)
```

### 监控指标

- 数据采集成功率
- 处理速度和延迟
- 数据库连接状态
- 错误率和异常情况

## 🚀 部署建议

### 生产环境

1. **数据库优化**: 配置连接池和索引
2. **定时任务**: 使用cron或类似工具定期同步
3. **监控告警**: 配置日志监控和异常告警
4. **备份策略**: 定期备份数据和配置

### 性能优化

1. **批量处理**: 使用合适的批次大小
2. **并发控制**: 避免过多并发请求
3. **缓存策略**: 缓存频繁查询的数据
4. **数据库调优**: 优化查询和索引

## 📞 支持和贡献

### 问题反馈

如遇到问题，请提供：
- 详细的错误信息
- 相关的日志文件
- 系统环境信息
- 重现步骤

### 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交代码和测试
4. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证，详见LICENSE文件。

---

**版本**: 1.0.0  
**最后更新**: 2025-07-23  
**维护团队**: AI工具平台团队
