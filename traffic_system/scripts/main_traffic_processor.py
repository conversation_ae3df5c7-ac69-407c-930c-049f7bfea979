#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
流量数据处理主程序
提供命令行接口和批量处理功能
"""

import asyncio
import argparse
import json
import sys
import os
from datetime import datetime
from typing import List
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from traffic_system.core.traffic_data_manager import TrafficDataManager, DatabaseConfig
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(f'traffic_processor_{datetime.now().strftime("%Y%m%d")}.log')
    ]
)
logger = logging.getLogger(__name__)


def parse_database_url(database_url: str) -> DatabaseConfig:
    """解析数据库URL并创建配置"""
    import re

    # 解析 postgresql://user:password@host:port/database?options
    pattern = r'postgresql://([^:]+):([^@]+)@([^:]+):(\d+)/([^?]+)'
    match = re.match(pattern, database_url)

    if match:
        user, password, host, port, database = match.groups()
        return DatabaseConfig(
            host=host,
            port=int(port),
            database=database,
            user=user,
            password=password
        )
    else:
        raise ValueError(f"无法解析数据库URL: {database_url}")


def get_database_config(args) -> DatabaseConfig:
    """获取数据库配置，优先使用环境变量"""
    # 首先检查是否有DATABASE_URL环境变量
    database_url = os.getenv('DATABASE_URL')
    if database_url:
        logger.info("使用环境变量DATABASE_URL中的数据库配置")
        return parse_database_url(database_url)

    # 否则使用命令行参数或默认值
    return DatabaseConfig(
        host=args.db_host,
        port=args.db_port,
        database=args.db_name,
        user=args.db_user,
        password=args.db_password
    )


async def process_single_domain(domain: str, db_config: DatabaseConfig):
    """处理单个域名"""
    manager = TrafficDataManager(db_config)
    
    try:
        await manager.init_db_pool()
        result = await manager.process_domain_traffic(domain)
        
        print("\n" + "="*60)
        print(f"域名处理结果: {domain}")
        print("="*60)
        
        if result.get('success', False):
            print(f"✅ 处理成功")
            print(f"   Tool ID: {result.get('tool_id')}")
            print(f"   数据点数: {result.get('data_points')}")
            print(f"   响应时间: {result.get('response_time_ms')}ms")
            
            # 获取并显示摘要
            summary = await manager.get_traffic_summary(result.get('tool_id'))
            print_traffic_summary(summary)
        else:
            print(f"❌ 处理失败: {result.get('error')}")
        
        return result
        
    except Exception as e:
        logger.error(f"处理域名失败: {e}")
        return {"success": False, "error": str(e)}
    finally:
        await manager.close_db_pool()


async def process_multiple_domains(domains: List[str], db_config: DatabaseConfig):
    """处理多个域名"""
    manager = TrafficDataManager(db_config)
    
    try:
        await manager.init_db_pool()
        results = await manager.batch_process_domains(domains)
        
        print("\n" + "="*60)
        print(f"批量处理结果: {len(domains)} 个域名")
        print("="*60)
        
        successful = 0
        failed = 0
        
        for result in results:
            domain = result.get('domain', 'Unknown')
            if result.get('success', False):
                successful += 1
                print(f"✅ {domain} - {result.get('data_points', 0)} 数据点")
            else:
                failed += 1
                print(f"❌ {domain} - {result.get('error', 'Unknown error')}")
        
        print(f"\n总结: {successful} 成功, {failed} 失败")
        
        return results
        
    except Exception as e:
        logger.error(f"批量处理失败: {e}")
        return []
    finally:
        await manager.close_db_pool()


def print_traffic_summary(summary: dict):
    """打印流量数据摘要"""
    if summary.get('error'):
        print(f"❌ 获取摘要失败: {summary['error']}")
        return
    
    overview = summary.get('overview')
    if overview:
        print(f"\n📊 流量概览:")
        print(f"   域名: {overview.get('domain', 'N/A')}")
        print(f"   全球排名: #{overview.get('global_rank', 'N/A')}")
        print(f"   总访问量: {overview.get('total_visits_raw', 'N/A')} ({overview.get('visits_change_percent', 'N/A')}%)")
        print(f"   跳出率: {overview.get('bounce_rate', 'N/A')}%")
        print(f"   平均停留时间: {overview.get('avg_duration_raw', 'N/A')}")
    
    trends = summary.get('trends', [])
    if trends:
        print(f"\n📈 访问量趋势:")
        for trend in trends[:3]:
            growth = trend.get('growth_rate')
            growth_str = f" ({growth:+.2f}%)" if growth else ""
            print(f"   {trend.get('period')}: {trend.get('visits_raw')}{growth_str}")
    
    sources = summary.get('sources', [])
    if sources:
        print(f"\n🔗 流量来源:")
        for source in sources[:5]:
            print(f"   {source.get('source_type')}: {source.get('traffic_percent_raw')}")
    
    regions = summary.get('regions', [])
    if regions:
        print(f"\n🌍 热门地区:")
        for region in regions[:3]:
            print(f"   {region.get('region_name')}: {region.get('traffic_percent_raw')}")
    
    keywords = summary.get('keywords', [])
    if keywords:
        print(f"\n🔍 热门关键词:")
        for keyword in keywords[:3]:
            visits = keyword.get('estimated_visits', 0)
            visits_str = format_number(visits) if visits > 0 else 'N/A'
            print(f"   {keyword.get('keyword')}: {visits_str} 访问量")


def format_number(num: int) -> str:
    """格式化数字显示"""
    if num >= 1_000_000:
        return f"{num / 1_000_000:.1f}M"
    elif num >= 1_000:
        return f"{num / 1_000:.1f}K"
    else:
        return str(num)


def load_domains_from_file(filepath: str) -> List[str]:
    """从文件加载域名列表"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            domains = []
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    domains.append(line)
            return domains
    except Exception as e:
        logger.error(f"加载域名文件失败: {e}")
        return []


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='流量数据处理工具')
    parser.add_argument('--domain', '-d', type=str, help='单个域名')
    parser.add_argument('--domains', '-D', type=str, nargs='+', help='多个域名')
    parser.add_argument('--file', '-f', type=str, help='域名列表文件')
    parser.add_argument('--output', '-o', type=str, help='输出结果到JSON文件')
    
    # 数据库配置参数
    parser.add_argument('--db-host', default='127.0.0.1', help='数据库主机')
    parser.add_argument('--db-port', type=int, default=5432, help='数据库端口')
    parser.add_argument('--db-name', default='aistak_db', help='数据库名称')
    parser.add_argument('--db-user', default='root', help='数据库用户')
    parser.add_argument('--db-password', default='fuwenhao', help='数据库密码')
    
    args = parser.parse_args()

    # 创建数据库配置（优先使用环境变量）
    try:
        db_config = get_database_config(args)
        logger.info(f"数据库配置: {db_config.user}@{db_config.host}:{db_config.port}/{db_config.database}")
    except Exception as e:
        logger.error(f"数据库配置错误: {e}")
        return
    
    # 确定要处理的域名
    domains = []
    
    if args.domain:
        domains = [args.domain]
    elif args.domains:
        domains = args.domains
    elif args.file:
        domains = load_domains_from_file(args.file)
    else:
        print("请指定要处理的域名:")
        print("  --domain DOMAIN        处理单个域名")
        print("  --domains D1 D2 D3     处理多个域名")
        print("  --file FILE            从文件加载域名列表")
        return
    
    if not domains:
        print("❌ 没有找到要处理的域名")
        return
    
    print(f"🚀 开始处理 {len(domains)} 个域名...")
    
    # 处理域名
    if len(domains) == 1:
        results = [await process_single_domain(domains[0], db_config)]
    else:
        results = await process_multiple_domains(domains, db_config)
    
    # 保存结果到文件
    if args.output:
        try:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump({
                    'timestamp': datetime.now().isoformat(),
                    'total_domains': len(domains),
                    'successful': sum(1 for r in results if r.get('success', False)),
                    'results': results
                }, f, indent=2, ensure_ascii=False, default=str)
            print(f"✅ 结果已保存到: {args.output}")
        except Exception as e:
            logger.error(f"保存结果文件失败: {e}")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        sys.exit(1)
