#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试数据提取问题
"""

import re
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from traffic_system.core.traffic_cv_spider import TrafficCVSpider
from bs4 import BeautifulSoup


def debug_regions_keywords():
    """调试地区和关键词提取"""
    spider = TrafficCVSpider()
    
    # 获取原始数据
    result = spider.get_domain_data('klingai.com')
    
    if not result.get('success'):
        print("❌ 获取数据失败")
        return
    
    # 重新获取HTML内容进行调试
    response = spider.session.get("https://traffic.cv/klingai.com", timeout=30, verify=False)
    soup = BeautifulSoup(response.text, 'html.parser')
    page_text = soup.get_text()
    
    print("🔍 调试地区数据提取")
    print("=" * 50)
    
    # 查找包含地区信息的部分
    if 'Top Regions' in page_text:
        print("✅ 找到 'Top Regions' 文本")
        
        # 查找Top Regions部分
        regions_section = re.search(r'Top Regions.*?(?=Top Keywords|Whois Information|$)', page_text, re.DOTALL)
        if regions_section:
            region_text = regions_section.group(0)
            print(f"地区部分文本 (前500字符):\n{region_text[:500]}")
            
            # 尝试不同的正则表达式
            patterns = [
                r'([A-Za-z\s,]+)\s+([0-9.]+%)',  # 原始模式
                r'([A-Za-z\s,]+?)\s+([0-9.]+%)',  # 非贪婪匹配
                r'([A-Za-z\s,]{3,})\s+([0-9.]+%)',  # 至少3个字符
                r'([A-Za-z\s,]+)\s*([0-9.]+%)',  # 可选空格
            ]
            
            for i, pattern in enumerate(patterns):
                matches = re.findall(pattern, region_text)
                print(f"\n模式 {i+1}: {pattern}")
                print(f"匹配结果: {matches[:3]}")  # 只显示前3个
        else:
            print("❌ 未找到地区部分")
    else:
        print("❌ 页面中没有 'Top Regions' 文本")
    
    print("\n🔍 调试关键词数据提取")
    print("=" * 50)
    
    # 查找包含关键词信息的部分
    if 'Top Keywords' in page_text:
        print("✅ 找到 'Top Keywords' 文本")
        
        # 查找Top Keywords部分
        keywords_section = re.search(r'Top Keywords.*?(?=Whois Information|$)', page_text, re.DOTALL)
        if keywords_section:
            keyword_text = keywords_section.group(0)
            print(f"关键词部分文本 (前500字符):\n{keyword_text[:500]}")
            
            # 尝试不同的正则表达式
            patterns = [
                r'([^\n]+?)\s+([0-9.]+[KMB]?)\s+([0-9.]+[KMB]?)\s+\$([0-9.]+)',  # 原始模式
                r'([^\n\t]+?)\s+([0-9.]+[KMB]?)\s+([0-9.]+[KMB]?)\s+\$([0-9.]+)',  # 排除制表符
                r'([a-zA-Z\s]+?)\s+([0-9.]+[KMB]?)\s+([0-9.]+[KMB]?)\s+\$([0-9.]+)',  # 只匹配字母和空格
                r'([^0-9\n]+?)\s+([0-9.]+[KMB]?)\s+([0-9.]+[KMB]?)\s+\$([0-9.]+)',  # 排除数字开头
            ]
            
            for i, pattern in enumerate(patterns):
                matches = re.findall(pattern, keyword_text)
                print(f"\n模式 {i+1}: {pattern}")
                print(f"匹配结果: {matches[:3]}")  # 只显示前3个
        else:
            print("❌ 未找到关键词部分")
    else:
        print("❌ 页面中没有 'Top Keywords' 文本")
    
    # 查找所有可能的数据模式
    print("\n🔍 查找页面中的百分比数据")
    print("=" * 50)
    
    # 查找所有百分比
    percentages = re.findall(r'([A-Za-z\s,]+?)\s+([0-9.]+%)', page_text)
    print(f"找到 {len(percentages)} 个百分比数据:")
    for i, (text, percent) in enumerate(percentages[:10]):  # 只显示前10个
        print(f"  {i+1}. '{text.strip()}' -> {percent}")
    
    print("\n🔍 查找页面中的流量数据")
    print("=" * 50)
    
    # 查找所有流量数据
    traffic_data = re.findall(r'([a-zA-Z\s]+?)\s+([0-9.]+[KMB])\s+([0-9.]+[KMB])', page_text)
    print(f"找到 {len(traffic_data)} 个流量数据:")
    for i, (keyword, traffic, volume) in enumerate(traffic_data[:10]):  # 只显示前10个
        print(f"  {i+1}. '{keyword.strip()}' -> {traffic} / {volume}")


if __name__ == "__main__":
    debug_regions_keywords()
