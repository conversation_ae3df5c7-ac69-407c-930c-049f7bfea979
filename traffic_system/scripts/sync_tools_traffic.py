#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工具流量数据同步主程序
提供命令行接口进行工具流量数据同步
"""

import asyncio
import argparse
import json
import sys
from datetime import datetime
from typing import List
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from traffic_system.core.tools_traffic_sync import ToolsTrafficSyncer
from traffic_system.core.traffic_data_manager import DatabaseConfig
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(f'tools_traffic_sync_{datetime.now().strftime("%Y%m%d")}.log')
    ]
)
logger = logging.getLogger(__name__)


async def show_statistics(syncer: ToolsTrafficSyncer):
    """显示同步统计信息"""
    print("\n" + "="*60)
    print("工具流量数据同步统计")
    print("="*60)
    
    stats = await syncer.get_sync_statistics()
    
    print(f"📊 基础统计:")
    print(f"   总工具数: {stats['total_tools']}")
    print(f"   唯一域名数: {stats['unique_domains']}")
    print(f"   已有流量数据域名: {stats['traffic_domains']}")
    print(f"   已映射域名数: {stats['mapped_domains']}")
    print(f"   流量数据覆盖率: {stats['coverage_percentage']}%")
    
    if stats['duplicate_domains']:
        print(f"\n🔄 重复域名情况 (前5):")
        for i, domain_info in enumerate(stats['duplicate_domains'], 1):
            print(f"   {i}. {domain_info['domain']} ({domain_info['tool_count']} 个工具)")


async def sync_specific_domains(syncer: ToolsTrafficSyncer, domains: List[str]):
    """同步指定域名的流量数据"""
    print(f"\n🎯 同步指定域名: {', '.join(domains)}")
    print("-" * 60)
    
    # 获取域名映射
    domain_mapping = await syncer.get_domain_tool_mapping()
    
    results = []
    for domain in domains:
        if domain not in domain_mapping:
            print(f"❌ 域名不存在: {domain}")
            continue
        
        tools = domain_mapping[domain]
        primary_tool = syncer.select_primary_tool(tools)
        primary_tool_id = primary_tool['tool_id']
        
        print(f"\n处理域名: {domain}")
        print(f"  主要工具: {primary_tool_id}")
        print(f"  关联工具数: {len(tools)}")
        
        # 创建映射关系
        await syncer.create_domain_tool_mapping(
            domain, primary_tool_id, 
            [tool['tool_id'] for tool in tools]
        )
        
        # 同步流量数据
        result = await syncer.sync_traffic_data_for_domain(domain, primary_tool_id)
        results.append(result)
        
        if result['success']:
            print(f"  ✅ 同步成功: {result.get('data_points', 0)} 数据点")
        else:
            print(f"  ❌ 同步失败: {result.get('error', 'Unknown error')}")
        
        # 添加延迟
        await asyncio.sleep(2)
    
    return results


async def sync_all_domains(syncer: ToolsTrafficSyncer, limit: int = None, 
                         skip_existing: bool = True):
    """同步所有域名的流量数据"""
    print(f"\n🚀 开始同步所有域名的流量数据")
    if limit:
        print(f"   限制处理数量: {limit}")
    if skip_existing:
        print(f"   跳过已有数据: 是")
    print("-" * 60)
    
    start_time = datetime.now()
    stats = await syncer.sync_all_domains(limit=limit, skip_existing=skip_existing)
    end_time = datetime.now()
    
    # 显示结果
    print(f"\n📈 同步完成! 耗时: {(end_time - start_time).total_seconds():.2f}秒")
    print(f"   总域名数: {stats['total_domains']}")
    print(f"   已处理: {stats['processed']}")
    print(f"   成功: {stats['successful']}")
    print(f"   失败: {stats['failed']}")
    print(f"   跳过: {stats['skipped']}")
    
    if stats['errors']:
        print(f"\n❌ 错误信息 (前5条):")
        for error in stats['errors'][:5]:
            print(f"   - {error}")
    
    return stats


async def show_domain_mapping(syncer: ToolsTrafficSyncer, limit: int = 10):
    """显示域名映射关系"""
    print(f"\n🗺️ 域名-工具映射关系 (前{limit}条)")
    print("-" * 60)
    
    domain_mapping = await syncer.get_domain_tool_mapping()
    
    # 按工具数量排序
    sorted_domains = sorted(
        domain_mapping.items(), 
        key=lambda x: len(x[1]), 
        reverse=True
    )
    
    for i, (domain, tools) in enumerate(sorted_domains[:limit], 1):
        primary_tool = syncer.select_primary_tool(tools)
        print(f"{i:2d}. {domain}")
        print(f"    主要工具: {primary_tool['tool_id']}")
        print(f"    工具总数: {len(tools)}")
        if len(tools) > 1:
            other_tools = [t['tool_id'] for t in tools if t['tool_id'] != primary_tool['tool_id']]
            print(f"    其他工具: {', '.join(other_tools[:3])}" + 
                  (f" (+{len(other_tools)-3}个)" if len(other_tools) > 3 else ""))
        print()


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='工具流量数据同步脚本')
    parser.add_argument('--stats', action='store_true', help='显示同步统计信息')
    parser.add_argument('--mapping', action='store_true', help='显示域名映射关系')
    parser.add_argument('--sync-all', action='store_true', help='同步所有域名的流量数据')
    parser.add_argument('--sync-domains', nargs='+', help='同步指定域名的流量数据')
    parser.add_argument('--limit', type=int, help='限制处理的域名数量')
    parser.add_argument('--force', action='store_true', help='强制更新已有数据')
    parser.add_argument('--output', '-o', type=str, help='输出结果到JSON文件')
    
    # 数据库配置参数
    parser.add_argument('--db-host', default='127.0.0.1', help='数据库主机')
    parser.add_argument('--db-port', type=int, default=5432, help='数据库端口')
    parser.add_argument('--db-name', default='aistak_db', help='数据库名称')
    parser.add_argument('--db-user', default='root', help='数据库用户')
    parser.add_argument('--db-password', default='fuwenhao', help='数据库密码')
    
    args = parser.parse_args()
    
    # 创建数据库配置
    db_config = DatabaseConfig(
        host=args.db_host,
        port=args.db_port,
        database=args.db_name,
        user=args.db_user,
        password=args.db_password
    )
    
    # 创建同步器
    syncer = ToolsTrafficSyncer(db_config)
    
    try:
        await syncer.init_db_pool()
        
        result = None
        
        if args.stats:
            await show_statistics(syncer)
        
        elif args.mapping:
            await show_domain_mapping(syncer, limit=args.limit or 10)
        
        elif args.sync_domains:
            result = await sync_specific_domains(syncer, args.sync_domains)
        
        elif args.sync_all:
            result = await sync_all_domains(
                syncer, 
                limit=args.limit, 
                skip_existing=not args.force
            )
        
        else:
            print("请指定操作:")
            print("  --stats          显示统计信息")
            print("  --mapping        显示域名映射关系")
            print("  --sync-all       同步所有域名")
            print("  --sync-domains   同步指定域名")
            print("\n示例:")
            print("  python sync_tools_traffic.py --stats")
            print("  python sync_tools_traffic.py --mapping")
            print("  python sync_tools_traffic.py --sync-all --limit 10")
            print("  python sync_tools_traffic.py --sync-domains chatgpt.com github.com")
            return
        
        # 保存结果到文件
        if args.output and result:
            try:
                with open(args.output, 'w', encoding='utf-8') as f:
                    json.dump({
                        'timestamp': datetime.now().isoformat(),
                        'operation': 'sync_domains' if args.sync_domains else 'sync_all',
                        'result': result
                    }, f, indent=2, ensure_ascii=False, default=str)
                print(f"✅ 结果已保存到: {args.output}")
            except Exception as e:
                logger.error(f"保存结果文件失败: {e}")
    
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        return 1
    
    finally:
        await syncer.close_db_pool()
    
    return 0


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
        exit(1)
    except Exception as e:
        logger.error(f"程序异常退出: {e}")
        exit(1)
