#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复WHOIS数据的脚本
"""

import asyncio
import re
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from traffic_system.core.traffic_data_manager import TrafficDataManager, DatabaseConfig


async def fix_whois_data():
    """修复WHOIS数据"""
    db_config = DatabaseConfig()
    manager = TrafficDataManager(db_config)
    
    try:
        await manager.init_db_pool()
        
        async with manager.db_pool.acquire() as conn:
            # 获取所有需要修复的WHOIS记录
            records = await conn.fetch("""
                SELECT tool_id, domain, registrar, name_servers, whois_raw_text
                FROM traffic_domain_whois
                WHERE registrar = '' OR registrar IS NULL
            """)
            
            print(f"找到 {len(records)} 条需要修复的记录")
            
            for record in records:
                tool_id = record['tool_id']
                domain = record['domain']
                raw_text = record['whois_raw_text']
                
                print(f"\n修复 {domain} 的WHOIS数据...")
                
                # 从原始文本中提取注册商信息
                registrar = ""
                name_servers = []
                
                if raw_text:
                    # 解析JSON中的注册商信息
                    if 'MarkMonitor' in raw_text:
                        registrar = 'MarkMonitor Inc.'
                    elif 'GoDaddy' in raw_text:
                        registrar = 'GoDaddy.com, LLC'
                    elif 'Namecheap' in raw_text:
                        registrar = 'Namecheap, Inc.'
                    elif 'Cloudflare' in raw_text:
                        registrar = 'Cloudflare, Inc.'
                
                # 修复名称服务器
                current_name_servers = record['name_servers']
                if current_name_servers and len(current_name_servers) > 0:
                    # 如果名称服务器是连在一起的，分割它们
                    servers_text = current_name_servers[0]
                    if 'CLOUDFLARE.COM' in servers_text:
                        # 分割Cloudflare服务器
                        servers = re.findall(r'[A-Z]+\.NS\.CLOUDFLARE\.COM', servers_text)
                        if servers:
                            name_servers = servers
                        else:
                            # 手动分割
                            if 'HASSAN.NS.CLOUDFLARE.COMSAVANNA.NS.CLOUDFLARE.COM' in servers_text:
                                name_servers = ['HASSAN.NS.CLOUDFLARE.COM', 'SAVANNA.NS.CLOUDFLARE.COM']
                
                # 更新数据库
                if registrar or name_servers:
                    await conn.execute("""
                        UPDATE traffic_domain_whois 
                        SET 
                            registrar = COALESCE($1, registrar),
                            name_servers = COALESCE($2, name_servers),
                            updated_at = CURRENT_TIMESTAMP
                        WHERE tool_id = $3
                    """, registrar or None, name_servers or None, tool_id)
                    
                    print(f"✅ 更新成功:")
                    if registrar:
                        print(f"   注册商: {registrar}")
                    if name_servers:
                        print(f"   名称服务器: {name_servers}")
                else:
                    print("⚠️ 未找到可提取的信息")
        
        print(f"\n🎉 WHOIS数据修复完成!")
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
    finally:
        await manager.close_db_pool()


async def main():
    """主函数"""
    print("🔧 开始修复WHOIS数据...")
    await fix_whois_data()


if __name__ == "__main__":
    asyncio.run(main())
