#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
初始化domain字段脚本
为tools表添加domain字段并创建索引
"""

import asyncio
import asyncpg
import logging
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from traffic_system.core.traffic_data_manager import DatabaseConfig

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


async def init_domain_field():
    """初始化domain字段"""
    db_config = DatabaseConfig()
    
    try:
        # 创建数据库连接
        conn = await asyncpg.connect(
            host=db_config.host,
            port=db_config.port,
            database=db_config.database,
            user=db_config.user,
            password=db_config.password
        )
        
        logger.info("数据库连接成功")
        
        # 1. 检查domain字段是否已存在
        field_exists = await conn.fetchval("""
            SELECT COUNT(*) 
            FROM information_schema.columns 
            WHERE table_name = 'tools' AND column_name = 'domain'
        """)
        
        if field_exists > 0:
            logger.info("domain字段已存在，跳过创建")
        else:
            # 2. 添加domain字段
            logger.info("添加domain字段...")
            await conn.execute("ALTER TABLE tools ADD COLUMN domain VARCHAR(255)")
            logger.info("✅ domain字段添加成功")
        
        # 3. 检查索引是否已存在
        index_exists = await conn.fetchval("""
            SELECT COUNT(*) 
            FROM pg_indexes 
            WHERE tablename = 'tools' AND indexname = 'idx_tools_domain'
        """)
        
        if index_exists > 0:
            logger.info("domain索引已存在，跳过创建")
        else:
            # 4. 创建索引
            logger.info("创建domain字段索引...")
            await conn.execute("CREATE INDEX idx_tools_domain ON tools(domain)")
            logger.info("✅ domain索引创建成功")
        
        # 5. 添加字段注释
        await conn.execute("""
            COMMENT ON COLUMN tools.domain IS '从url字段解析出的域名，如: chatgpt.com, github.com'
        """)
        logger.info("✅ 字段注释添加成功")
        
        # 6. 验证字段创建
        field_info = await conn.fetchrow("""
            SELECT column_name, data_type, character_maximum_length, is_nullable 
            FROM information_schema.columns 
            WHERE table_name = 'tools' AND column_name = 'domain'
        """)
        
        if field_info:
            logger.info(f"✅ 字段验证成功: {dict(field_info)}")
        
        # 7. 显示当前tools表记录数
        total_tools = await conn.fetchval("SELECT COUNT(*) FROM tools")
        logger.info(f"📊 当前tools表共有 {total_tools} 条记录")
        
        await conn.close()
        logger.info("数据库连接已关闭")
        
        return True
        
    except Exception as e:
        logger.error(f"初始化domain字段失败: {e}")
        return False


async def main():
    """主函数"""
    print("🚀 开始初始化tools表的domain字段...")
    
    success = await init_domain_field()
    
    if success:
        print("🎉 domain字段初始化完成!")
        print("\n下一步:")
        print("  运行域名更新脚本: python update_tools_domain.py")
        print("  查看统计信息: python update_tools_domain.py --stats")
        print("  预览更新内容: python update_tools_domain.py --dry-run")
    else:
        print("❌ domain字段初始化失败!")
        return 1
    
    return 0


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
        exit(1)
    except Exception as e:
        logger.error(f"程序异常退出: {e}")
        exit(1)
