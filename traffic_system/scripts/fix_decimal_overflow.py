#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复数据库字段精度溢出问题
将DECIMAL字段扩大精度范围以避免数值溢出
"""

import asyncio
import logging
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from traffic_system.core.traffic_data_manager import DatabaseConfig

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class DecimalOverflowFixer:
    """数据库字段精度修复器"""
    
    def __init__(self, db_config: DatabaseConfig):
        self.db_config = db_config
        self.db_pool = None
    
    async def init_db_pool(self):
        """初始化数据库连接池"""
        import asyncpg
        self.db_pool = await asyncpg.create_pool(
            host=self.db_config.host,
            port=self.db_config.port,
            database=self.db_config.database,
            user=self.db_config.user,
            password=self.db_config.password,
            min_size=2,
            max_size=10
        )
        logger.info("数据库连接池初始化成功")
    
    async def close_db_pool(self):
        """关闭数据库连接池"""
        if self.db_pool:
            await self.db_pool.close()
            logger.info("数据库连接池已关闭")
    
    async def fix_decimal_fields(self):
        """修复所有DECIMAL字段的精度问题"""
        
        # 定义需要修复的字段
        fixes = [
            # traffic_site_overview 表
            ("traffic_site_overview", "visits_change_percent", "DECIMAL(10,2)"),
            ("traffic_site_overview", "pages_per_visit", "DECIMAL(8,2)"),
            ("traffic_site_overview", "bounce_rate", "DECIMAL(8,2)"),
            ("traffic_site_overview", "domain_age_years", "DECIMAL(8,2)"),
            
            # traffic_monthly_trends 表
            ("traffic_monthly_trends", "growth_rate", "DECIMAL(12,4)"),
            
            # traffic_source_analysis 表
            ("traffic_source_analysis", "traffic_percent", "DECIMAL(8,2)"),
            
            # traffic_region_distribution 表
            ("traffic_region_distribution", "traffic_percent", "DECIMAL(8,2)"),
            
            # traffic_keyword_analysis 表
            ("traffic_keyword_analysis", "traffic_percent", "DECIMAL(8,2)"),
            ("traffic_keyword_analysis", "average_position", "DECIMAL(8,2)"),
        ]
        
        async with self.db_pool.acquire() as conn:
            logger.info("开始修复数据库字段精度...")
            
            success_count = 0
            for table_name, column_name, new_type in fixes:
                try:
                    # 检查字段是否存在
                    exists = await conn.fetchval("""
                        SELECT EXISTS (
                            SELECT 1 FROM information_schema.columns 
                            WHERE table_name = $1 AND column_name = $2
                        )
                    """, table_name, column_name)
                    
                    if not exists:
                        logger.warning(f"字段不存在: {table_name}.{column_name}")
                        continue
                    
                    # 执行字段类型修改
                    sql = f"ALTER TABLE {table_name} ALTER COLUMN {column_name} TYPE {new_type}"
                    await conn.execute(sql)
                    
                    logger.info(f"✅ 修复成功: {table_name}.{column_name} -> {new_type}")
                    success_count += 1
                    
                except Exception as e:
                    logger.error(f"❌ 修复失败: {table_name}.{column_name} -> {e}")
            
            logger.info(f"字段精度修复完成: {success_count}/{len(fixes)} 成功")
    
    async def verify_fixes(self):
        """验证修复结果"""
        async with self.db_pool.acquire() as conn:
            logger.info("验证修复结果...")
            
            # 查询所有DECIMAL字段的精度信息
            result = await conn.fetch("""
                SELECT 
                    table_name, 
                    column_name, 
                    data_type, 
                    numeric_precision, 
                    numeric_scale
                FROM information_schema.columns 
                WHERE table_name IN (
                    'traffic_site_overview', 
                    'traffic_monthly_trends', 
                    'traffic_source_analysis', 
                    'traffic_region_distribution', 
                    'traffic_keyword_analysis'
                ) 
                AND data_type = 'numeric'
                ORDER BY table_name, column_name
            """)
            
            print("\n" + "="*80)
            print("数据库字段精度信息")
            print("="*80)
            print(f"{'表名':<25} {'字段名':<20} {'类型':<10} {'精度':<6} {'小数位':<6}")
            print("-"*80)
            
            for row in result:
                print(f"{row['table_name']:<25} {row['column_name']:<20} {row['data_type']:<10} {row['numeric_precision']:<6} {row['numeric_scale']:<6}")
            
            print("="*80)
            logger.info(f"共查询到 {len(result)} 个DECIMAL字段")


async def main():
    """主函数"""
    logger.info("开始修复数据库字段精度溢出问题...")
    
    # 创建修复器
    db_config = DatabaseConfig()
    fixer = DecimalOverflowFixer(db_config)
    
    try:
        # 初始化数据库连接
        await fixer.init_db_pool()
        
        # 执行修复
        await fixer.fix_decimal_fields()
        
        # 验证修复结果
        await fixer.verify_fixes()
        
        logger.info("✅ 数据库字段精度修复完成！")
        
    except Exception as e:
        logger.error(f"❌ 修复过程中出现错误: {e}")
        return 1
    
    finally:
        await fixer.close_db_pool()
    
    return 0


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        logger.info("\n⚠️ 用户中断修复过程")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ 程序异常退出: {e}")
        sys.exit(1)
