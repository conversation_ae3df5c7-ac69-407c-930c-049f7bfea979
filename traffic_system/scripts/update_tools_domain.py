#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工具域名更新脚本
从tools表的url字段解析域名信息，并更新到domain字段中
"""

import asyncio
import asyncpg
import re
from urllib.parse import urlparse
from typing import Optional, List, Dict, Any
import logging
from datetime import datetime
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from traffic_system.core.traffic_data_manager import DatabaseConfig

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class DomainExtractor:
    """域名提取器"""
    
    def __init__(self):
        # 常见的子域名前缀，需要移除
        self.common_subdomains = {
            'www', 'app', 'api', 'admin', 'dashboard', 'portal', 
            'beta', 'staging', 'dev', 'test', 'demo', 'blog',
            'docs', 'help', 'support', 'cdn', 'static', 'assets'
        }
        
        # 特殊域名映射 - 处理一些特殊情况
        self.special_domains = {
            'play.google.com': 'play.google.com',
            'apps.apple.com': 'apps.apple.com',
            'chrome.google.com': 'chrome.google.com',
            'addons.mozilla.org': 'addons.mozilla.org',
            'marketplace.visualstudio.com': 'marketplace.visualstudio.com'
        }
    
    def extract_domain(self, url: str) -> Optional[str]:
        """从URL中提取域名"""
        if not url:
            return None
        
        try:
            # 确保URL有协议前缀
            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url
            
            # 解析URL
            parsed = urlparse(url)
            hostname = parsed.hostname
            
            if not hostname:
                return None
            
            # 转换为小写
            hostname = hostname.lower()
            
            # 检查特殊域名
            for special_domain in self.special_domains:
                if hostname == special_domain or hostname.endswith('.' + special_domain):
                    return self.special_domains[special_domain]
            
            # 移除常见的子域名前缀
            domain_parts = hostname.split('.')
            
            # 如果域名有多个部分，检查是否需要移除子域名
            if len(domain_parts) > 2:
                # 检查第一个部分是否是常见的子域名
                if domain_parts[0] in self.common_subdomains:
                    # 移除子域名，保留主域名
                    hostname = '.'.join(domain_parts[1:])
            
            return hostname
            
        except Exception as e:
            logger.warning(f"解析URL失败 '{url}': {e}")
            return None
    
    def validate_domain(self, domain: str) -> bool:
        """验证域名格式是否正确"""
        if not domain:
            return False

        # 对于特殊域名（如play.google.com），使用更宽松的验证
        if domain in self.special_domains.values():
            return True

        # 改进的域名格式验证，支持多级域名
        # 域名的每个部分都应该符合标准格式
        domain_parts = domain.split('.')

        # 至少要有两个部分（如 example.com）
        if len(domain_parts) < 2:
            return False

        # 验证每个部分
        for part in domain_parts:
            if not part:  # 空部分
                return False
            if len(part) > 63:  # 每个部分最长63个字符
                return False
            if not re.match(r'^[a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?$', part):
                return False

        # 最后一个部分（顶级域名）应该至少有2个字符且只包含字母
        tld = domain_parts[-1]
        if len(tld) < 2 or not re.match(r'^[a-zA-Z]+$', tld):
            return False

        return True


class ToolsDomainUpdater:
    """工具域名更新器"""
    
    def __init__(self, db_config: DatabaseConfig):
        self.db_config = db_config
        self.db_pool = None
        self.extractor = DomainExtractor()
    
    async def init_db_pool(self):
        """初始化数据库连接池"""
        try:
            self.db_pool = await asyncpg.create_pool(
                host=self.db_config.host,
                port=self.db_config.port,
                database=self.db_config.database,
                user=self.db_config.user,
                password=self.db_config.password,
                min_size=1,
                max_size=5
            )
            logger.info("数据库连接池初始化成功")
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise
    
    async def close_db_pool(self):
        """关闭数据库连接池"""
        if self.db_pool:
            await self.db_pool.close()
            logger.info("数据库连接池已关闭")
    
    async def get_tools_data(self, only_empty_domain: bool = True) -> List[Dict[str, Any]]:
        """获取工具的URL数据"""
        async with self.db_pool.acquire() as conn:
            if only_empty_domain:
                # 只获取domain字段为空的数据
                rows = await conn.fetch("""
                    SELECT id, tool_id, url, domain
                    FROM tools
                    WHERE domain IS NULL OR domain = ''
                    ORDER BY id
                """)
            else:
                # 获取所有数据
                rows = await conn.fetch("""
                    SELECT id, tool_id, url, domain
                    FROM tools
                    ORDER BY id
                """)

            return [dict(row) for row in rows]
    
    async def update_tool_domain(self, tool_id: str, domain: str) -> bool:
        """更新单个工具的域名"""
        try:
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    UPDATE tools 
                    SET domain = $1, updated_at = CURRENT_TIMESTAMP
                    WHERE tool_id = $2
                """, domain, tool_id)
                return True
        except Exception as e:
            logger.error(f"更新工具 {tool_id} 的域名失败: {e}")
            return False
    
    async def batch_update_domains(self, updates: List[Dict[str, str]]) -> int:
        """批量更新域名"""
        success_count = 0
        
        async with self.db_pool.acquire() as conn:
            async with conn.transaction():
                for update in updates:
                    try:
                        await conn.execute("""
                            UPDATE tools 
                            SET domain = $1, updated_at = CURRENT_TIMESTAMP
                            WHERE tool_id = $2
                        """, update['domain'], update['tool_id'])
                        success_count += 1
                    except Exception as e:
                        logger.error(f"批量更新工具 {update['tool_id']} 失败: {e}")
        
        return success_count
    
    async def process_all_tools(self, dry_run: bool = False, only_empty_domain: bool = True) -> Dict[str, Any]:
        """处理工具的域名提取和更新"""
        if only_empty_domain:
            logger.info("开始处理domain字段为空的工具域名提取...")
        else:
            logger.info("开始处理所有工具的域名提取...")

        # 获取工具数据
        tools_data = await self.get_tools_data(only_empty_domain=only_empty_domain)
        logger.info(f"获取到 {len(tools_data)} 个工具")
        
        # 统计信息
        stats = {
            'total': len(tools_data),
            'processed': 0,
            'updated': 0,
            'skipped': 0,
            'failed': 0,
            'invalid_urls': 0
        }
        
        updates = []
        
        for tool in tools_data:
            tool_id = tool['tool_id']
            url = tool['url']
            current_domain = tool['domain']
            
            stats['processed'] += 1
            
            # 提取域名
            extracted_domain = self.extractor.extract_domain(url)
            
            if not extracted_domain:
                logger.warning(f"无法从URL提取域名: {tool_id} -> {url}")
                stats['invalid_urls'] += 1
                continue
            
            # 验证域名格式
            if not self.extractor.validate_domain(extracted_domain):
                logger.warning(f"域名格式无效: {tool_id} -> {extracted_domain}")
                stats['failed'] += 1
                continue
            
            # 检查是否需要更新
            if only_empty_domain and current_domain and current_domain.strip():
                # 如果只处理空域名，但当前域名不为空，则跳过
                logger.debug(f"域名已存在，跳过: {tool_id} -> {current_domain}")
                stats['skipped'] += 1
                continue
            elif current_domain == extracted_domain:
                logger.debug(f"域名无变化，跳过: {tool_id} -> {extracted_domain}")
                stats['skipped'] += 1
                continue
            
            # 记录更新信息
            logger.info(f"提取域名: {tool_id} -> {extracted_domain} (从 {url})")
            
            if dry_run:
                print(f"[DRY RUN] 将更新: {tool_id} -> {extracted_domain}")
            else:
                updates.append({
                    'tool_id': tool_id,
                    'domain': extracted_domain
                })
        
        # 执行批量更新
        if not dry_run and updates:
            logger.info(f"开始批量更新 {len(updates)} 个域名...")
            updated_count = await self.batch_update_domains(updates)
            stats['updated'] = updated_count
            logger.info(f"批量更新完成，成功更新 {updated_count} 个域名")
        
        return stats
    
    async def get_domain_statistics(self) -> Dict[str, Any]:
        """获取域名统计信息"""
        async with self.db_pool.acquire() as conn:
            # 总工具数
            total_tools = await conn.fetchval("SELECT COUNT(*) FROM tools")
            
            # 有域名的工具数
            tools_with_domain = await conn.fetchval(
                "SELECT COUNT(*) FROM tools WHERE domain IS NOT NULL AND domain != ''"
            )
            
            # 热门域名统计
            top_domains = await conn.fetch("""
                SELECT domain, COUNT(*) as tool_count
                FROM tools 
                WHERE domain IS NOT NULL AND domain != ''
                GROUP BY domain 
                ORDER BY tool_count DESC, domain
                LIMIT 10
            """)
            
            return {
                'total_tools': total_tools,
                'tools_with_domain': tools_with_domain,
                'coverage_percentage': round((tools_with_domain / total_tools) * 100, 2) if total_tools > 0 else 0,
                'top_domains': [dict(row) for row in top_domains]
            }


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='工具域名更新脚本')
    parser.add_argument('--dry-run', action='store_true', help='只显示将要更新的内容，不实际更新')
    parser.add_argument('--stats', action='store_true', help='显示域名统计信息')
    parser.add_argument('--all', action='store_true', help='处理所有工具，包括已有域名的工具')
    parser.add_argument('--db-host', default='127.0.0.1', help='数据库主机')
    parser.add_argument('--db-port', type=int, default=5432, help='数据库端口')
    parser.add_argument('--db-name', default='aistak_db', help='数据库名称')
    parser.add_argument('--db-user', default='root', help='数据库用户')
    parser.add_argument('--db-password', default='fuwenhao', help='数据库密码')
    
    args = parser.parse_args()
    
    # 创建数据库配置
    db_config = DatabaseConfig(
        host=args.db_host,
        port=args.db_port,
        database=args.db_name,
        user=args.db_user,
        password=args.db_password
    )
    
    # 创建更新器
    updater = ToolsDomainUpdater(db_config)
    
    try:
        await updater.init_db_pool()
        
        if args.stats:
            # 显示统计信息
            print("\n" + "="*60)
            print("域名统计信息")
            print("="*60)
            
            stats = await updater.get_domain_statistics()
            print(f"总工具数: {stats['total_tools']}")
            print(f"有域名的工具数: {stats['tools_with_domain']}")
            print(f"域名覆盖率: {stats['coverage_percentage']}%")
            
            if stats['top_domains']:
                print(f"\n热门域名 (前10):")
                for i, domain_info in enumerate(stats['top_domains'], 1):
                    print(f"  {i:2d}. {domain_info['domain']} ({domain_info['tool_count']} 个工具)")
        
        else:
            # 执行域名更新
            print("\n" + "="*60)
            print("工具域名更新")
            print("="*60)
            
            if args.dry_run:
                print("🔍 DRY RUN 模式 - 只显示将要更新的内容")
            
            start_time = datetime.now()
            stats = await updater.process_all_tools(
                dry_run=args.dry_run,
                only_empty_domain=not args.all
            )
            end_time = datetime.now()
            
            # 显示结果
            print(f"\n处理完成! 耗时: {(end_time - start_time).total_seconds():.2f}秒")
            print(f"总工具数: {stats['total']}")
            print(f"已处理: {stats['processed']}")
            print(f"已更新: {stats['updated']}")
            print(f"已跳过: {stats['skipped']}")
            print(f"失败: {stats['failed']}")
            print(f"无效URL: {stats['invalid_urls']}")
            
            if not args.dry_run and stats['updated'] > 0:
                print(f"\n✅ 成功更新了 {stats['updated']} 个工具的域名信息!")
    
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        return 1
    
    finally:
        await updater.close_db_pool()
    
    return 0


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
        exit(1)
    except Exception as e:
        logger.error(f"程序异常退出: {e}")
        exit(1)
