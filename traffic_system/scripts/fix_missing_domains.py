#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复缺失域名的脚本
处理一些特殊情况的域名提取
"""

import asyncio
import asyncpg
import re
from urllib.parse import urlparse
from typing import Optional
import logging
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from traffic_system.core.traffic_data_manager import DatabaseConfig

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class EnhancedDomainExtractor:
    """增强版域名提取器"""
    
    def __init__(self):
        # 特殊域名映射
        self.special_mappings = {
            'chromewebstore.google.com': 'chrome.google.com',
            'chrome.google.com': 'chrome.google.com',
            'addons.mozilla.org': 'addons.mozilla.org',
            'marketplace.visualstudio.com': 'marketplace.visualstudio.com',
            'apps.apple.com': 'apps.apple.com',
            'play.google.com': 'play.google.com'
        }
        
        # 子域名处理规则
        self.subdomain_rules = {
            'pro.': '',      # pro.placy.ai -> placy.ai
            'en.': '',       # en.minea.com -> minea.com
            'chat.': '',     # chat.helpedby.ai -> helpedby.ai
            'ai.': '',       # ai.tenorshare.com -> tenorshare.com
            'refer.': '',    # refer.edesk.com -> edesk.com
        }
    
    def extract_domain(self, url: str) -> Optional[str]:
        """增强版域名提取"""
        if not url:
            return None
        
        try:
            # 确保URL有协议前缀
            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url
            
            # 解析URL
            parsed = urlparse(url)
            hostname = parsed.hostname
            
            if not hostname:
                return None
            
            hostname = hostname.lower()
            
            # 检查特殊域名映射
            for special_domain, mapped_domain in self.special_mappings.items():
                if hostname == special_domain or hostname.endswith('.' + special_domain):
                    return mapped_domain
            
            # 处理子域名规则
            for prefix, replacement in self.subdomain_rules.items():
                if hostname.startswith(prefix):
                    hostname = replacement + hostname[len(prefix):]
                    break
            
            # 移除www前缀
            if hostname.startswith('www.'):
                hostname = hostname[4:]
            
            return hostname
            
        except Exception as e:
            logger.warning(f"解析URL失败 '{url}': {e}")
            return None


async def fix_missing_domains():
    """修复缺失的域名"""
    db_config = DatabaseConfig()
    extractor = EnhancedDomainExtractor()
    
    try:
        conn = await asyncpg.connect(
            host=db_config.host,
            port=db_config.port,
            database=db_config.database,
            user=db_config.user,
            password=db_config.password
        )
        
        logger.info("数据库连接成功")
        
        # 获取没有域名的记录
        missing_records = await conn.fetch("""
            SELECT tool_id, url
            FROM tools 
            WHERE domain IS NULL OR domain = ''
        """)
        
        logger.info(f"找到 {len(missing_records)} 条缺失域名的记录")
        
        updated_count = 0
        
        for record in missing_records:
            tool_id = record['tool_id']
            url = record['url']
            
            # 尝试提取域名
            domain = extractor.extract_domain(url)
            
            if domain:
                # 更新数据库
                await conn.execute("""
                    UPDATE tools 
                    SET domain = $1, updated_at = CURRENT_TIMESTAMP
                    WHERE tool_id = $2
                """, domain, tool_id)
                
                logger.info(f"修复域名: {tool_id} -> {domain}")
                updated_count += 1
            else:
                logger.warning(f"仍无法提取域名: {tool_id} -> {url}")
        
        await conn.close()
        
        logger.info(f"修复完成，成功更新 {updated_count} 个域名")
        return updated_count
        
    except Exception as e:
        logger.error(f"修复域名失败: {e}")
        return 0


async def main():
    """主函数"""
    print("🔧 开始修复缺失的域名...")
    
    updated_count = await fix_missing_domains()
    
    if updated_count > 0:
        print(f"✅ 成功修复了 {updated_count} 个域名!")
        
        # 显示最新统计
        print("\n运行统计脚本查看最新情况:")
        print("  python update_tools_domain.py --stats")
    else:
        print("⚠️ 没有找到可修复的域名")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
    except Exception as e:
        logger.error(f"程序异常退出: {e}")
