# 工具流量数据同步系统

## 🎯 系统概述

这是一个完整的工具流量数据同步系统，解决了 `tools` 表中多个 `tool_id` 对应同一 `domain` 的问题，实现了流量数据与工具的准确关联。

### 核心功能
- **智能主工具选择**: 为每个域名自动选择最合适的主工具
- **域名冲突解决**: 处理多个工具对应同一域名的情况
- **流量数据同步**: 自动获取并入库流量数据
- **数据一致性保证**: 确保流量表中域名和tool_id的唯一对应关系
- **映射关系管理**: 维护域名与工具的完整映射关系

## 📁 文件结构

```
├── tools_traffic_sync.py          # 核心同步逻辑类
├── sync_tools_traffic.py          # 命令行主程序
├── DOMAIN_CONFLICT_RESOLUTION.md  # 域名冲突解决策略文档
└── TOOLS_TRAFFIC_SYNC_README.md   # 使用说明文档
```

## 🔧 核心解决方案

### 1. 主工具选择策略

为每个域名选择一个**主要工具 (Primary Tool)**作为流量数据的唯一关联对象：

#### 选择规则优先级：
1. **排除扩展程序**: 优先排除Chrome扩展、浏览器插件等衍生产品
2. **创建时间优先**: 选择最早创建的工具 (`created_at` 最小)
3. **字典序排序**: 如果创建时间相同，选择 `tool_id` 字典序最小的

#### 典型案例：
- **chrome.google.com**: 58个工具 → 选择 `twitter_x_response_generator` (最早创建)
- **apps.apple.com**: 40个工具 → 选择最早创建的非扩展工具
- **play.google.com**: 11个工具 → 选择最早创建的工具

### 2. 域名-工具映射表

创建 `domain_tool_mapping` 表记录完整的映射关系：

```sql
CREATE TABLE domain_tool_mapping (
    id SERIAL PRIMARY KEY,
    domain VARCHAR(255) NOT NULL,
    primary_tool_id VARCHAR(50) NOT NULL,
    related_tool_ids TEXT[] NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT uk_domain_mapping UNIQUE (domain)
);
```

### 3. 数据一致性保证

- **唯一性**: 每个域名在流量表中只有一条记录
- **关联性**: 流量数据的 `tool_id` 使用主要工具ID
- **完整性**: 保持所有工具与域名的关联关系

## 🚀 使用方法

### 1. 查看统计信息
```bash
# 显示基础统计和重复域名情况
python sync_tools_traffic.py --stats
```

**输出示例**:
```
============================================================
工具流量数据同步统计
============================================================
📊 基础统计:
   总工具数: 2071
   唯一域名数: 1944
   已有流量数据域名: 8
   已映射域名数: 7
   流量数据覆盖率: 0.41%

🔄 重复域名情况 (前5):
   1. chrome.google.com (58 个工具)
   2. apps.apple.com (40 个工具)
   3. play.google.com (11 个工具)
   4. toolsmart.ai (3 个工具)
   5. bookeeping.ai (2 个工具)
```

### 2. 查看域名映射关系
```bash
# 显示域名-工具映射关系
python sync_tools_traffic.py --mapping --limit 10
```

**输出示例**:
```
🗺️ 域名-工具映射关系 (前10条)
------------------------------------------------------------
 1. chrome.google.com
    主要工具: twitter_x_response_generator
    工具总数: 58
    其他工具: genii_assistant, engage_bot_-_linkedin_messaging_ai_assistant, xcelsior_clipper (+54个)

 2. apps.apple.com
    主要工具: primary_tool_id
    工具总数: 40
    其他工具: tool1, tool2, tool3 (+36个)
```

### 3. 同步指定域名
```bash
# 同步特定域名的流量数据
python sync_tools_traffic.py --sync-domains miro.com chrome.google.com
```

**输出示例**:
```
🎯 同步指定域名: miro.com, chrome.google.com
------------------------------------------------------------

处理域名: miro.com
  主要工具: miro
  关联工具数: 1
  ✅ 同步成功: 20 数据点

处理域名: chrome.google.com
  主要工具: twitter_x_response_generator
  关联工具数: 58
  ✅ 同步成功: 16 数据点
```

### 4. 批量同步所有域名
```bash
# 小规模测试同步
python sync_tools_traffic.py --sync-all --limit 10

# 跳过已有数据的全量同步
python sync_tools_traffic.py --sync-all

# 强制更新所有数据
python sync_tools_traffic.py --sync-all --force
```

**输出示例**:
```
🚀 开始同步所有域名的流量数据
   限制处理数量: 5
   跳过已有数据: 是
------------------------------------------------------------

📈 同步完成! 耗时: 23.63秒
   总域名数: 1944
   已处理: 5
   成功: 5
   失败: 0
   跳过: 0
```

## 📊 命令行参数

### 基础操作参数
- `--stats`: 显示同步统计信息
- `--mapping`: 显示域名映射关系
- `--sync-all`: 同步所有域名的流量数据
- `--sync-domains DOMAIN1 DOMAIN2`: 同步指定域名

### 控制参数
- `--limit N`: 限制处理的域名数量
- `--force`: 强制更新已有数据（默认跳过）
- `--output FILE`: 输出结果到JSON文件

### 数据库配置参数
- `--db-host`: 数据库主机 (默认: 127.0.0.1)
- `--db-port`: 数据库端口 (默认: 5432)
- `--db-name`: 数据库名称 (默认: aistak_db)
- `--db-user`: 数据库用户 (默认: root)
- `--db-password`: 数据库密码 (默认: fuwenhao)

## 🔍 数据验证

### 验证流量数据
```sql
-- 查看已同步的流量数据
SELECT 
    tool_id,
    domain,
    total_visits,
    bounce_rate
FROM traffic_site_overview 
WHERE domain IN ('miro.com', 'chrome.google.com');
```

### 验证映射关系
```sql
-- 查看域名映射关系
SELECT 
    domain,
    primary_tool_id,
    array_length(related_tool_ids, 1) as tool_count
FROM domain_tool_mapping 
ORDER BY tool_count DESC;
```

### 验证数据一致性
```sql
-- 检查流量数据覆盖率
SELECT 
    COUNT(DISTINCT t.domain) as total_domains,
    COUNT(DISTINCT tso.domain) as traffic_domains,
    ROUND((COUNT(DISTINCT tso.domain)::float / COUNT(DISTINCT t.domain)) * 100, 2) as coverage_percentage
FROM tools t
LEFT JOIN traffic_site_overview tso ON t.domain = tso.domain
WHERE t.domain IS NOT NULL;
```

## 📈 系统优势

### 1. 数据一致性
- ✅ 每个域名在流量表中只有一条记录
- ✅ 流量数据与主要工具唯一关联
- ✅ 避免数据混淆和重复

### 2. 查询灵活性
- ✅ 支持通过任意工具ID查找流量数据
- ✅ 支持域名级别的流量分析
- ✅ 支持工具关联关系查询

### 3. 业务价值
- ✅ 准确的流量数据分析
- ✅ 清晰的工具-流量关联关系
- ✅ 支持复杂的业务查询需求

## 🛠️ 实施建议

### 阶段1: 数据分析 (已完成)
```bash
# 了解数据现状
python sync_tools_traffic.py --stats
python sync_tools_traffic.py --mapping
```

### 阶段2: 小规模测试 (已完成)
```bash
# 测试特定域名
python sync_tools_traffic.py --sync-domains miro.com chrome.google.com

# 小批量测试
python sync_tools_traffic.py --sync-all --limit 10
```

### 阶段3: 分批全量同步 (推荐)
```bash
# 分批同步，每批100个域名
python sync_tools_traffic.py --sync-all --limit 100

# 继续下一批（会自动跳过已有数据）
python sync_tools_traffic.py --sync-all --limit 200
```

### 阶段4: 定期维护
```bash
# 定期同步新增域名
python sync_tools_traffic.py --sync-all

# 强制更新所有数据（谨慎使用）
python sync_tools_traffic.py --sync-all --force
```

## 🔧 故障排除

### 常见问题

1. **域名不存在错误**
   - 确认域名在tools表中存在
   - 检查域名拼写是否正确

2. **同步失败**
   - 检查网络连接
   - 查看错误日志
   - 验证数据库权限

3. **数据不一致**
   - 运行数据验证查询
   - 检查映射表完整性
   - 必要时重新同步

### 日志分析
```bash
# 查看详细日志
tail -f tools_traffic_sync_20250723.log

# 搜索错误信息
grep "ERROR\|FAILED" tools_traffic_sync_20250723.log
```

## 📝 总结

这套工具流量数据同步系统成功解决了：

✅ **域名冲突问题**: 多个tool_id对应同一domain的合理处理  
✅ **数据一致性**: 流量表中域名和tool_id的唯一对应关系  
✅ **查询灵活性**: 支持多种查询方式和业务需求  
✅ **自动化处理**: 批量同步和智能错误处理  
✅ **完整映射**: 保持所有工具与域名的关联关系  

系统现在可以支持准确的流量分析、工具关联查询和复杂的业务洞察功能！🎊
