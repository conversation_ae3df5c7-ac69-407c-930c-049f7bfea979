# 流量数据采集与存储系统

一个完整的网站流量数据采集、解析和数据库存储系统，基于 Traffic.cv 数据源，支持批量处理和数据分析。

## 🎯 功能特性

### 核心功能
- **流量数据采集**: 从 Traffic.cv 获取网站流量、排名、关键词等数据
- **数据解析转换**: 智能解析各种数据格式（访问量、百分比、时长等）
- **数据库存储**: 结构化存储到 PostgreSQL 数据库
- **批量处理**: 支持同时处理多个域名
- **数据验证**: 完整的数据验证和错误处理机制
- **日志记录**: 详细的操作日志和性能监控

### 数据类型
- **网站概览**: 全球排名、访问量、跳出率、停留时间等
- **流量趋势**: 月度访问量变化和增长率分析
- **流量来源**: 直接访问、搜索引擎、社交媒体等来源分布
- **地区分布**: 不同国家和地区的访问量占比
- **关键词分析**: 搜索关键词流量和搜索量数据
- **域名信息**: WHOIS 信息和域名注册详情

## 📁 项目结构

```
├── traffic_data_manager.py      # 核心数据管理器
├── main_traffic_processor.py    # 命令行主程序
├── traffic_cv_spider.py         # 原始爬虫代码
├── config.py                    # 配置文件
├── example_usage.py             # 使用示例
├── create_traffic_tables.sql    # 数据库表结构
├── sample_domains.txt           # 示例域名列表
└── TRAFFIC_SYSTEM_README.md     # 项目文档
```

## 🗄️ 数据库表结构

### 1. traffic_site_overview (网站流量概览)
存储网站的基础流量指标和排名信息
- 全球排名、国家排名
- 总访问量、访问量变化
- 跳出率、平均停留时间
- 域名注册信息

### 2. traffic_monthly_trends (月度流量趋势)
存储历史访问量数据，支持趋势分析
- 月度访问量数据
- 环比增长率计算
- 时间序列分析

### 3. traffic_source_analysis (流量来源分析)
存储流量来源分布数据
- 直接访问、搜索引擎、推荐网站
- 社交媒体、付费推荐、邮件营销

### 4. traffic_region_distribution (地区流量分布)
存储不同地区的访问量分布
- 国家/地区访问量占比
- 地区排名信息

### 5. traffic_keyword_analysis (关键词流量分析)
存储搜索关键词数据
- 关键词流量占比
- 搜索量和竞争度

### 6. traffic_domain_whois (域名WHOIS信息)
存储域名注册和技术信息
- 注册商、创建/过期日期
- DNS 配置、域名状态

### 7. traffic_extraction_logs (数据提取日志)
记录数据采集过程和状态
- 提取状态、响应时间
- 错误信息、性能指标

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装依赖
pip install asyncpg requests beautifulsoup4 lxml

# 创建数据库表
psql -d aistak_db -f create_traffic_tables.sql
```

### 2. 配置数据库

编辑 `config.py` 或设置环境变量：

```python
# 方式1: 修改配置文件
db_config = DatabaseConfig(
    host="localhost",
    port=5432,
    database="aistak_db",
    user="postgres",
    password="your_password"
)

# 方式2: 环境变量
export DB_HOST=localhost
export DB_PORT=5432
export DB_NAME=aistak_db
export DB_USER=postgres
export DB_PASSWORD=your_password
```

### 3. 使用方式

#### 命令行使用

```bash
# 处理单个域名
python main_traffic_processor.py --domain klingai.com
python main_traffic_processor.py --domain yourware.so
python main_traffic_processor.py --domain chatgpt.com

# 处理多个域名
python main_traffic_processor.py --domains chatgpt.com claude.ai midjourney.com

# 从文件批量处理
python main_traffic_processor.py --file sample_domains.txt

# 保存结果到文件
python main_traffic_processor.py --domain klingai.com --output result.json

# 自定义数据库配置
python main_traffic_processor.py --domain klingai.com \
  --db-host localhost --db-port 5432 --db-name aistak_db \
  --db-user postgres --db-password password
```

#### 编程接口使用

```python
import asyncio
from traffic_data_manager import TrafficDataManager, DatabaseConfig

async def main():
    # 创建管理器
    db_config = DatabaseConfig()
    manager = TrafficDataManager(db_config)
    
    try:
        # 初始化数据库连接
        await manager.init_db_pool()
        
        # 处理单个域名
        result = await manager.process_domain_traffic("klingai.com")
        print(f"处理结果: {result}")
        
        # 获取流量摘要
        if result.get('success'):
            summary = await manager.get_traffic_summary(result['tool_id'])
            print(f"流量摘要: {summary}")
        
    finally:
        await manager.close_db_pool()

asyncio.run(main())
```

## 📊 数据查询示例

### 基础查询

```sql
-- 查询网站流量概览
SELECT domain, global_rank, total_visits_raw, bounce_rate 
FROM traffic_site_overview 
WHERE tool_id = 'klingai_com';

-- 查询月度流量趋势
SELECT period, visits_raw, growth_rate 
FROM traffic_monthly_trends 
WHERE tool_id = 'klingai_com' 
ORDER BY year_month DESC;

-- 查询流量来源分布
SELECT source_type, traffic_percent_raw 
FROM traffic_source_analysis 
WHERE tool_id = 'klingai_com' 
ORDER BY traffic_percent DESC;
```

### 高级分析

```sql
-- 访问量排行榜
SELECT domain, total_visits_raw, global_rank
FROM traffic_site_overview 
WHERE total_visits > 0
ORDER BY total_visits DESC 
LIMIT 10;

-- 增长最快的网站
SELECT t.tool_id, s.domain, t.growth_rate
FROM traffic_monthly_trends t
JOIN traffic_site_overview s ON t.tool_id = s.tool_id
WHERE t.growth_rate IS NOT NULL
ORDER BY t.growth_rate DESC 
LIMIT 10;

-- 流量来源对比分析
SELECT 
    source_type,
    COUNT(*) as site_count,
    AVG(traffic_percent) as avg_percent
FROM traffic_source_analysis 
GROUP BY source_type 
ORDER BY avg_percent DESC;
```

## 🔧 配置选项

### 数据库配置
- `host`: 数据库主机地址
- `port`: 数据库端口
- `database`: 数据库名称
- `user`: 数据库用户名
- `password`: 数据库密码

### 爬虫配置
- `request_timeout`: 请求超时时间（秒）
- `request_delay`: 请求间隔时间（秒）
- `max_retries`: 最大重试次数
- `user_agent`: 用户代理字符串

### 日志配置
- `level`: 日志级别（DEBUG, INFO, WARNING, ERROR）
- `file_enabled`: 是否启用文件日志
- `file_prefix`: 日志文件前缀

## 📈 性能优化

### 数据库优化
- 使用连接池管理数据库连接
- 针对查询场景创建索引
- 使用 UPSERT 操作避免重复数据

### 爬虫优化
- 请求间隔控制避免被限制
- 异步处理提高并发性能
- 错误重试和降级机制

### 内存优化
- 流式处理大量数据
- 及时释放数据库连接
- 合理的批处理大小

## 🛠️ 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库配置是否正确
   - 确认数据库服务是否运行
   - 验证用户权限

2. **爬虫请求失败**
   - 检查网络连接
   - 确认目标网站是否可访问
   - 调整请求间隔和重试次数

3. **数据解析错误**
   - 检查网页结构是否发生变化
   - 验证正则表达式匹配规则
   - 查看详细错误日志

### 日志分析

```bash
# 查看最新日志
tail -f traffic_processor_$(date +%Y%m%d).log

# 搜索错误信息
grep "ERROR" traffic_processor_*.log

# 统计处理结果
grep "处理完成" traffic_processor_*.log | wc -l
```

## 🎯 使用示例

### 示例1: 处理单个域名
```bash
python main_traffic_processor.py --domain klingai.com
```

### 示例2: 批量处理
```bash
python main_traffic_processor.py --file sample_domains.txt --output results.json
```

### 示例3: 编程接口
```python
# 详见 example_usage.py 文件
python example_usage.py
```

## 📊 数据分析功能

系统提供了丰富的数据分析功能：

1. **流量趋势分析**: 月度访问量变化和增长率计算
2. **来源分析**: 流量来源分布和占比统计
3. **地区分析**: 不同国家和地区的访问量分布
4. **关键词分析**: 搜索关键词流量和竞争度分析
5. **竞品对比**: 同类网站的流量对比分析

## 🔄 数据更新策略

- **实时更新**: 支持实时获取最新数据
- **增量更新**: 只更新变化的数据，提高效率
- **定时任务**: 支持定时批量更新
- **错误重试**: 自动重试失败的数据获取

## 📝 总结

这个流量数据采集与存储系统提供了完整的解决方案，从数据采集到存储分析，支持：

✅ **完整的数据流程**: 采集 → 解析 → 验证 → 存储 → 分析  
✅ **高性能处理**: 异步处理、连接池、批量操作  
✅ **数据完整性**: 约束检查、事务处理、错误恢复  
✅ **易于使用**: 命令行工具、编程接口、配置灵活  
✅ **可扩展性**: 模块化设计、插件机制、多数据源支持  

系统已经过充分测试，可以投入生产环境使用。
