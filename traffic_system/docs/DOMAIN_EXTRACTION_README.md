# Tools表域名提取系统

为 `tools` 表添加 `domain` 字段，并从 `url` 字段中智能提取域名信息的完整解决方案。

## 🎯 功能概述

### 核心功能
- **自动域名提取**: 从URL中智能提取主域名
- **智能子域名处理**: 自动移除常见子域名前缀（www, app, api等）
- **特殊域名映射**: 处理Chrome扩展、App Store等特殊情况
- **批量更新**: 支持批量处理和更新
- **数据验证**: 确保提取的域名格式正确
- **统计分析**: 提供详细的域名分布统计

### 处理能力
- ✅ **标准域名**: `https://example.com` → `example.com`
- ✅ **子域名处理**: `https://www.example.com` → `example.com`
- ✅ **特殊前缀**: `https://app.example.com` → `example.com`
- ✅ **Chrome扩展**: `chromewebstore.google.com/...` → `chrome.google.com`
- ✅ **App Store**: `apps.apple.com/...` → `apps.apple.com`
- ✅ **参数清理**: 自动移除UTM参数和查询字符串

## 📁 文件结构

```
├── add_domain_field.sql          # SQL脚本：添加domain字段
├── init_domain_field.py          # 初始化脚本：创建字段和索引
├── update_tools_domain.py        # 主要脚本：域名提取和更新
├── fix_missing_domains.py        # 修复脚本：处理特殊情况
└── DOMAIN_EXTRACTION_README.md   # 使用文档
```

## 🚀 快速开始

### 1. 初始化数据库字段

```bash
# 方法1: 使用Python脚本（推荐）
python init_domain_field.py

# 方法2: 直接执行SQL
psql -d aistak_db -f add_domain_field.sql
```

### 2. 提取和更新域名

```bash
# 预览将要更新的内容
python update_tools_domain.py --dry-run

# 执行实际更新
python update_tools_domain.py

# 查看统计信息
python update_tools_domain.py --stats
```

### 3. 修复特殊情况（可选）

```bash
# 修复一些特殊域名
python fix_missing_domains.py
```

## 📊 执行结果

### 最终统计
- **总工具数**: 2,071
- **域名覆盖率**: 100%
- **成功提取**: 2,071 个域名

### 热门域名分布
| 排名 | 域名 | 工具数量 |
|------|------|----------|
| 1 | chrome.google.com | 58 |
| 2 | apps.apple.com | 40 |
| 3 | play.google.com | 11 |
| 4 | toolsmart.ai | 3 |
| 5 | bestfaceswap.net | 2 |

## 🔧 脚本详细说明

### 1. init_domain_field.py
**功能**: 初始化数据库字段
- 添加 `domain VARCHAR(255)` 字段
- 创建索引 `idx_tools_domain`
- 添加字段注释
- 验证字段创建成功

**使用方法**:
```bash
python init_domain_field.py
```

### 2. update_tools_domain.py
**功能**: 主要的域名提取和更新工具

**命令行参数**:
- `--dry-run`: 预览模式，不实际更新
- `--stats`: 显示域名统计信息
- `--db-host`: 数据库主机（默认: 127.0.0.1）
- `--db-port`: 数据库端口（默认: 5432）
- `--db-name`: 数据库名称（默认: aistak_db）
- `--db-user`: 数据库用户（默认: root）
- `--db-password`: 数据库密码（默认: fuwenhao）

**使用示例**:
```bash
# 预览更新
python update_tools_domain.py --dry-run

# 执行更新
python update_tools_domain.py

# 查看统计
python update_tools_domain.py --stats

# 自定义数据库配置
python update_tools_domain.py --db-host localhost --db-user postgres
```

### 3. fix_missing_domains.py
**功能**: 修复特殊情况的域名提取
- 处理Chrome扩展商店链接
- 处理特殊子域名前缀
- 修复复杂的URL结构

**特殊处理规则**:
- `chromewebstore.google.com` → `chrome.google.com`
- `pro.example.com` → `example.com`
- `en.example.com` → `example.com`
- `chat.example.com` → `example.com`

## 🎯 域名提取规则

### 标准处理流程
1. **URL解析**: 使用 `urlparse` 提取hostname
2. **协议补全**: 自动添加 `https://` 前缀
3. **特殊映射**: 检查特殊域名映射表
4. **子域名处理**: 移除常见子域名前缀
5. **格式验证**: 验证域名格式正确性

### 子域名移除规则
移除以下常见前缀：
- `www.` - 万维网前缀
- `app.` - 应用程序子域名
- `api.` - API接口子域名
- `admin.` - 管理后台子域名
- `dashboard.` - 仪表板子域名
- `portal.` - 门户网站子域名
- `beta.`, `staging.`, `dev.`, `test.` - 测试环境
- `demo.` - 演示环境
- `blog.`, `docs.`, `help.`, `support.` - 内容子域名
- `cdn.`, `static.`, `assets.` - 静态资源子域名

### 特殊域名保留
以下域名保持完整格式：
- `play.google.com` - Google Play商店
- `apps.apple.com` - Apple App Store
- `chrome.google.com` - Chrome扩展商店
- `addons.mozilla.org` - Firefox扩展商店
- `marketplace.visualstudio.com` - VS Code扩展商店

## 📈 数据库影响

### 表结构变更
```sql
-- 添加的字段
ALTER TABLE tools ADD COLUMN domain VARCHAR(255);

-- 创建的索引
CREATE INDEX idx_tools_domain ON tools(domain);

-- 字段注释
COMMENT ON COLUMN tools.domain IS '从url字段解析出的域名，如: chatgpt.com, github.com';
```

### 查询优化
新增的索引支持以下高效查询：
```sql
-- 按域名查找工具
SELECT * FROM tools WHERE domain = 'chatgpt.com';

-- 域名统计
SELECT domain, COUNT(*) FROM tools GROUP BY domain ORDER BY COUNT(*) DESC;

-- 域名模糊匹配
SELECT * FROM tools WHERE domain LIKE '%.google.com';
```

## 🔍 验证和测试

### 数据完整性检查
```sql
-- 检查域名覆盖率
SELECT 
    COUNT(*) as total_tools,
    COUNT(domain) as tools_with_domain,
    CAST((COUNT(domain)::float / COUNT(*)) * 100 AS DECIMAL(5,2)) as coverage_percentage
FROM tools;

-- 查看热门域名
SELECT domain, COUNT(*) as tool_count
FROM tools 
WHERE domain IS NOT NULL 
GROUP BY domain 
ORDER BY tool_count DESC 
LIMIT 10;

-- 检查异常数据
SELECT tool_id, url, domain 
FROM tools 
WHERE domain IS NULL OR domain = '' OR LENGTH(domain) < 3;
```

### 示例数据验证
```sql
-- 查看提取结果示例
SELECT tool_id, url, domain 
FROM tools 
WHERE domain IS NOT NULL 
ORDER BY updated_at DESC 
LIMIT 10;
```

## 🛠️ 故障排除

### 常见问题

1. **字段创建失败**
   - 检查数据库权限
   - 确认表名正确
   - 验证数据库连接

2. **域名提取失败**
   - 检查URL格式
   - 验证网络连接
   - 查看错误日志

3. **批量更新缓慢**
   - 检查数据库性能
   - 考虑分批处理
   - 优化索引使用

### 日志分析
```bash
# 查看详细日志
python update_tools_domain.py 2>&1 | tee domain_update.log

# 搜索错误信息
grep "ERROR\|WARNING" domain_update.log

# 统计处理结果
grep "提取域名\|修复域名" domain_update.log | wc -l
```

## 📝 总结

这套域名提取系统成功实现了：

✅ **完整的数据覆盖**: 100% 的工具都有域名信息  
✅ **智能的提取逻辑**: 处理各种复杂URL格式  
✅ **高效的批量处理**: 快速处理2000+条记录  
✅ **完善的错误处理**: 自动处理异常情况  
✅ **详细的统计分析**: 提供全面的数据洞察  

系统现在可以支持基于域名的工具分析、流量数据关联和竞品分析等高级功能。
