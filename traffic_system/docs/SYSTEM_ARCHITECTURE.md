# 流量数据分析系统架构文档

## 🎯 系统架构概览

### 整体架构图

```
┌─────────────────────────────────────────────────────────────────┐
│                    AI工具流量数据分析系统                          │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐         │
│  │   数据采集   │    │   数据处理   │    │   数据分析   │         │
│  │   模块      │────│    模块     │────│    模块     │         │
│  └─────────────┘    └─────────────┘    └─────────────┘         │
│         │                   │                   │              │
│         ▼                   ▼                   ▼              │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐         │
│  │Traffic.cv   │    │PostgreSQL   │    │   统计报表   │         │
│  │数据源       │    │数据库       │    │   可视化     │         │
│  └─────────────┘    └─────────────┘    └─────────────┘         │
│                                                                 │
├─────────────────────────────────────────────────────────────────┤
│                        核心组件                                  │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │TrafficCVSpider  │  │TrafficDataManager│  │ToolsTrafficSync │ │
│  │                 │  │                 │  │                 │ │
│  │• HTTP请求处理   │  │• 数据解析转换    │  │• 工具域名映射    │ │
│  │• HTML解析      │  │• 数据库操作     │  │• 冲突解决       │ │
│  │• 数据提取      │  │• 错误处理       │  │• 批量同步       │ │
│  │• 重试机制      │  │• 日志记录       │  │• 状态管理       │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

### 数据流程图

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   工具URL   │───▶│  域名提取   │───▶│  流量采集   │───▶│  数据存储   │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │                   │
       ▼                   ▼                   ▼                   ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│tools表      │    │domain字段   │    │Traffic.cv   │    │流量数据表   │
│2071条记录   │    │1944个域名   │    │API响应      │    │7个数据表    │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
```

## 🔧 核心模块详解

### 1. TrafficCVSpider - 数据采集器

**功能职责**:
- 发送HTTP请求到Traffic.cv
- 解析HTML响应内容
- 提取结构化数据
- 处理异常和重试

**核心方法**:
```python
class TrafficCVSpider:
    async def get_traffic_data(self, domain: str) -> Dict[str, Any]
    async def extract_site_overview(self, html: str) -> Dict[str, Any]
    async def extract_monthly_trends(self, html: str) -> List[Dict[str, Any]]
    async def extract_traffic_sources(self, html: str) -> List[Dict[str, Any]]
    async def extract_region_data(self, html: str) -> List[Dict[str, Any]]
    async def extract_keyword_data(self, html: str) -> List[Dict[str, Any]]
    async def extract_whois_data(self, html: str) -> Dict[str, Any]
```

**数据提取策略**:
- **正则表达式匹配**: 提取JavaScript中的JSON数据
- **HTML解析**: 使用BeautifulSoup解析表格和列表
- **文本处理**: 清理和标准化数据格式
- **错误恢复**: 部分数据失败不影响整体处理

### 2. TrafficDataManager - 数据管理器

**功能职责**:
- 管理数据库连接池
- 执行数据转换和清洗
- 处理数据入库操作
- 维护数据一致性

**核心方法**:
```python
class TrafficDataManager:
    async def process_domain_traffic(self, domain: str) -> Dict[str, Any]
    async def save_site_overview(self, data: Dict[str, Any]) -> bool
    async def save_monthly_trends(self, data: List[Dict[str, Any]]) -> bool
    async def save_traffic_sources(self, data: List[Dict[str, Any]]) -> bool
    async def save_region_distribution(self, data: List[Dict[str, Any]]) -> bool
    async def save_keyword_analysis(self, data: List[Dict[str, Any]]) -> bool
    async def save_whois_info(self, data: Dict[str, Any]) -> bool
```

**数据转换规则**:
- **数值处理**: 将字符串数值转换为数字类型
- **日期处理**: 统一日期格式为ISO标准
- **文本清理**: 移除HTML标签和特殊字符
- **数据验证**: 检查必填字段和数据范围

### 3. ToolsTrafficSyncer - 工具流量同步器

**功能职责**:
- 解决多工具对应同域名问题
- 管理域名-工具映射关系
- 执行批量同步操作
- 维护数据一致性

**核心方法**:
```python
class ToolsTrafficSyncer:
    async def sync_all_domains(self, limit: int = None) -> Dict[str, Any]
    async def sync_traffic_data_for_domain(self, domain: str, tool_id: str) -> Dict[str, Any]
    def select_primary_tool(self, tools: List[Dict[str, Any]]) -> Dict[str, Any]
    async def create_domain_tool_mapping(self, domain: str, primary_tool_id: str, related_tools: List[str]) -> bool
```

**主工具选择算法**:
1. **过滤扩展程序**: 排除Chrome扩展等衍生产品
2. **时间优先**: 选择创建时间最早的工具
3. **字典序**: 相同时间按tool_id字典序排序
4. **人工干预**: 支持手动指定特殊情况

## 📊 数据库设计详解

### 表结构设计

#### 1. traffic_site_overview - 网站概览
```sql
CREATE TABLE traffic_site_overview (
    id SERIAL PRIMARY KEY,
    tool_id VARCHAR(50) NOT NULL,
    domain VARCHAR(255) NOT NULL,
    global_rank INTEGER,
    country_rank INTEGER,
    country_code VARCHAR(10),
    total_visits BIGINT,
    total_visits_raw VARCHAR(50),
    visits_change_percent DECIMAL(5,2),
    avg_duration_seconds INTEGER,
    avg_duration_raw VARCHAR(20),
    pages_per_visit DECIMAL(3,2),
    bounce_rate DECIMAL(5,2),
    domain_creation_date DATE,
    domain_expiration_date DATE,
    domain_last_changed DATE,
    domain_age_years DECIMAL(4,1),
    data_source VARCHAR(50) DEFAULT 'traffic.cv',
    source_url TEXT,
    extraction_method VARCHAR(50) DEFAULT 'html_parsing',
    stat_date DATE DEFAULT CURRENT_DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT uk_site_overview_tool_domain UNIQUE (tool_id, domain)
);
```

#### 2. domain_tool_mapping - 域名工具映射
```sql
CREATE TABLE domain_tool_mapping (
    id SERIAL PRIMARY KEY,
    domain VARCHAR(255) NOT NULL,
    primary_tool_id VARCHAR(50) NOT NULL,
    related_tool_ids TEXT[] NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT uk_domain_mapping UNIQUE (domain)
);
```

### 索引优化

```sql
-- 性能优化索引
CREATE INDEX idx_traffic_site_overview_domain ON traffic_site_overview(domain);
CREATE INDEX idx_traffic_site_overview_tool_id ON traffic_site_overview(tool_id);
CREATE INDEX idx_traffic_monthly_trends_domain_month ON traffic_monthly_trends(domain, month);
CREATE INDEX idx_domain_tool_mapping_primary_tool ON domain_tool_mapping(primary_tool_id);

-- 复合索引
CREATE INDEX idx_traffic_overview_domain_date ON traffic_site_overview(domain, stat_date);
CREATE INDEX idx_traffic_trends_tool_month ON traffic_monthly_trends(tool_id, month);
```

## 🔄 数据处理流程

### 1. 域名提取流程

```
tools表URL → 域名解析 → 格式验证 → 去重处理 → 更新domain字段
     │            │          │          │            │
     ▼            ▼          ▼          ▼            ▼
原始URL数据   urlparse解析  正则验证   子域名处理   批量更新
2071条记录   提取hostname  域名格式   移除www等   1944个域名
```

### 2. 流量数据采集流程

```
域名列表 → HTTP请求 → HTML解析 → 数据提取 → 格式转换 → 数据入库
    │         │         │         │         │         │
    ▼         ▼         ▼         ▼         ▼         ▼
1944个域名  Traffic.cv  BeautifulSoup  正则匹配  类型转换  PostgreSQL
批量处理   异步请求    HTML解析     JSON提取   数据清洗   7个数据表
```

### 3. 工具关联流程

```
域名映射 → 主工具选择 → 映射表更新 → 流量数据关联 → 一致性检查
    │          │           │            │             │
    ▼          ▼           ▼            ▼             ▼
多工具域名   选择算法    mapping表     更新tool_id    数据验证
127个重复   时间优先    唯一约束      批量更新       完整性检查
```

## 🚀 性能优化

### 1. 数据库优化

**连接池配置**:
```python
pool_config = {
    'min_size': 2,
    'max_size': 10,
    'max_queries': 50000,
    'max_inactive_connection_lifetime': 300
}
```

### 2. 并发控制

**异步处理**:
```python
semaphore = asyncio.Semaphore(5)
tasks = [process_domain(domain) for domain in domains]
results = await asyncio.gather(*tasks, return_exceptions=True)
```

### 3. 内存管理

**数据分批处理**:
```python
batch_size = 100
for i in range(0, len(domains), batch_size):
    batch = domains[i:i + batch_size]
    await process_batch(batch)
```

## 📈 监控和告警

### 1. 关键指标

**系统性能指标**:
- 数据采集成功率 (目标: >95%)
- 平均响应时间 (目标: <5秒)
- 数据库连接数 (监控: 连接池使用率)
- 内存使用率 (告警: >80%)

**业务指标**:
- 域名覆盖率 (当前: 100%)
- 数据完整性 (检查: 必填字段)
- 更新频率 (建议: 每日更新)
- 错误率 (告警: >5%)

### 2. 日志管理

**日志级别**:
- DEBUG: 详细调试信息
- INFO: 正常操作信息
- WARNING: 警告信息
- ERROR: 错误信息
- CRITICAL: 严重错误

---

**文档版本**: 1.0.0  
**最后更新**: 2025-07-23  
**维护团队**: AI工具平台团队
