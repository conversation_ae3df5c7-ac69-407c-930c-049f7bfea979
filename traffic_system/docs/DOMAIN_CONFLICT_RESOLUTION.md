# 域名冲突解决策略

## 🎯 问题描述

在 `tools` 表中，多个不同的 `tool_id` 可能对应同一个 `domain` 值。这种情况在流量数据入库时会产生以下问题：

1. **数据唯一性冲突**: 流量表中 `domain` 和 `tool_id` 需要保持唯一对应关系
2. **数据混淆风险**: 不同工具的流量数据可能被错误关联
3. **查询复杂性**: 难以确定流量数据对应的具体工具

## 🔧 解决方案

### 1. 主要工具选择策略 (Primary Tool Selection)

为每个域名选择一个**主要工具 (Primary Tool)**，作为该域名流量数据的唯一关联对象。

#### 选择规则优先级：
1. **排除扩展程序**: 优先排除Chrome扩展、浏览器插件等衍生产品
2. **创建时间优先**: 选择最早创建的工具 (`created_at` 最小)
3. **字典序排序**: 如果创建时间相同，选择 `tool_id` 字典序最小的
4. **人工干预**: 对于特殊情况，支持手动指定主要工具

#### 实现逻辑：
```python
def select_primary_tool(self, tools: List[Dict[str, Any]]) -> Dict[str, Any]:
    # 1. 过滤扩展程序
    extension_keywords = ['extension', 'chrome', 'browser', 'addon', 'plugin']
    non_extension_tools = [tool for tool in tools 
                          if not any(keyword in tool['tool_id'].lower() 
                                   for keyword in extension_keywords)]
    
    # 2. 选择候选工具
    candidate_tools = non_extension_tools if non_extension_tools else tools
    
    # 3. 按规则排序
    candidate_tools.sort(key=lambda x: (x['created_at'], x['tool_id']))
    
    return candidate_tools[0]
```

### 2. 域名-工具映射表 (Domain-Tool Mapping)

创建专门的映射表记录域名与工具的关系：

```sql
CREATE TABLE domain_tool_mapping (
    id SERIAL PRIMARY KEY,
    domain VARCHAR(255) NOT NULL,
    primary_tool_id VARCHAR(50) NOT NULL,
    related_tool_ids TEXT[] NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT uk_domain_mapping UNIQUE (domain)
);
```

#### 字段说明：
- `domain`: 域名
- `primary_tool_id`: 主要工具ID（用于流量数据关联）
- `related_tool_ids`: 所有相关工具ID数组
- `created_at/updated_at`: 时间戳

### 3. 流量数据关联策略

#### 数据入库规则：
1. **唯一性保证**: 每个域名在流量表中只有一条记录
2. **主要工具关联**: 流量数据的 `tool_id` 字段使用主要工具ID
3. **数据更新**: 如果域名已有流量数据但工具ID不同，更新为主要工具ID
4. **历史数据保持**: 保留原有流量数据，只更新关联关系

#### 实现流程：
```python
async def sync_traffic_data_for_domain(self, domain: str, primary_tool_id: str):
    # 1. 检查现有数据
    existing_tool_id = await self.check_existing_traffic_data(domain)
    
    # 2. 更新关联关系（如果需要）
    if existing_tool_id and existing_tool_id != primary_tool_id:
        await self.update_existing_traffic_tool_id(domain, existing_tool_id, primary_tool_id)
    
    # 3. 获取新的流量数据
    result = await self.traffic_manager.process_domain_traffic(domain)
    
    # 4. 确保正确的tool_id
    await self.ensure_correct_tool_id(domain, primary_tool_id)
```

## 📊 典型案例分析

### 案例1: Chrome扩展商店
**域名**: `chrome.google.com`  
**工具数量**: 58个  
**解决方案**: 
- 所有Chrome扩展都映射到同一域名
- 选择最早创建的非扩展工具作为主要工具
- 流量数据统一关联到主要工具

### 案例2: App Store应用
**域名**: `apps.apple.com`  
**工具数量**: 40个  
**解决方案**:
- 所有iOS应用都映射到App Store域名
- 按创建时间选择主要工具
- 流量数据反映整个App Store的访问情况

### 案例3: 独立工具平台
**域名**: `toolsmart.ai`  
**工具数量**: 3个  
**解决方案**:
- 可能是同一平台的不同功能
- 选择最核心的工具作为主要工具
- 流量数据反映整个平台的访问情况

## 🔍 数据查询策略

### 1. 通过工具查找流量数据
```sql
-- 方法1: 直接查询（主要工具）
SELECT * FROM traffic_site_overview WHERE tool_id = 'target_tool_id';

-- 方法2: 通过映射表查询（任意相关工具）
SELECT tso.* 
FROM traffic_site_overview tso
JOIN domain_tool_mapping dtm ON tso.domain = dtm.domain
WHERE 'target_tool_id' = ANY(dtm.related_tool_ids);
```

### 2. 通过域名查找所有相关工具
```sql
SELECT 
    dtm.domain,
    dtm.primary_tool_id,
    dtm.related_tool_ids,
    tso.monthly_visits,
    tso.bounce_rate
FROM domain_tool_mapping dtm
LEFT JOIN traffic_site_overview tso ON dtm.domain = tso.domain
WHERE dtm.domain = 'target_domain';
```

### 3. 统计分析查询
```sql
-- 域名重复情况统计
SELECT 
    domain,
    array_length(related_tool_ids, 1) as tool_count,
    primary_tool_id
FROM domain_tool_mapping
WHERE array_length(related_tool_ids, 1) > 1
ORDER BY tool_count DESC;
```

## 🚀 实施步骤

### 1. 数据分析阶段
```bash
# 查看统计信息
python sync_tools_traffic.py --stats

# 查看域名映射关系
python sync_tools_traffic.py --mapping
```

### 2. 小规模测试
```bash
# 同步指定域名
python sync_tools_traffic.py --sync-domains chatgpt.com github.com

# 限量同步测试
python sync_tools_traffic.py --sync-all --limit 10
```

### 3. 全量同步
```bash
# 跳过已有数据的同步
python sync_tools_traffic.py --sync-all

# 强制更新所有数据
python sync_tools_traffic.py --sync-all --force
```

## 📈 预期效果

### 数据一致性
- ✅ 每个域名在流量表中只有一条记录
- ✅ 流量数据与主要工具唯一关联
- ✅ 避免数据混淆和重复

### 查询效率
- ✅ 支持通过任意工具ID查找流量数据
- ✅ 支持域名级别的流量分析
- ✅ 支持工具关联关系查询

### 业务价值
- ✅ 准确的流量数据分析
- ✅ 清晰的工具-流量关联关系
- ✅ 支持复杂的业务查询需求

## 🔧 维护和监控

### 定期检查
1. **数据一致性检查**: 定期验证流量数据与映射关系的一致性
2. **新工具处理**: 新增工具时自动更新映射关系
3. **异常数据监控**: 监控异常的流量数据关联

### 手动干预
1. **主要工具调整**: 支持手动调整主要工具选择
2. **特殊情况处理**: 处理自动规则无法覆盖的特殊情况
3. **数据修复**: 提供数据修复和回滚机制

这套解决方案确保了流量数据的唯一性和一致性，同时保持了数据查询的灵活性和业务分析的准确性。
