#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
流量系统统一启动脚本
提供便捷的命令行接口来运行各种流量系统功能
"""

import sys
import os
import argparse
import subprocess
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def run_script(script_name: str, args: list = None):
    """运行指定的脚本"""
    script_path = Path(__file__).parent / "scripts" / script_name
    if not script_path.exists():
        print(f"❌ 脚本不存在: {script_path}")
        return 1
    
    cmd = [sys.executable, str(script_path)]
    if args:
        cmd.extend(args)
    
    try:
        result = subprocess.run(cmd, check=True)
        return result.returncode
    except subprocess.CalledProcessError as e:
        print(f"❌ 脚本执行失败: {e}")
        return e.returncode
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
        return 1


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='AI工具流量数据分析系统统一启动脚本',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 流量数据处理
  python run.py traffic --domain chatgpt.com
  python run.py traffic --batch --limit 10
  
  # 域名管理
  python run.py domain --init
  python run.py domain --update
  python run.py domain --stats
  
  # 工具流量同步
  python run.py sync --stats
  python run.py sync --mapping
  python run.py sync --domains chatgpt.com github.com
  python run.py sync --all --limit 10
  
  # 数据修复
  python run.py fix --whois
  python run.py fix --domains
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 流量数据处理命令
    traffic_parser = subparsers.add_parser('traffic', help='流量数据处理')
    traffic_parser.add_argument('--domain', help='处理指定域名')
    traffic_parser.add_argument('--batch', action='store_true', help='批量处理')
    traffic_parser.add_argument('--limit', type=int, help='限制处理数量')
    traffic_parser.add_argument('--output', help='输出文件路径')
    
    # 域名管理命令
    domain_parser = subparsers.add_parser('domain', help='域名管理')
    domain_parser.add_argument('--init', action='store_true', help='初始化域名字段')
    domain_parser.add_argument('--update', action='store_true', help='更新域名信息')
    domain_parser.add_argument('--stats', action='store_true', help='显示域名统计')
    domain_parser.add_argument('--dry-run', action='store_true', help='预览模式')
    
    # 工具流量同步命令
    sync_parser = subparsers.add_parser('sync', help='工具流量同步')
    sync_parser.add_argument('--stats', action='store_true', help='显示统计信息')
    sync_parser.add_argument('--mapping', action='store_true', help='显示域名映射')
    sync_parser.add_argument('--domains', nargs='+', help='同步指定域名')
    sync_parser.add_argument('--all', action='store_true', help='同步所有域名')
    sync_parser.add_argument('--limit', type=int, help='限制处理数量')
    sync_parser.add_argument('--force', action='store_true', help='强制更新')
    sync_parser.add_argument('--output', help='输出文件路径')
    
    # 数据修复命令
    fix_parser = subparsers.add_parser('fix', help='数据修复')
    fix_parser.add_argument('--whois', action='store_true', help='修复WHOIS数据')
    fix_parser.add_argument('--domains', action='store_true', help='修复缺失域名')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 0
    
    # 根据命令执行相应的脚本
    if args.command == 'traffic':
        script_args = []
        if args.domain:
            script_args.extend(['--domain', args.domain])
        if args.batch:
            script_args.append('--batch')
        if args.limit:
            script_args.extend(['--limit', str(args.limit)])
        if args.output:
            script_args.extend(['--output', args.output])
        
        return run_script('main_traffic_processor.py', script_args)
    
    elif args.command == 'domain':
        if args.init:
            return run_script('init_domain_field.py')
        elif args.update:
            script_args = []
            if args.dry_run:
                script_args.append('--dry-run')
            return run_script('update_tools_domain.py', script_args)
        elif args.stats:
            return run_script('update_tools_domain.py', ['--stats'])
        else:
            print("请指定域名管理操作: --init, --update, 或 --stats")
            return 1
    
    elif args.command == 'sync':
        script_args = []
        if args.stats:
            script_args.append('--stats')
        elif args.mapping:
            script_args.append('--mapping')
        elif args.domains:
            script_args.extend(['--sync-domains'] + args.domains)
        elif args.all:
            script_args.append('--sync-all')
        
        if args.limit:
            script_args.extend(['--limit', str(args.limit)])
        if args.force:
            script_args.append('--force')
        if args.output:
            script_args.extend(['--output', args.output])
        
        return run_script('sync_tools_traffic.py', script_args)
    
    elif args.command == 'fix':
        if args.whois:
            return run_script('fix_whois_data.py')
        elif args.domains:
            return run_script('fix_missing_domains.py')
        else:
            print("请指定修复操作: --whois 或 --domains")
            return 1
    
    else:
        print(f"未知命令: {args.command}")
        return 1


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 程序异常退出: {e}")
        sys.exit(1)
