#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
流量系统集成测试脚本
验证整个系统的功能完整性
"""

import sys
import os
import asyncio
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from traffic_system.core import TrafficDataManager, TrafficCVSpider, ToolsTrafficSyncer, DatabaseConfig

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class SystemTester:
    """系统测试器"""
    
    def __init__(self):
        self.db_config = DatabaseConfig()
        self.test_results = {
            'database_connection': False,
            'traffic_spider': False,
            'data_manager': False,
            'tools_syncer': False,
            'domain_extraction': False,
            'data_integrity': False
        }
    
    async def test_database_connection(self):
        """测试数据库连接"""
        try:
            logger.info("🔍 测试数据库连接...")
            manager = TrafficDataManager(self.db_config)
            await manager.init_db_pool()
            
            # 测试基本查询
            async with manager.db_pool.acquire() as conn:
                result = await conn.fetchval("SELECT 1")
                if result == 1:
                    self.test_results['database_connection'] = True
                    logger.info("✅ 数据库连接测试通过")
                else:
                    logger.error("❌ 数据库查询结果异常")
            
            await manager.close_db_pool()
            
        except Exception as e:
            logger.error(f"❌ 数据库连接测试失败: {e}")
    
    async def test_traffic_spider(self):
        """测试流量数据采集器"""
        try:
            logger.info("🔍 测试流量数据采集器...")
            spider = TrafficCVSpider()
            
            # 测试数据采集
            test_domain = "example.com"
            data = await spider.get_traffic_data(test_domain)
            
            if data and 'success' in data:
                self.test_results['traffic_spider'] = True
                logger.info("✅ 流量数据采集器测试通过")
                logger.info(f"   采集到数据类型: {list(data.keys())}")
            else:
                logger.error("❌ 流量数据采集失败")
                
        except Exception as e:
            logger.error(f"❌ 流量数据采集器测试失败: {e}")
    
    async def test_data_manager(self):
        """测试数据管理器"""
        try:
            logger.info("🔍 测试数据管理器...")
            manager = TrafficDataManager(self.db_config)
            await manager.init_db_pool()
            
            # 测试数据处理
            test_domain = "test-domain.com"
            result = await manager.process_domain_traffic(test_domain)
            
            if result and 'success' in result:
                self.test_results['data_manager'] = True
                logger.info("✅ 数据管理器测试通过")
                logger.info(f"   处理结果: {result.get('success', False)}")
            else:
                logger.error("❌ 数据管理器测试失败")
            
            await manager.close_db_pool()
            
        except Exception as e:
            logger.error(f"❌ 数据管理器测试失败: {e}")
    
    async def test_tools_syncer(self):
        """测试工具流量同步器"""
        try:
            logger.info("🔍 测试工具流量同步器...")
            syncer = ToolsTrafficSyncer(self.db_config)
            await syncer.init_db_pool()
            
            # 测试统计功能
            stats = await syncer.get_sync_statistics()
            
            if stats and 'total_tools' in stats:
                self.test_results['tools_syncer'] = True
                logger.info("✅ 工具流量同步器测试通过")
                logger.info(f"   统计信息: 总工具数={stats.get('total_tools', 0)}")
            else:
                logger.error("❌ 工具流量同步器测试失败")
            
            await syncer.close_db_pool()
            
        except Exception as e:
            logger.error(f"❌ 工具流量同步器测试失败: {e}")
    
    async def test_domain_extraction(self):
        """测试域名提取功能"""
        try:
            logger.info("🔍 测试域名提取功能...")
            
            # 导入域名提取器
            from traffic_system.scripts.update_tools_domain import DomainExtractor
            
            extractor = DomainExtractor()
            
            # 测试各种URL格式
            test_urls = [
                "https://www.example.com",
                "http://app.test.com/path",
                "chrome.google.com/webstore/detail/...",
                "https://subdomain.domain.co.uk"
            ]
            
            success_count = 0
            for url in test_urls:
                domain = extractor.extract_domain(url)
                if domain:
                    success_count += 1
                    logger.info(f"   {url} -> {domain}")
            
            if success_count >= len(test_urls) * 0.8:  # 80%成功率
                self.test_results['domain_extraction'] = True
                logger.info("✅ 域名提取功能测试通过")
            else:
                logger.error("❌ 域名提取功能测试失败")
                
        except Exception as e:
            logger.error(f"❌ 域名提取功能测试失败: {e}")
    
    async def test_data_integrity(self):
        """测试数据完整性"""
        try:
            logger.info("🔍 测试数据完整性...")
            manager = TrafficDataManager(self.db_config)
            await manager.init_db_pool()
            
            async with manager.db_pool.acquire() as conn:
                # 检查表是否存在
                tables = [
                    'traffic_site_overview',
                    'traffic_monthly_trends', 
                    'traffic_source_analysis',
                    'traffic_region_distribution',
                    'traffic_keyword_analysis',
                    'traffic_domain_whois',
                    'traffic_extraction_logs'
                ]
                
                existing_tables = []
                for table in tables:
                    exists = await conn.fetchval("""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables 
                            WHERE table_name = $1
                        )
                    """, table)
                    if exists:
                        existing_tables.append(table)
                
                if len(existing_tables) >= len(tables) * 0.8:  # 80%的表存在
                    self.test_results['data_integrity'] = True
                    logger.info("✅ 数据完整性测试通过")
                    logger.info(f"   存在的表: {len(existing_tables)}/{len(tables)}")
                else:
                    logger.error("❌ 数据完整性测试失败")
                    logger.error(f"   缺失的表: {set(tables) - set(existing_tables)}")
            
            await manager.close_db_pool()
            
        except Exception as e:
            logger.error(f"❌ 数据完整性测试失败: {e}")
    
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("🚀 开始系统集成测试...")
        logger.info("=" * 60)
        
        # 按顺序执行测试
        await self.test_database_connection()
        await self.test_data_integrity()
        await self.test_traffic_spider()
        await self.test_data_manager()
        await self.test_tools_syncer()
        await self.test_domain_extraction()
        
        # 输出测试结果
        logger.info("=" * 60)
        logger.info("📊 测试结果汇总:")
        
        passed = 0
        total = len(self.test_results)
        
        for test_name, result in self.test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            logger.info(f"   {test_name}: {status}")
            if result:
                passed += 1
        
        logger.info("=" * 60)
        logger.info(f"📈 测试通过率: {passed}/{total} ({passed/total*100:.1f}%)")
        
        if passed == total:
            logger.info("🎉 所有测试通过！系统运行正常！")
            return True
        else:
            logger.warning(f"⚠️ 有 {total-passed} 个测试失败，请检查系统配置")
            return False
    
    def print_system_info(self):
        """打印系统信息"""
        logger.info("🔧 系统信息:")
        logger.info(f"   Python版本: {sys.version}")
        logger.info(f"   项目路径: {project_root}")
        logger.info(f"   数据库配置: {self.db_config.host}:{self.db_config.port}/{self.db_config.database}")


async def main():
    """主函数"""
    tester = SystemTester()
    
    # 打印系统信息
    tester.print_system_info()
    
    # 运行测试
    success = await tester.run_all_tests()
    
    # 返回退出码
    return 0 if success else 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        logger.info("\n⚠️ 用户中断测试")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ 测试程序异常退出: {e}")
        sys.exit(1)
