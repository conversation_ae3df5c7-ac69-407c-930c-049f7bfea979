-- 流量统计数据库表结构
-- 创建时间: 2025-07-23
-- 描述: 用于存储工具网站的流量分析数据

-- 1. 网站流量概览表
CREATE TABLE IF NOT EXISTS traffic_site_overview (
    id SERIAL PRIMARY KEY,
    tool_id VARCHAR(50) NOT NULL,                    -- 关联tools表的tool_id
    domain VARCHAR(255) NOT NULL,                    -- 网站域名
    global_rank INTEGER,                             -- 全球排名
    country_rank INTEGER,                            -- 国家排名
    country_code VARCHAR(3) DEFAULT 'US',            -- 国家代码
    total_visits BIGINT DEFAULT 0,                   -- 总访问量
    total_visits_raw VARCHAR(20),                    -- 原始访问量字符串
    visits_change_percent DECIMAL(6,2),              -- 访问量变化百分比
    avg_duration_seconds INTEGER,                    -- 平均访问时长(秒)
    avg_duration_raw VARCHAR(20),                    -- 原始时长格式
    pages_per_visit DECIMAL(5,2),                    -- 每次访问页数
    bounce_rate DECIMAL(5,2),                        -- 跳出率
    domain_creation_date DATE,                       -- 域名创建日期
    domain_expiration_date DATE,                     -- 域名过期日期
    domain_last_changed DATE,                        -- 域名最后修改日期
    domain_age_years DECIMAL(4,2),                   -- 域名年龄
    data_source VARCHAR(50) DEFAULT 'traffic.cv',    -- 数据来源
    source_url TEXT,                                 -- 数据源URL
    extraction_method VARCHAR(100),                  -- 提取方法
    stat_date DATE NOT NULL,                         -- 统计日期
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT uk_traffic_overview_tool_date UNIQUE (tool_id, stat_date),
    CONSTRAINT chk_bounce_rate CHECK (bounce_rate >= 0 AND bounce_rate <= 100)
);

-- 2. 月度流量趋势表
CREATE TABLE IF NOT EXISTS traffic_monthly_trends (
    id SERIAL PRIMARY KEY,
    tool_id VARCHAR(50) NOT NULL,
    period VARCHAR(7) NOT NULL,                      -- 时间周期 (如"2025-06")
    year_month DATE NOT NULL,                        -- 标准化的年月日期
    visits BIGINT DEFAULT 0,                         -- 月访问量
    visits_raw VARCHAR(20),                          -- 原始访问量字符串
    growth_rate DECIMAL(8,4),                        -- 环比增长率
    data_source VARCHAR(50) DEFAULT 'traffic.cv',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT uk_traffic_trends_tool_period UNIQUE (tool_id, period)
);

-- 3. 流量来源分析表
CREATE TABLE IF NOT EXISTS traffic_source_analysis (
    id SERIAL PRIMARY KEY,
    tool_id VARCHAR(50) NOT NULL,
    source_type VARCHAR(50) NOT NULL,                -- direct, search, referrals, social, paidReferrals, mail
    traffic_percent DECIMAL(5,2) NOT NULL,           -- 流量占比
    traffic_percent_raw VARCHAR(10),                 -- 原始百分比字符串
    estimated_visits BIGINT,                         -- 估算访问量
    stat_date DATE NOT NULL,
    data_source VARCHAR(50) DEFAULT 'traffic.cv',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT uk_traffic_sources_tool_type_date UNIQUE (tool_id, source_type, stat_date),
    CONSTRAINT chk_traffic_percent CHECK (traffic_percent >= 0 AND traffic_percent <= 100)
);

-- 4. 地区流量分布表
CREATE TABLE IF NOT EXISTS traffic_region_distribution (
    id SERIAL PRIMARY KEY,
    tool_id VARCHAR(50) NOT NULL,
    region_name VARCHAR(100) NOT NULL,               -- 地区名称
    country_code VARCHAR(3),                         -- ISO 3166-1 alpha-3 国家代码
    traffic_percent DECIMAL(5,2) NOT NULL,           -- 流量占比
    traffic_percent_raw VARCHAR(10),                 -- 原始百分比字符串
    estimated_visits BIGINT,                         -- 估算访问量
    rank_in_region INTEGER,                          -- 在该地区的排名
    stat_date DATE NOT NULL,
    data_source VARCHAR(50) DEFAULT 'traffic.cv',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT uk_traffic_regions_tool_region_date UNIQUE (tool_id, region_name, stat_date),
    CONSTRAINT chk_region_percent CHECK (traffic_percent >= 0 AND traffic_percent <= 100)
);

-- 5. 关键词流量分析表
CREATE TABLE IF NOT EXISTS traffic_keyword_analysis (
    id SERIAL PRIMARY KEY,
    tool_id VARCHAR(50) NOT NULL,
    keyword VARCHAR(255) NOT NULL,                   -- 关键词
    keyword_type VARCHAR(20) DEFAULT 'organic',      -- organic, paid
    traffic_percent DECIMAL(5,2),                    -- 流量占比
    traffic_percent_raw VARCHAR(10),                 -- 原始百分比字符串
    estimated_visits BIGINT,                         -- 估算访问量
    search_volume BIGINT,                            -- 搜索量
    keyword_difficulty INTEGER,                      -- 关键词难度 (1-100)
    average_position DECIMAL(4,1),                   -- 平均排名位置
    stat_date DATE NOT NULL,
    data_source VARCHAR(50) DEFAULT 'traffic.cv',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT uk_traffic_keywords_tool_keyword_date UNIQUE (tool_id, keyword, keyword_type, stat_date),
    CONSTRAINT chk_keyword_percent CHECK (traffic_percent IS NULL OR (traffic_percent >= 0 AND traffic_percent <= 100))
);

-- 6. 域名WHOIS信息表
CREATE TABLE IF NOT EXISTS traffic_domain_whois (
    id SERIAL PRIMARY KEY,
    tool_id VARCHAR(50) NOT NULL,
    domain VARCHAR(255) NOT NULL,
    registrar TEXT,                                  -- 注册商
    creation_date DATE,                              -- 创建日期
    expiration_date DATE,                            -- 过期日期
    updated_date DATE,                               -- 更新日期
    domain_status TEXT,                              -- 域名状态
    name_servers TEXT[],                             -- 名称服务器数组
    dnssec_status VARCHAR(50),                       -- DNSSEC状态
    whois_raw_text TEXT,                             -- 原始WHOIS文本
    whois_json JSONB,                                -- WHOIS JSON数据
    data_source VARCHAR(50) DEFAULT 'traffic.cv',
    extracted_at TIMESTAMP WITH TIME ZONE NOT NULL,  -- 提取时间
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT uk_traffic_whois_tool_domain UNIQUE (tool_id, domain)
);

-- 7. 数据提取日志表
CREATE TABLE IF NOT EXISTS traffic_extraction_logs (
    id SERIAL PRIMARY KEY,
    tool_id VARCHAR(50) NOT NULL,
    domain VARCHAR(255) NOT NULL,
    extraction_status VARCHAR(20) NOT NULL,          -- success, failed, partial
    data_source VARCHAR(50) NOT NULL,
    source_url TEXT,
    extraction_method VARCHAR(100),
    response_time_ms INTEGER,                        -- 响应时间(毫秒)
    data_points_extracted INTEGER DEFAULT 0,         -- 提取的数据点数量
    error_message TEXT,                              -- 错误信息
    raw_response JSONB,                              -- 原始响应数据
    extracted_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引以优化查询性能
-- 网站概览表索引
CREATE INDEX IF NOT EXISTS idx_traffic_overview_tool_id ON traffic_site_overview(tool_id);
CREATE INDEX IF NOT EXISTS idx_traffic_overview_date ON traffic_site_overview(stat_date DESC);
CREATE INDEX IF NOT EXISTS idx_traffic_overview_domain ON traffic_site_overview(domain);
CREATE INDEX IF NOT EXISTS idx_traffic_overview_global_rank ON traffic_site_overview(global_rank) WHERE global_rank IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_traffic_overview_visits ON traffic_site_overview(total_visits DESC);

-- 月度趋势表索引
CREATE INDEX IF NOT EXISTS idx_traffic_trends_tool_id ON traffic_monthly_trends(tool_id);
CREATE INDEX IF NOT EXISTS idx_traffic_trends_period ON traffic_monthly_trends(year_month DESC);
CREATE INDEX IF NOT EXISTS idx_traffic_trends_visits ON traffic_monthly_trends(visits DESC);

-- 流量来源表索引
CREATE INDEX IF NOT EXISTS idx_traffic_sources_tool_id ON traffic_source_analysis(tool_id);
CREATE INDEX IF NOT EXISTS idx_traffic_sources_type ON traffic_source_analysis(source_type);
CREATE INDEX IF NOT EXISTS idx_traffic_sources_percent ON traffic_source_analysis(traffic_percent DESC);
CREATE INDEX IF NOT EXISTS idx_traffic_sources_date ON traffic_source_analysis(stat_date DESC);

-- 地区分布表索引
CREATE INDEX IF NOT EXISTS idx_traffic_regions_tool_id ON traffic_region_distribution(tool_id);
CREATE INDEX IF NOT EXISTS idx_traffic_regions_country ON traffic_region_distribution(country_code);
CREATE INDEX IF NOT EXISTS idx_traffic_regions_percent ON traffic_region_distribution(traffic_percent DESC);
CREATE INDEX IF NOT EXISTS idx_traffic_regions_date ON traffic_region_distribution(stat_date DESC);

-- 关键词分析表索引
CREATE INDEX IF NOT EXISTS idx_traffic_keywords_tool_id ON traffic_keyword_analysis(tool_id);
CREATE INDEX IF NOT EXISTS idx_traffic_keywords_keyword ON traffic_keyword_analysis(keyword);
CREATE INDEX IF NOT EXISTS idx_traffic_keywords_percent ON traffic_keyword_analysis(traffic_percent DESC);
CREATE INDEX IF NOT EXISTS idx_traffic_keywords_volume ON traffic_keyword_analysis(search_volume DESC) WHERE search_volume IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_traffic_keywords_date ON traffic_keyword_analysis(stat_date DESC);

-- WHOIS信息表索引
CREATE INDEX IF NOT EXISTS idx_traffic_whois_tool_id ON traffic_domain_whois(tool_id);
CREATE INDEX IF NOT EXISTS idx_traffic_whois_domain ON traffic_domain_whois(domain);
CREATE INDEX IF NOT EXISTS idx_traffic_whois_registrar ON traffic_domain_whois(registrar);
CREATE INDEX IF NOT EXISTS idx_traffic_whois_creation ON traffic_domain_whois(creation_date);

-- 提取日志表索引
CREATE INDEX IF NOT EXISTS idx_traffic_logs_tool_id ON traffic_extraction_logs(tool_id);
CREATE INDEX IF NOT EXISTS idx_traffic_logs_status ON traffic_extraction_logs(extraction_status);
CREATE INDEX IF NOT EXISTS idx_traffic_logs_extracted_at ON traffic_extraction_logs(extracted_at DESC);
CREATE INDEX IF NOT EXISTS idx_traffic_logs_domain ON traffic_extraction_logs(domain);

-- 添加表注释
COMMENT ON TABLE traffic_site_overview IS '网站流量概览表：存储工具网站的基础流量指标、排名信息和域名信息';
COMMENT ON TABLE traffic_monthly_trends IS '月度流量趋势表：存储工具网站的历史访问量数据，支持趋势分析和环比计算';
COMMENT ON TABLE traffic_source_analysis IS '流量来源分析表：存储工具网站的流量来源分布，包括直接访问、搜索、推荐等';
COMMENT ON TABLE traffic_region_distribution IS '地区流量分布表：存储工具网站在不同国家和地区的访问量分布情况';
COMMENT ON TABLE traffic_keyword_analysis IS '关键词流量分析表：存储工具网站通过搜索关键词获得的流量数据和排名信息';
COMMENT ON TABLE traffic_domain_whois IS '域名WHOIS信息表：存储工具网站的域名注册信息、技术配置和历史记录';
COMMENT ON TABLE traffic_extraction_logs IS '数据提取日志表：记录流量数据采集的执行状态、性能指标和错误信息';
