-- 为tools表添加domain字段的SQL脚本
-- 创建时间: 2025-07-23
-- 描述: 添加domain字段用于存储从url解析出的域名信息

-- 1. 添加domain字段
ALTER TABLE tools ADD COLUMN IF NOT EXISTS domain VARCHAR(255);

-- 2. 为domain字段添加注释
COMMENT ON COLUMN tools.domain IS '从url字段解析出的域名，如: chatgpt.com, github.com';

-- 3. 创建索引以优化查询性能
CREATE INDEX IF NOT EXISTS idx_tools_domain ON tools(domain);

-- 4. 查看表结构确认字段已添加
SELECT column_name, data_type, character_maximum_length, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'tools' AND column_name = 'domain';

-- 5. 显示当前tools表的记录数
SELECT COUNT(*) as total_tools FROM tools;
