-- 修复数据库字段精度溢出问题
-- 将可能溢出的DECIMAL字段扩大精度范围
-- 创建时间: 2025-07-23

-- 1. 修复 traffic_site_overview 表中的字段精度
ALTER TABLE traffic_site_overview 
ALTER COLUMN visits_change_percent TYPE DECIMAL(10,2);

ALTER TABLE traffic_site_overview 
ALTER COLUMN pages_per_visit TYPE DECIMAL(8,2);

ALTER TABLE traffic_site_overview 
ALTER COLUMN bounce_rate TYPE DECIMAL(8,2);

ALTER TABLE traffic_site_overview 
ALTER COLUMN domain_age_years TYPE DECIMAL(8,2);

-- 2. 修复 traffic_monthly_trends 表中的字段精度
ALTER TABLE traffic_monthly_trends 
ALTER COLUMN growth_rate TYPE DECIMAL(12,4);

-- 3. 修复 traffic_source_analysis 表中的字段精度
ALTER TABLE traffic_source_analysis 
ALTER COLUMN traffic_percent TYPE DECIMAL(8,2);

-- 4. 修复 traffic_region_distribution 表中的字段精度
ALTER TABLE traffic_region_distribution 
ALTER COLUMN traffic_percent TYPE DECIMAL(8,2);

-- 5. 修复 traffic_keyword_analysis 表中的字段精度
ALTER TABLE traffic_keyword_analysis 
ALTER COLUMN traffic_percent TYPE DECIMAL(8,2);

ALTER TABLE traffic_keyword_analysis 
ALTER COLUMN average_position TYPE DECIMAL(8,2);

-- 6. 验证修改结果
SELECT 
    table_name, 
    column_name, 
    data_type, 
    numeric_precision, 
    numeric_scale
FROM information_schema.columns 
WHERE table_name IN (
    'traffic_site_overview', 
    'traffic_monthly_trends', 
    'traffic_source_analysis', 
    'traffic_region_distribution', 
    'traffic_keyword_analysis'
) 
AND data_type = 'numeric'
ORDER BY table_name, column_name;
