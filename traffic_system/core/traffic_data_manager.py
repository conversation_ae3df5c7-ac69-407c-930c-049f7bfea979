#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
流量数据管理器
整合流量数据采集、解析和数据库存储功能
"""

import asyncio
import asyncpg
import json
import re
from datetime import datetime, date
from decimal import Decimal
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import logging
from .traffic_cv_spider import TrafficCVSpider

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


@dataclass
class DatabaseConfig:
    """数据库配置"""
    host: str = "127.0.0.1"
    port: int = 5432
    database: str = "aistak_db"
    user: str = "root"
    password: str = "fuwenhao"


class TrafficDataManager:
    """流量数据管理器"""
    
    def __init__(self, db_config: DatabaseConfig):
        self.db_config = db_config
        self.spider = TrafficCVSpider()
        self.db_pool = None
    
    async def init_db_pool(self):
        """初始化数据库连接池"""
        try:
            self.db_pool = await asyncpg.create_pool(
                host=self.db_config.host,
                port=self.db_config.port,
                database=self.db_config.database,
                user=self.db_config.user,
                password=self.db_config.password,
                min_size=1,
                max_size=10
            )
            logger.info("数据库连接池初始化成功")
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise
    
    async def close_db_pool(self):
        """关闭数据库连接池"""
        if self.db_pool:
            await self.db_pool.close()
            logger.info("数据库连接池已关闭")
    
    def convert_visits_to_number(self, visits_str: str) -> int:
        """将访问量字符串转换为数字"""
        if not visits_str:
            return 0

        visits_str = visits_str.strip().upper()

        try:
            # 处理特殊格式如 "2.165.78K" -> "2165.78K"
            if visits_str.count('.') > 1:
                # 移除第一个小数点（千位分隔符）
                parts = visits_str.split('.')
                if len(parts) >= 3:
                    visits_str = parts[0] + parts[1] + '.' + '.'.join(parts[2:])

            if visits_str.endswith('M'):
                return int(float(visits_str[:-1]) * 1_000_000)
            elif visits_str.endswith('K'):
                return int(float(visits_str[:-1]) * 1_000)
            elif visits_str.endswith('B'):
                return int(float(visits_str[:-1]) * 1_000_000_000)
            else:
                # 移除逗号并转换为整数
                return int(visits_str.replace(',', ''))
        except (ValueError, TypeError):
            logger.warning(f"无法转换访问量字符串: {visits_str}")
            return 0
    
    def convert_duration_to_seconds(self, duration_str: str) -> int:
        """将时长字符串转换为秒数"""
        if not duration_str:
            return 0
        
        try:
            parts = duration_str.split(':')
            if len(parts) == 3:
                hours, minutes, seconds = map(int, parts)
                return hours * 3600 + minutes * 60 + seconds
            return 0
        except (ValueError, TypeError):
            logger.warning(f"无法转换时长字符串: {duration_str}")
            return 0
    
    def extract_percentage(self, percent_str: str) -> Optional[Decimal]:
        """提取百分比数值"""
        if not percent_str:
            return None
        
        try:
            # 移除%符号和其他字符，只保留数字和小数点
            cleaned = re.sub(r'[^\d.-]', '', percent_str)
            if cleaned:
                return Decimal(cleaned)
            return None
        except (ValueError, TypeError):
            logger.warning(f"无法转换百分比字符串: {percent_str}")
            return None
    
    def parse_date(self, date_str: str) -> Optional[date]:
        """解析日期字符串"""
        if not date_str:
            return None

        try:
            # 尝试多种日期格式
            formats = ['%Y-%m-%d', '%Y-%m-%d %H:%M:%S', '%Y/%m/%d']
            for fmt in formats:
                try:
                    return datetime.strptime(date_str.strip(), fmt).date()
                except ValueError:
                    continue
            return None
        except Exception:
            logger.warning(f"无法解析日期字符串: {date_str}")
            return None

    def parse_whois_date(self, date_str: str) -> Optional[date]:
        """解析WHOIS日期字符串"""
        if not date_str:
            return None

        try:
            # 清理日期字符串，移除多余的空格和字符
            date_str = date_str.strip()

            # 尝试多种WHOIS日期格式
            formats = [
                '%Y-%m-%d %H:%M:%S',  # 2022-11-30 23:59:19
                '%Y-%m-%d',           # 2022-11-30
                '%d-%m-%Y %H:%M:%S',  # 30-11-2022 23:59:19
                '%d-%m-%Y',           # 30-11-2022
                '%Y/%m/%d %H:%M:%S',  # 2022/11/30 23:59:19
                '%Y/%m/%d',           # 2022/11/30
            ]

            for fmt in formats:
                try:
                    return datetime.strptime(date_str, fmt).date()
                except ValueError:
                    continue

            logger.warning(f"无法解析WHOIS日期字符串: {date_str}")
            return None
        except Exception as e:
            logger.warning(f"解析WHOIS日期时出错: {e}")
            return None
    
    async def get_or_create_tool_id(self, domain: str) -> Optional[str]:
        """根据域名获取或创建tool_id"""
        async with self.db_pool.acquire() as conn:
            # 首先尝试从tools表中查找匹配的域名
            result = await conn.fetchrow(
                "SELECT tool_id FROM tools WHERE url ILIKE $1 OR url ILIKE $2",
                f"%{domain}%", f"%{domain.replace('www.', '')}%"
            )
            
            if result:
                return result['tool_id']
            
            # 如果没找到，创建一个基于域名的tool_id
            tool_id = domain.replace('.', '_').replace('-', '_')
            logger.info(f"为域名 {domain} 创建新的 tool_id: {tool_id}")
            return tool_id
    
    async def save_site_overview(self, tool_id: str, data: Dict[str, Any], stat_date: date):
        """保存网站流量概览数据"""
        try:
            basic_info = data.get('basic_info', {})
            traffic_metrics = data.get('traffic_metrics', {})
            domain_info = data.get('domain_info', {})
            
            # 转换数据
            global_rank = None
            if basic_info.get('global_rank'):
                try:
                    global_rank = int(basic_info['global_rank'].replace(',', ''))
                except (ValueError, AttributeError):
                    pass
            
            country_rank = None
            if basic_info.get('country_rank'):
                try:
                    country_rank = int(basic_info['country_rank'].replace(',', ''))
                except (ValueError, AttributeError):
                    pass
            
            total_visits = self.convert_visits_to_number(traffic_metrics.get('total_visits', ''))
            avg_duration_seconds = self.convert_duration_to_seconds(traffic_metrics.get('avg_duration', ''))
            visits_change_percent = self.extract_percentage(traffic_metrics.get('total_visits_change', ''))
            pages_per_visit = None
            if traffic_metrics.get('pages_per_visit'):
                try:
                    pages_per_visit = Decimal(traffic_metrics['pages_per_visit'])
                except (ValueError, TypeError):
                    pass
            
            bounce_rate = self.extract_percentage(traffic_metrics.get('bounce_rate', ''))
            
            # 解析域名日期
            domain_creation_date = self.parse_date(domain_info.get('domain_creation', ''))
            domain_expiration_date = self.parse_date(domain_info.get('domain_expiration', ''))
            domain_last_changed = self.parse_date(domain_info.get('last_changed', ''))
            
            # 解析域名年龄
            domain_age_years = None
            if domain_info.get('domain_age'):
                try:
                    age_str = domain_info['domain_age']
                    age_match = re.search(r'([0-9.]+)', age_str)
                    if age_match:
                        domain_age_years = Decimal(age_match.group(1))
                except (ValueError, TypeError):
                    pass
            
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO traffic_site_overview (
                        tool_id, domain, global_rank, country_rank, total_visits, 
                        total_visits_raw, visits_change_percent, avg_duration_seconds, 
                        avg_duration_raw, pages_per_visit, bounce_rate, 
                        domain_creation_date, domain_expiration_date, domain_last_changed, 
                        domain_age_years, stat_date, data_source, source_url, extraction_method
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19)
                    ON CONFLICT (tool_id, stat_date) 
                    DO UPDATE SET
                        global_rank = EXCLUDED.global_rank,
                        country_rank = EXCLUDED.country_rank,
                        total_visits = EXCLUDED.total_visits,
                        total_visits_raw = EXCLUDED.total_visits_raw,
                        visits_change_percent = EXCLUDED.visits_change_percent,
                        avg_duration_seconds = EXCLUDED.avg_duration_seconds,
                        avg_duration_raw = EXCLUDED.avg_duration_raw,
                        pages_per_visit = EXCLUDED.pages_per_visit,
                        bounce_rate = EXCLUDED.bounce_rate,
                        domain_creation_date = EXCLUDED.domain_creation_date,
                        domain_expiration_date = EXCLUDED.domain_expiration_date,
                        domain_last_changed = EXCLUDED.domain_last_changed,
                        domain_age_years = EXCLUDED.domain_age_years,
                        updated_at = CURRENT_TIMESTAMP
                """, 
                tool_id, data.get('domain', ''), global_rank, country_rank, total_visits,
                traffic_metrics.get('total_visits', ''), visits_change_percent, avg_duration_seconds,
                traffic_metrics.get('avg_duration', ''), pages_per_visit, bounce_rate,
                domain_creation_date, domain_expiration_date, domain_last_changed,
                domain_age_years, stat_date, 'traffic.cv', 
                f"https://traffic.cv/{data.get('domain', '')}", 'HTML parsing with BeautifulSoup'
                )
            
            logger.info(f"网站概览数据已保存: {tool_id}")
            
        except Exception as e:
            logger.error(f"保存网站概览数据失败: {e}")
            raise
    
    async def save_monthly_trends(self, tool_id: str, visits_data: List[Dict[str, str]], stat_date: date):
        """保存月度流量趋势数据"""
        try:
            async with self.db_pool.acquire() as conn:
                for visit_item in visits_data:
                    period = visit_item.get('period', '')
                    visits_raw = visit_item.get('visits', '')
                    visits = self.convert_visits_to_number(visits_raw)
                    
                    # 转换period为标准日期格式
                    try:
                        year_month = datetime.strptime(f"{period}-01", "%Y-%m-%d").date()
                    except ValueError:
                        logger.warning(f"无法解析时间周期: {period}")
                        continue
                    
                    await conn.execute("""
                        INSERT INTO traffic_monthly_trends (
                            tool_id, period, year_month, visits, visits_raw, data_source
                        ) VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (tool_id, period) 
                        DO UPDATE SET
                            visits = EXCLUDED.visits,
                            visits_raw = EXCLUDED.visits_raw,
                            updated_at = CURRENT_TIMESTAMP
                    """, tool_id, period, year_month, visits, visits_raw, 'traffic.cv')
                
                # 计算环比增长率
                await self.calculate_growth_rates(conn, tool_id)
                
            logger.info(f"月度趋势数据已保存: {tool_id}, {len(visits_data)} 条记录")
            
        except Exception as e:
            logger.error(f"保存月度趋势数据失败: {e}")
            raise
    
    async def calculate_growth_rates(self, conn, tool_id: str):
        """计算环比增长率"""
        try:
            # 获取按时间排序的访问量数据
            rows = await conn.fetch("""
                SELECT id, visits, year_month 
                FROM traffic_monthly_trends 
                WHERE tool_id = $1 
                ORDER BY year_month DESC
            """, tool_id)
            
            # 计算环比增长率
            for i in range(len(rows) - 1):
                current_visits = rows[i]['visits']
                previous_visits = rows[i + 1]['visits']
                
                if previous_visits > 0:
                    growth_rate = ((current_visits - previous_visits) / previous_visits) * 100
                    await conn.execute("""
                        UPDATE traffic_monthly_trends 
                        SET growth_rate = $1 
                        WHERE id = $2
                    """, Decimal(str(round(growth_rate, 4))), rows[i]['id'])
                    
        except Exception as e:
            logger.error(f"计算增长率失败: {e}")

    async def save_traffic_sources(self, tool_id: str, sources_data: Dict[str, str], stat_date: date):
        """保存流量来源数据"""
        try:
            async with self.db_pool.acquire() as conn:
                for source_type, percent_str in sources_data.items():
                    traffic_percent = self.extract_percentage(percent_str)
                    if traffic_percent is None:
                        continue

                    await conn.execute("""
                        INSERT INTO traffic_source_analysis (
                            tool_id, source_type, traffic_percent, traffic_percent_raw, stat_date, data_source
                        ) VALUES ($1, $2, $3, $4, $5, $6)
                        ON CONFLICT (tool_id, source_type, stat_date)
                        DO UPDATE SET
                            traffic_percent = EXCLUDED.traffic_percent,
                            traffic_percent_raw = EXCLUDED.traffic_percent_raw,
                            updated_at = CURRENT_TIMESTAMP
                    """, tool_id, source_type, traffic_percent, percent_str, stat_date, 'traffic.cv')

            logger.info(f"流量来源数据已保存: {tool_id}, {len(sources_data)} 条记录")

        except Exception as e:
            logger.error(f"保存流量来源数据失败: {e}")
            raise

    async def save_region_distribution(self, tool_id: str, regions_data: List[Dict[str, str]], stat_date: date):
        """保存地区流量分布数据"""
        try:
            async with self.db_pool.acquire() as conn:
                for region_item in regions_data:
                    region_name = region_item.get('region', '')
                    percent_str = region_item.get('percentage', '')
                    traffic_percent = self.extract_percentage(percent_str)

                    if not region_name or traffic_percent is None:
                        continue

                    # 尝试获取国家代码（简单映射）
                    country_code = self.get_country_code(region_name)

                    await conn.execute("""
                        INSERT INTO traffic_region_distribution (
                            tool_id, region_name, country_code, traffic_percent,
                            traffic_percent_raw, stat_date, data_source
                        ) VALUES ($1, $2, $3, $4, $5, $6, $7)
                        ON CONFLICT (tool_id, region_name, stat_date)
                        DO UPDATE SET
                            traffic_percent = EXCLUDED.traffic_percent,
                            traffic_percent_raw = EXCLUDED.traffic_percent_raw,
                            country_code = EXCLUDED.country_code,
                            updated_at = CURRENT_TIMESTAMP
                    """, tool_id, region_name, country_code, traffic_percent, percent_str, stat_date, 'traffic.cv')

            logger.info(f"地区分布数据已保存: {tool_id}, {len(regions_data)} 条记录")

        except Exception as e:
            logger.error(f"保存地区分布数据失败: {e}")
            raise

    async def save_keyword_analysis(self, tool_id: str, keywords_data: List[Dict[str, str]], stat_date: date):
        """保存关键词分析数据"""
        try:
            async with self.db_pool.acquire() as conn:
                for keyword_item in keywords_data:
                    keyword = keyword_item.get('keyword', '')
                    traffic_raw = keyword_item.get('traffic', '')
                    volume_raw = keyword_item.get('volume', '')
                    cpc_str = keyword_item.get('cpc', '')

                    if not keyword:
                        continue

                    # 转换流量数据
                    estimated_visits = self.convert_visits_to_number(traffic_raw)
                    search_volume = self.convert_visits_to_number(volume_raw)

                    # 计算流量占比（需要总访问量）
                    traffic_percent = None

                    await conn.execute("""
                        INSERT INTO traffic_keyword_analysis (
                            tool_id, keyword, keyword_type, traffic_percent, traffic_percent_raw,
                            estimated_visits, search_volume, stat_date, data_source
                        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                        ON CONFLICT (tool_id, keyword, keyword_type, stat_date)
                        DO UPDATE SET
                            traffic_percent = EXCLUDED.traffic_percent,
                            traffic_percent_raw = EXCLUDED.traffic_percent_raw,
                            estimated_visits = EXCLUDED.estimated_visits,
                            search_volume = EXCLUDED.search_volume,
                            updated_at = CURRENT_TIMESTAMP
                    """, tool_id, keyword, 'organic', traffic_percent, traffic_raw,
                         estimated_visits, search_volume, stat_date, 'traffic.cv')

            logger.info(f"关键词分析数据已保存: {tool_id}, {len(keywords_data)} 条记录")

        except Exception as e:
            logger.error(f"保存关键词分析数据失败: {e}")
            raise

    async def save_whois_info(self, tool_id: str, domain: str, whois_data: Dict[str, Any], raw_whois: Dict[str, Any]):
        """保存WHOIS信息"""
        try:
            async with self.db_pool.acquire() as conn:
                # 解析日期 - 处理多种格式
                creation_date = self.parse_whois_date(whois_data.get('created_date', ''))
                expiration_date = self.parse_whois_date(whois_data.get('expires_date', ''))
                updated_date = self.parse_whois_date(whois_data.get('updated_date', ''))

                # 解析名称服务器
                name_servers = None
                name_servers_text = whois_data.get('name_servers', '')
                if name_servers_text:
                    # 处理连续文本格式，如 "HASSAN.NS.CLOUDFLARE.COMSAVANNA.NS.CLOUDFLARE.COM"
                    # 按大写字母分割
                    servers = re.findall(r'[A-Z][A-Z0-9.-]*\.(?:COM|NET|ORG|NS\.CLOUDFLARE\.COM)', name_servers_text)
                    if servers:
                        name_servers = servers
                    else:
                        # 如果没有匹配到，尝试按空格或逗号分割
                        name_servers = [ns.strip() for ns in re.split(r'[,\s]+', name_servers_text) if ns.strip()]

                # 处理可能过长的字符串字段
                registrar = whois_data.get('registrar', '')

                # 如果registrar为空，尝试从domain字段中提取（这个字段包含了所有信息）
                if not registrar and whois_data.get('domain'):
                    domain_text = whois_data.get('domain', '')
                    # 从类似 "StatusclientID292Created Date..." 的文本中提取注册商
                    # 实际上这个字段名有误导性，它包含了registrar信息
                    registrar_match = re.search(r'([A-Za-z\s.]+?)(?=ID\d+|Created Date)', domain_text)
                    if registrar_match:
                        registrar = registrar_match.group(1).strip()
                        logger.info(f"从domain字段提取到注册商: {registrar}")

                if registrar and len(registrar) > 255:
                    registrar = registrar[:252] + '...'  # 截断并添加省略号

                domain_status = whois_data.get('domain_status', '')
                if domain_status and len(domain_status) > 500:
                    domain_status = domain_status[:497] + '...'

                dnssec_status = whois_data.get('dnssec', '')
                if dnssec_status and len(dnssec_status) > 50:
                    dnssec_status = dnssec_status[:47] + '...'

                # 保存原始WHOIS文本
                whois_raw_text = None
                if raw_whois and raw_whois.get('raw_text'):
                    whois_raw_text = raw_whois['raw_text']

                await conn.execute("""
                    INSERT INTO traffic_domain_whois (
                        tool_id, domain, registrar, creation_date, expiration_date,
                        updated_date, domain_status, name_servers, dnssec_status,
                        whois_raw_text, whois_json, data_source, extracted_at
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
                    ON CONFLICT (tool_id, domain)
                    DO UPDATE SET
                        registrar = EXCLUDED.registrar,
                        creation_date = EXCLUDED.creation_date,
                        expiration_date = EXCLUDED.expiration_date,
                        updated_date = EXCLUDED.updated_date,
                        domain_status = EXCLUDED.domain_status,
                        name_servers = EXCLUDED.name_servers,
                        dnssec_status = EXCLUDED.dnssec_status,
                        whois_raw_text = EXCLUDED.whois_raw_text,
                        whois_json = EXCLUDED.whois_json,
                        updated_at = CURRENT_TIMESTAMP
                """,
                tool_id, domain, registrar, creation_date, expiration_date,
                updated_date, domain_status, name_servers, dnssec_status,
                whois_raw_text, json.dumps(raw_whois) if raw_whois else None, 'traffic.cv', datetime.now()
                )

            logger.info(f"WHOIS信息已保存: {tool_id}")

        except Exception as e:
            logger.error(f"保存WHOIS信息失败: {e}")
            raise

    async def log_extraction(self, tool_id: str, domain: str, status: str,
                           data_points: int = 0, error_msg: str = None,
                           response_time_ms: int = None, raw_response: Dict = None):
        """记录数据提取日志"""
        try:
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO traffic_extraction_logs (
                        tool_id, domain, extraction_status, data_source, source_url,
                        extraction_method, response_time_ms, data_points_extracted,
                        error_message, raw_response, extracted_at
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
                """,
                tool_id, domain, status, 'traffic.cv', f"https://traffic.cv/{domain}",
                'HTML parsing with BeautifulSoup', response_time_ms, data_points,
                error_msg, json.dumps(raw_response) if raw_response else None, datetime.now()
                )

        except Exception as e:
            logger.error(f"记录提取日志失败: {e}")

    def get_country_code(self, country_name: str) -> Optional[str]:
        """获取国家代码（简单映射）"""
        country_mapping = {
            'United States': 'USA',
            'China': 'CHN',
            'India': 'IND',
            'Korea, Republic of': 'KOR',
            'Brazil': 'BRA',
            'Japan': 'JPN',
            'Germany': 'DEU',
            'United Kingdom': 'GBR',
            'France': 'FRA',
            'Canada': 'CAN'
        }
        return country_mapping.get(country_name)

    async def process_domain_traffic(self, domain: str) -> Dict[str, Any]:
        """处理指定域名的流量数据"""
        start_time = datetime.now()

        try:
            logger.info(f"开始处理域名流量数据: {domain}")

            # 获取流量数据
            result = self.spider.get_domain_data(domain)

            if not result.get('success', False):
                await self.log_extraction(
                    domain.replace('.', '_'), domain, 'failed',
                    error_msg="Spider failed to get data"
                )
                return result

            # 获取或创建tool_id
            tool_id = await self.get_or_create_tool_id(domain)
            if not tool_id:
                logger.error(f"无法获取tool_id for domain: {domain}")
                return {"success": False, "error": "Cannot get tool_id"}

            data = result.get('data', {})
            stat_date = date.today()
            data_points = 0

            # 保存网站概览数据
            if data.get('basic_info') or data.get('traffic_metrics') or data.get('domain_info'):
                await self.save_site_overview(tool_id, data, stat_date)
                data_points += 1

            # 保存月度趋势数据
            visits_over_time = data.get('visits_over_time', [])
            if visits_over_time:
                await self.save_monthly_trends(tool_id, visits_over_time, stat_date)
                data_points += len(visits_over_time)

            # 保存流量来源数据
            traffic_sources = data.get('traffic_sources', {})
            if traffic_sources:
                await self.save_traffic_sources(tool_id, traffic_sources, stat_date)
                data_points += len(traffic_sources)

            # 保存地区分布数据
            top_regions = data.get('top_regions', [])
            if top_regions:
                await self.save_region_distribution(tool_id, top_regions, stat_date)
                data_points += len(top_regions)

            # 保存关键词分析数据
            top_keywords = data.get('top_keywords', [])
            if top_keywords:
                await self.save_keyword_analysis(tool_id, top_keywords, stat_date)
                data_points += len(top_keywords)

            # 保存WHOIS信息
            whois_info = data.get('whois_info', {})
            raw_whois = data.get('raw_whois', {})
            if whois_info or raw_whois:
                await self.save_whois_info(tool_id, domain, whois_info, raw_whois)
                data_points += 1

            # 记录成功日志
            end_time = datetime.now()
            response_time_ms = int((end_time - start_time).total_seconds() * 1000)

            await self.log_extraction(
                tool_id, domain, 'success',
                data_points=data_points,
                response_time_ms=response_time_ms,
                raw_response=result
            )

            logger.info(f"域名 {domain} 流量数据处理完成，保存了 {data_points} 个数据点")

            return {
                "success": True,
                "domain": domain,
                "tool_id": tool_id,
                "data_points": data_points,
                "response_time_ms": response_time_ms,
                "message": f"Successfully processed {data_points} data points"
            }

        except Exception as e:
            # 记录失败日志
            end_time = datetime.now()
            response_time_ms = int((end_time - start_time).total_seconds() * 1000)

            await self.log_extraction(
                domain.replace('.', '_'), domain, 'failed',
                error_msg=str(e),
                response_time_ms=response_time_ms
            )

            logger.error(f"处理域名 {domain} 流量数据失败: {e}")
            return {
                "success": False,
                "domain": domain,
                "error": str(e),
                "response_time_ms": response_time_ms
            }

    async def batch_process_domains(self, domains: List[str]) -> List[Dict[str, Any]]:
        """批量处理多个域名的流量数据"""
        results = []

        logger.info(f"开始批量处理 {len(domains)} 个域名")

        for i, domain in enumerate(domains, 1):
            logger.info(f"处理进度: {i}/{len(domains)} - {domain}")

            try:
                result = await self.process_domain_traffic(domain)
                results.append(result)

                # 添加延迟避免请求过于频繁
                if i < len(domains):
                    await asyncio.sleep(2)

            except Exception as e:
                logger.error(f"批量处理域名 {domain} 失败: {e}")
                results.append({
                    "success": False,
                    "domain": domain,
                    "error": str(e)
                })

        successful = sum(1 for r in results if r.get('success', False))
        logger.info(f"批量处理完成: {successful}/{len(domains)} 成功")

        return results

    async def get_traffic_summary(self, tool_id: str) -> Dict[str, Any]:
        """获取流量数据摘要"""
        try:
            async with self.db_pool.acquire() as conn:
                # 获取最新的概览数据
                overview = await conn.fetchrow("""
                    SELECT * FROM traffic_site_overview
                    WHERE tool_id = $1
                    ORDER BY stat_date DESC
                    LIMIT 1
                """, tool_id)

                # 获取最近3个月的趋势数据
                trends = await conn.fetch("""
                    SELECT period, visits, visits_raw, growth_rate
                    FROM traffic_monthly_trends
                    WHERE tool_id = $1
                    ORDER BY year_month DESC
                    LIMIT 3
                """, tool_id)

                # 获取流量来源数据
                sources = await conn.fetch("""
                    SELECT source_type, traffic_percent, traffic_percent_raw
                    FROM traffic_source_analysis
                    WHERE tool_id = $1
                    ORDER BY traffic_percent DESC
                """, tool_id)

                # 获取热门地区数据
                regions = await conn.fetch("""
                    SELECT region_name, traffic_percent, traffic_percent_raw
                    FROM traffic_region_distribution
                    WHERE tool_id = $1
                    ORDER BY traffic_percent DESC
                    LIMIT 5
                """, tool_id)

                # 获取热门关键词数据
                keywords = await conn.fetch("""
                    SELECT keyword, estimated_visits, search_volume
                    FROM traffic_keyword_analysis
                    WHERE tool_id = $1
                    ORDER BY estimated_visits DESC
                    LIMIT 5
                """, tool_id)

                return {
                    "tool_id": tool_id,
                    "overview": dict(overview) if overview else None,
                    "trends": [dict(row) for row in trends],
                    "sources": [dict(row) for row in sources],
                    "regions": [dict(row) for row in regions],
                    "keywords": [dict(row) for row in keywords]
                }

        except Exception as e:
            logger.error(f"获取流量摘要失败: {e}")
            return {"error": str(e)}
