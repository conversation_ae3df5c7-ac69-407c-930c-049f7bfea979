#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工具流量数据同步脚本
将tools表中的域名解析查询流量数据，并入库到相关流量表中
解决多个tool_id对应同一domain的问题
"""

import asyncio
import asyncpg
import json
from datetime import datetime, date
from typing import Dict, List, Optional, Any, Tuple
import logging
from .traffic_data_manager import TrafficDataManager, DatabaseConfig

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ToolsTrafficSyncer:
    """工具流量数据同步器"""
    
    def __init__(self, db_config: DatabaseConfig):
        self.db_config = db_config
        self.db_pool = None
        self.traffic_manager = TrafficDataManager(db_config)
    
    async def init_db_pool(self):
        """初始化数据库连接池"""
        try:
            self.db_pool = await asyncpg.create_pool(
                host=self.db_config.host,
                port=self.db_config.port,
                database=self.db_config.database,
                user=self.db_config.user,
                password=self.db_config.password,
                min_size=2,
                max_size=10
            )
            await self.traffic_manager.init_db_pool()
            logger.info("数据库连接池初始化成功")
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise
    
    async def close_db_pool(self):
        """关闭数据库连接池"""
        if self.db_pool:
            await self.db_pool.close()
        await self.traffic_manager.close_db_pool()
        logger.info("数据库连接池已关闭")
    
    async def get_domain_tool_mapping(self) -> Dict[str, List[Dict[str, Any]]]:
        """获取域名到工具的映射关系"""
        async with self.db_pool.acquire() as conn:
            rows = await conn.fetch("""
                SELECT tool_id, domain, url, created_at
                FROM tools 
                WHERE domain IS NOT NULL AND domain != ''
                ORDER BY domain, created_at ASC
            """)
            
            domain_mapping = {}
            for row in rows:
                domain = row['domain']
                if domain not in domain_mapping:
                    domain_mapping[domain] = []
                
                domain_mapping[domain].append({
                    'tool_id': row['tool_id'],
                    'domain': row['domain'],
                    'url': row['url'],
                    'created_at': row['created_at']
                })
            
            return domain_mapping
    
    def select_primary_tool(self, tools: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        为同一域名选择主要工具
        策略：
        1. 优先选择最早创建的工具（created_at最小）
        2. 如果创建时间相同，选择tool_id字典序最小的
        3. 避免选择明显的扩展程序或子产品
        """
        if len(tools) == 1:
            return tools[0]
        
        # 过滤掉明显的扩展程序
        non_extension_tools = []
        extension_keywords = ['extension', 'chrome', 'browser', 'addon', 'plugin']
        
        for tool in tools:
            tool_id_lower = tool['tool_id'].lower()
            is_extension = any(keyword in tool_id_lower for keyword in extension_keywords)
            if not is_extension:
                non_extension_tools.append(tool)
        
        # 如果过滤后还有工具，使用过滤后的列表
        candidate_tools = non_extension_tools if non_extension_tools else tools
        
        # 按创建时间排序，然后按tool_id排序
        candidate_tools.sort(key=lambda x: (x['created_at'], x['tool_id']))
        
        primary_tool = candidate_tools[0]
        logger.info(f"域名 {primary_tool['domain']} 选择主要工具: {primary_tool['tool_id']} (共{len(tools)}个工具)")
        
        return primary_tool
    
    async def check_existing_traffic_data(self, domain: str) -> Optional[str]:
        """检查域名是否已有流量数据"""
        async with self.db_pool.acquire() as conn:
            # 检查是否已有该域名的流量数据
            existing_tool_id = await conn.fetchval("""
                SELECT tool_id FROM traffic_site_overview 
                WHERE domain = $1 
                LIMIT 1
            """, domain)
            
            return existing_tool_id
    
    async def create_domain_tool_mapping(self, domain: str, primary_tool_id: str, 
                                       related_tools: List[str]) -> bool:
        """创建域名-工具映射记录"""
        try:
            async with self.db_pool.acquire() as conn:
                # 创建或更新映射表（如果不存在则创建）
                await conn.execute("""
                    CREATE TABLE IF NOT EXISTS domain_tool_mapping (
                        id SERIAL PRIMARY KEY,
                        domain VARCHAR(255) NOT NULL,
                        primary_tool_id VARCHAR(50) NOT NULL,
                        related_tool_ids TEXT[] NOT NULL,
                        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                        CONSTRAINT uk_domain_mapping UNIQUE (domain)
                    )
                """)
                
                # 插入或更新映射关系
                await conn.execute("""
                    INSERT INTO domain_tool_mapping (domain, primary_tool_id, related_tool_ids)
                    VALUES ($1, $2, $3)
                    ON CONFLICT (domain) 
                    DO UPDATE SET
                        primary_tool_id = EXCLUDED.primary_tool_id,
                        related_tool_ids = EXCLUDED.related_tool_ids,
                        updated_at = CURRENT_TIMESTAMP
                """, domain, primary_tool_id, related_tools)
                
                return True
        except Exception as e:
            logger.error(f"创建域名映射失败 {domain}: {e}")
            return False
    
    async def sync_traffic_data_for_domain(self, domain: str, primary_tool_id: str) -> Dict[str, Any]:
        """为指定域名同步流量数据"""
        try:
            logger.info(f"开始同步域名 {domain} 的流量数据 (主工具: {primary_tool_id})")
            
            # 检查是否已有流量数据
            existing_tool_id = await self.check_existing_traffic_data(domain)
            if existing_tool_id and existing_tool_id != primary_tool_id:
                logger.warning(f"域名 {domain} 已有流量数据，关联工具: {existing_tool_id}")
                # 更新现有数据的tool_id为主要工具ID
                await self.update_existing_traffic_tool_id(domain, existing_tool_id, primary_tool_id)
            
            # 使用流量管理器获取数据
            result = await self.traffic_manager.process_domain_traffic(domain)
            
            if result.get('success', False):
                # 确保流量数据使用正确的tool_id
                await self.ensure_correct_tool_id(domain, primary_tool_id)
                
                return {
                    'success': True,
                    'domain': domain,
                    'primary_tool_id': primary_tool_id,
                    'data_points': result.get('data_points', 0),
                    'response_time_ms': result.get('response_time_ms', 0)
                }
            else:
                return {
                    'success': False,
                    'domain': domain,
                    'primary_tool_id': primary_tool_id,
                    'error': result.get('error', 'Unknown error')
                }
                
        except Exception as e:
            logger.error(f"同步域名 {domain} 流量数据失败: {e}")
            return {
                'success': False,
                'domain': domain,
                'primary_tool_id': primary_tool_id,
                'error': str(e)
            }
    
    async def update_existing_traffic_tool_id(self, domain: str, old_tool_id: str, new_tool_id: str):
        """更新现有流量数据的tool_id"""
        try:
            async with self.db_pool.acquire() as conn:
                # 更新所有流量表中的tool_id
                tables_to_update = [
                    'traffic_site_overview',
                    'traffic_monthly_trends',
                    'traffic_source_analysis',
                    'traffic_region_distribution',
                    'traffic_keyword_analysis',
                    'traffic_domain_whois',
                    'traffic_extraction_logs'
                ]
                
                for table in tables_to_update:
                    result = await conn.execute(f"""
                        UPDATE {table} 
                        SET tool_id = $1, updated_at = CURRENT_TIMESTAMP
                        WHERE tool_id = $2
                    """, new_tool_id, old_tool_id)
                    
                    if result != 'UPDATE 0':
                        logger.info(f"更新表 {table}: {old_tool_id} -> {new_tool_id}")
                
        except Exception as e:
            logger.error(f"更新tool_id失败 {domain}: {e}")
    
    async def ensure_correct_tool_id(self, domain: str, correct_tool_id: str):
        """确保流量数据使用正确的tool_id"""
        try:
            async with self.db_pool.acquire() as conn:
                # 检查并修正tool_id
                current_tool_id = await conn.fetchval("""
                    SELECT tool_id FROM traffic_site_overview 
                    WHERE domain = $1 
                    LIMIT 1
                """, domain)
                
                if current_tool_id and current_tool_id != correct_tool_id:
                    await self.update_existing_traffic_tool_id(domain, current_tool_id, correct_tool_id)
                    
        except Exception as e:
            logger.error(f"确保正确tool_id失败 {domain}: {e}")
    
    async def get_sync_statistics(self) -> Dict[str, Any]:
        """获取同步统计信息"""
        async with self.db_pool.acquire() as conn:
            # 工具统计
            total_tools = await conn.fetchval("SELECT COUNT(*) FROM tools WHERE domain IS NOT NULL")
            unique_domains = await conn.fetchval("SELECT COUNT(DISTINCT domain) FROM tools WHERE domain IS NOT NULL")
            
            # 流量数据统计
            traffic_domains = await conn.fetchval("SELECT COUNT(DISTINCT domain) FROM traffic_site_overview")
            
            # 映射统计
            try:
                mapped_domains = await conn.fetchval("SELECT COUNT(*) FROM domain_tool_mapping")
            except:
                mapped_domains = 0
            
            # 重复域名统计
            duplicate_domains = await conn.fetch("""
                SELECT domain, COUNT(*) as tool_count
                FROM tools 
                WHERE domain IS NOT NULL AND domain != ''
                GROUP BY domain 
                HAVING COUNT(*) > 1
                ORDER BY tool_count DESC
                LIMIT 5
            """)
            
            return {
                'total_tools': total_tools,
                'unique_domains': unique_domains,
                'traffic_domains': traffic_domains,
                'mapped_domains': mapped_domains,
                'coverage_percentage': round((traffic_domains / unique_domains) * 100, 2) if unique_domains > 0 else 0,
                'duplicate_domains': [dict(row) for row in duplicate_domains]
            }
    
    async def sync_all_domains(self, limit: Optional[int] = None, 
                             skip_existing: bool = True) -> Dict[str, Any]:
        """同步所有域名的流量数据"""
        logger.info("开始同步所有域名的流量数据...")
        
        # 获取域名-工具映射
        domain_mapping = await self.get_domain_tool_mapping()
        
        # 统计信息
        stats = {
            'total_domains': len(domain_mapping),
            'processed': 0,
            'successful': 0,
            'failed': 0,
            'skipped': 0,
            'errors': []
        }
        
        # 处理域名（按域名排序确保一致性）
        sorted_domains = sorted(domain_mapping.keys())
        if limit:
            sorted_domains = sorted_domains[:limit]
        
        for domain in sorted_domains:
            tools = domain_mapping[domain]
            stats['processed'] += 1
            
            try:
                # 选择主要工具
                primary_tool = self.select_primary_tool(tools)
                primary_tool_id = primary_tool['tool_id']
                related_tool_ids = [tool['tool_id'] for tool in tools]
                
                # 创建映射关系
                await self.create_domain_tool_mapping(domain, primary_tool_id, related_tool_ids)
                
                # 检查是否跳过已有数据
                if skip_existing:
                    existing_tool_id = await self.check_existing_traffic_data(domain)
                    if existing_tool_id:
                        logger.info(f"跳过已有流量数据的域名: {domain} (工具: {existing_tool_id})")
                        stats['skipped'] += 1
                        continue
                
                # 同步流量数据
                result = await self.sync_traffic_data_for_domain(domain, primary_tool_id)
                
                if result['success']:
                    stats['successful'] += 1
                    logger.info(f"✅ 同步成功: {domain} -> {primary_tool_id} ({result.get('data_points', 0)} 数据点)")
                else:
                    stats['failed'] += 1
                    error_msg = f"同步失败: {domain} -> {result.get('error', 'Unknown error')}"
                    logger.error(error_msg)
                    stats['errors'].append(error_msg)
                
                # 添加延迟避免请求过于频繁
                await asyncio.sleep(3)
                
            except Exception as e:
                stats['failed'] += 1
                error_msg = f"处理域名 {domain} 时发生异常: {e}"
                logger.error(error_msg)
                stats['errors'].append(error_msg)
        
        logger.info(f"同步完成: {stats['successful']}/{stats['processed']} 成功")
        return stats
